import 'package:flutter/material.dart';
import 'package:fit_4_force/core/theme/app_theme.dart';

/// A utility class for consistent UI components and styling
class AppUI {
  // Prevent instantiation
  AppUI._();
  
  /// Standard padding values
  static const EdgeInsets paddingXXSmall = EdgeInsets.all(AppTheme.spacingXXSmall);
  static const EdgeInsets paddingXSmall = EdgeInsets.all(AppTheme.spacingXSmall);
  static const EdgeInsets paddingSmall = EdgeInsets.all(AppTheme.spacingSmall);
  static const EdgeInsets paddingMedium = EdgeInsets.all(AppTheme.spacingMedium);
  static const EdgeInsets paddingLarge = EdgeInsets.all(AppTheme.spacingLarge);
  static const EdgeInsets paddingXLarge = EdgeInsets.all(AppTheme.spacingXLarge);
  static const EdgeInsets paddingXXLarge = EdgeInsets.all(AppTheme.spacingXXLarge);
  
  /// Standard horizontal padding values
  static const EdgeInsets paddingHorizontalXXSmall = EdgeInsets.symmetric(horizontal: AppTheme.spacingXXSmall);
  static const EdgeInsets paddingHorizontalXSmall = EdgeInsets.symmetric(horizontal: AppTheme.spacingXSmall);
  static const EdgeInsets paddingHorizontalSmall = EdgeInsets.symmetric(horizontal: AppTheme.spacingSmall);
  static const EdgeInsets paddingHorizontalMedium = EdgeInsets.symmetric(horizontal: AppTheme.spacingMedium);
  static const EdgeInsets paddingHorizontalLarge = EdgeInsets.symmetric(horizontal: AppTheme.spacingLarge);
  static const EdgeInsets paddingHorizontalXLarge = EdgeInsets.symmetric(horizontal: AppTheme.spacingXLarge);
  static const EdgeInsets paddingHorizontalXXLarge = EdgeInsets.symmetric(horizontal: AppTheme.spacingXXLarge);
  
  /// Standard vertical padding values
  static const EdgeInsets paddingVerticalXXSmall = EdgeInsets.symmetric(vertical: AppTheme.spacingXXSmall);
  static const EdgeInsets paddingVerticalXSmall = EdgeInsets.symmetric(vertical: AppTheme.spacingXSmall);
  static const EdgeInsets paddingVerticalSmall = EdgeInsets.symmetric(vertical: AppTheme.spacingSmall);
  static const EdgeInsets paddingVerticalMedium = EdgeInsets.symmetric(vertical: AppTheme.spacingMedium);
  static const EdgeInsets paddingVerticalLarge = EdgeInsets.symmetric(vertical: AppTheme.spacingLarge);
  static const EdgeInsets paddingVerticalXLarge = EdgeInsets.symmetric(vertical: AppTheme.spacingXLarge);
  static const EdgeInsets paddingVerticalXXLarge = EdgeInsets.symmetric(vertical: AppTheme.spacingXXLarge);
  
  /// Standard border radius values
  static final BorderRadius borderRadiusSmall = BorderRadius.circular(AppTheme.borderRadiusSmall);
  static final BorderRadius borderRadiusMedium = BorderRadius.circular(AppTheme.borderRadiusMedium);
  static final BorderRadius borderRadiusLarge = BorderRadius.circular(AppTheme.borderRadiusLarge);
  static final BorderRadius borderRadiusXLarge = BorderRadius.circular(AppTheme.borderRadiusXLarge);
  static final BorderRadius borderRadiusXXLarge = BorderRadius.circular(AppTheme.borderRadiusXXLarge);
  static final BorderRadius borderRadiusCircular = BorderRadius.circular(AppTheme.borderRadiusCircular);
  
  /// Standard box shadows
  static List<BoxShadow> get shadowSmall => [
    BoxShadow(
      color: Colors.black.withValues(alpha: 0.05 * 255),
      blurRadius: 4,
      offset: const Offset(0, 2),
      spreadRadius: 0,
    ),
  ];
  
  static List<BoxShadow> get shadowMedium => [
    BoxShadow(
      color: Colors.black.withValues(alpha: 0.1 * 255),
      blurRadius: 8,
      offset: const Offset(0, 4),
      spreadRadius: 0,
    ),
  ];
  
  static List<BoxShadow> get shadowLarge => [
    BoxShadow(
      color: Colors.black.withValues(alpha: 0.15 * 255),
      blurRadius: 16,
      offset: const Offset(0, 8),
      spreadRadius: 0,
    ),
  ];
  
  /// Standard card decoration
  static BoxDecoration cardDecoration({
    Color? color,
    BorderRadius? borderRadius,
    List<BoxShadow>? boxShadow,
    Border? border,
    Gradient? gradient,
  }) {
    return BoxDecoration(
      color: color ?? Colors.white,
      borderRadius: borderRadius ?? borderRadiusXLarge,
      boxShadow: boxShadow ?? shadowMedium,
      border: border,
      gradient: gradient,
    );
  }
  
  /// Standard gradient decoration
  static BoxDecoration gradientDecoration({
    required List<Color> colors,
    AlignmentGeometry begin = Alignment.topLeft,
    AlignmentGeometry end = Alignment.bottomRight,
    BorderRadius? borderRadius,
    List<BoxShadow>? boxShadow,
    Border? border,
  }) {
    return BoxDecoration(
      gradient: LinearGradient(
        colors: colors,
        begin: begin,
        end: end,
      ),
      borderRadius: borderRadius ?? borderRadiusXLarge,
      boxShadow: boxShadow ?? shadowMedium,
      border: border,
    );
  }
  
  /// Standard button style
  static ButtonStyle buttonStyle({
    Color? backgroundColor,
    Color? foregroundColor,
    BorderRadius? borderRadius,
    EdgeInsetsGeometry? padding,
    double? elevation,
  }) {
    return ElevatedButton.styleFrom(
      backgroundColor: backgroundColor ?? AppTheme.primaryColor,
      foregroundColor: foregroundColor ?? Colors.white,
      elevation: elevation ?? AppTheme.elevationSmall,
      padding: padding ?? paddingHorizontalMedium.add(paddingVerticalSmall),
      shape: RoundedRectangleBorder(
        borderRadius: borderRadius ?? borderRadiusLarge,
      ),
    );
  }
  
  /// Standard text button style
  static ButtonStyle textButtonStyle({
    Color? foregroundColor,
    EdgeInsetsGeometry? padding,
  }) {
    return TextButton.styleFrom(
      foregroundColor: foregroundColor ?? AppTheme.primaryColor,
      padding: padding ?? paddingHorizontalSmall.add(paddingVerticalXSmall),
    );
  }
  
  /// Standard outlined button style
  static ButtonStyle outlinedButtonStyle({
    Color? foregroundColor,
    Color? borderColor,
    BorderRadius? borderRadius,
    EdgeInsetsGeometry? padding,
  }) {
    return OutlinedButton.styleFrom(
      foregroundColor: foregroundColor ?? AppTheme.primaryColor,
      side: BorderSide(color: borderColor ?? AppTheme.primaryColor),
      padding: padding ?? paddingHorizontalMedium.add(paddingVerticalSmall),
      shape: RoundedRectangleBorder(
        borderRadius: borderRadius ?? borderRadiusLarge,
      ),
    );
  }
  
  /// Standard input decoration
  static InputDecoration inputDecoration({
    String? labelText,
    String? hintText,
    Widget? prefixIcon,
    Widget? suffixIcon,
    BorderRadius? borderRadius,
    Color? fillColor,
    bool filled = true,
  }) {
    return InputDecoration(
      labelText: labelText,
      hintText: hintText,
      prefixIcon: prefixIcon,
      suffixIcon: suffixIcon,
      filled: filled,
      fillColor: fillColor ?? Colors.white,
      contentPadding: paddingMedium,
      border: OutlineInputBorder(
        borderRadius: borderRadius ?? borderRadiusLarge,
        borderSide: const BorderSide(color: Colors.grey),
      ),
      enabledBorder: OutlineInputBorder(
        borderRadius: borderRadius ?? borderRadiusLarge,
        borderSide: const BorderSide(color: Colors.grey),
      ),
      focusedBorder: OutlineInputBorder(
        borderRadius: borderRadius ?? borderRadiusLarge,
        borderSide: const BorderSide(color: AppTheme.primaryColor, width: 2),
      ),
      errorBorder: OutlineInputBorder(
        borderRadius: borderRadius ?? borderRadiusLarge,
        borderSide: const BorderSide(color: AppTheme.errorColor),
      ),
    );
  }
}
