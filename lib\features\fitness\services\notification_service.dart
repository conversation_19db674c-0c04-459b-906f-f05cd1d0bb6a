import 'package:fit_4_force/features/fitness/models/notification_model.dart';

/// Service for managing notifications
class NotificationService {
  // Singleton instance
  static final NotificationService _instance = NotificationService._internal();

  factory NotificationService() {
    return _instance;
  }

  NotificationService._internal();

  // Mock data for notifications
  final List<NotificationModel> _notifications = [
    NotificationModel(
      id: '1',
      title: 'Workout Reminder',
      message: 'Don\'t forget your scheduled Full Body Workout today!',
      timestamp: DateTime.now().subtract(const Duration(hours: 2)),
      type: NotificationType.reminder,
      relatedId: '1',
    ),
    NotificationModel(
      id: '2',
      title: 'Achievement Unlocked',
      message: 'Congratulations! You\'ve completed 5 workouts this week.',
      timestamp: DateTime.now().subtract(const Duration(days: 1)),
      type: NotificationType.achievement,
    ),
    NotificationModel(
      id: '3',
      title: 'New Workout Available',
      message: 'Check out the new HIIT workout in your recommended list.',
      timestamp: DateTime.now().subtract(const Duration(days: 2)),
      type: NotificationType.workout,
      relatedId: '5',
    ),
    NotificationModel(
      id: '4',
      title: 'Plan Progress',
      message: 'You\'re halfway through your Basic Training plan. Keep it up!',
      timestamp: DateTime.now().subtract(const Duration(days: 3)),
      type: NotificationType.plan,
      relatedId: '1',
    ),
    NotificationModel(
      id: '5',
      title: 'App Update',
      message: 'Fit4Force has been updated with new features. Check them out!',
      timestamp: DateTime.now().subtract(const Duration(days: 5)),
      type: NotificationType.general,
    ),
  ];

  // Get all notifications
  List<NotificationModel> getAllNotifications() {
    return List.from(_notifications);
  }

  // Get unread notifications
  List<NotificationModel> getUnreadNotifications() {
    return _notifications.where((notification) => !notification.isRead).toList();
  }

  // Get notification by ID
  NotificationModel? getNotificationById(String id) {
    try {
      return _notifications.firstWhere((notification) => notification.id == id);
    } catch (e) {
      return null;
    }
  }

  // Mark notification as read
  void markAsRead(String id) {
    final index = _notifications.indexWhere((notification) => notification.id == id);
    if (index != -1) {
      final notification = _notifications[index];
      _notifications[index] = notification.copyWith(isRead: true);
    }
  }

  // Mark all notifications as read
  void markAllAsRead() {
    for (var i = 0; i < _notifications.length; i++) {
      _notifications[i] = _notifications[i].copyWith(isRead: true);
    }
  }

  // Add notification
  void addNotification(NotificationModel notification) {
    _notifications.insert(0, notification);
  }

  // Delete notification
  void deleteNotification(String id) {
    _notifications.removeWhere((notification) => notification.id == id);
  }

  // Get unread count
  int getUnreadCount() {
    return _notifications.where((notification) => !notification.isRead).length;
  }
}
