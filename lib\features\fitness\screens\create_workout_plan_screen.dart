import 'package:flutter/material.dart';
import 'package:fit_4_force/core/theme/app_theme.dart';
import 'package:fit_4_force/features/fitness/models/workout_model.dart';
import 'package:fit_4_force/features/fitness/screens/workout_day_screen.dart';

class CreateWorkoutPlanScreen extends StatefulWidget {
  const CreateWorkoutPlanScreen({super.key});

  @override
  State<CreateWorkoutPlanScreen> createState() => _CreateWorkoutPlanScreenState();
}

class _CreateWorkoutPlanScreenState extends State<CreateWorkoutPlanScreen> {
  final _formKey = GlobalKey<FormState>();
  final _nameController = TextEditingController();
  final _descriptionController = TextEditingController();
  String _selectedLevel = 'Beginner';
  String _selectedDuration = '4 weeks';
  final List<String> _selectedExercises = [];
  final List<WorkoutModel> _workouts = [];
  int _currentStep = 0;
  
  final List<String> _levels = ['Beginner', 'Intermediate', 'Advanced'];
  final List<String> _durations = ['2 weeks', '4 weeks', '6 weeks', '8 weeks', '12 weeks'];
  
  // Mock exercise library
  final List<Map<String, dynamic>> _exerciseLibrary = [
    {
      'id': 'ex1',
      'name': 'Push-Ups',
      'category': 'Strength',
      'imageUrl': 'assets/images/exercises/pushups.png',
    },
    {
      'id': 'ex2',
      'name': 'Jumping Jacks',
      'category': 'Cardio',
      'imageUrl': 'assets/images/exercises/jumping_jacks.png',
    },
    {
      'id': 'ex3',
      'name': 'Squats',
      'category': 'Strength',
      'imageUrl': 'assets/images/exercises/squats.png',
    },
    {
      'id': 'ex4',
      'name': 'Plank',
      'category': 'Core',
      'imageUrl': 'assets/images/exercises/plank.png',
    },
    {
      'id': 'ex5',
      'name': 'Lunges',
      'category': 'Strength',
      'imageUrl': 'assets/images/exercises/lunges.png',
    },
    {
      'id': 'ex6',
      'name': 'Mountain Climbers',
      'category': 'Cardio',
      'imageUrl': 'assets/images/exercises/mountain_climbers.png',
    },
    {
      'id': 'ex7',
      'name': 'Crunches',
      'category': 'Core',
      'imageUrl': 'assets/images/exercises/crunches.png',
    },
    {
      'id': 'ex8',
      'name': 'Burpees',
      'category': 'Cardio',
      'imageUrl': 'assets/images/exercises/burpees.png',
    },
  ];
  
  @override
  void dispose() {
    _nameController.dispose();
    _descriptionController.dispose();
    super.dispose();
  }
  
  void _createWorkoutPlan() {
    if (_formKey.currentState!.validate()) {
      // In a real app, you would save this to your backend
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('Workout plan created successfully!'),
          backgroundColor: Colors.green,
        ),
      );
      
      // Navigate back to the custom workout screen
      Navigator.pop(context);
    }
  }
  
  void _addExercise(String exerciseId) {
    setState(() {
      if (_selectedExercises.contains(exerciseId)) {
        _selectedExercises.remove(exerciseId);
      } else {
        _selectedExercises.add(exerciseId);
      }
    });
  }
  
  void _generateWorkouts() {
    // In a real app, you would generate workouts based on the selected exercises
    // For now, we'll create a simple workout with the selected exercises
    
    final exercises = _selectedExercises.map((id) {
      final exercise = _exerciseLibrary.firstWhere((e) => e['id'] == id);
      return ExerciseModel(
        id: exercise['id'],
        name: exercise['name'],
        description: 'Description for ${exercise['name']}',
        imageUrl: exercise['imageUrl'],
        videoUrl: 'https://example.com/videos/${exercise['id']}.mp4',
        duration: 0,
        sets: 3,
        reps: 12,
        restTime: 30,
      );
    }).toList();
    
    final workout = WorkoutModel(
      id: 'workout_${DateTime.now().millisecondsSinceEpoch}',
      name: _nameController.text,
      description: _descriptionController.text,
      imageUrl: 'assets/images/workouts/custom.jpg',
      category: 'Custom',
      duration: 30,
      calories: 250,
      exercises: exercises,
      icon: Icons.fitness_center,
      color: Colors.blue,
    );
    
    setState(() {
      _workouts.add(workout);
      _currentStep = 2; // Move to the review step
    });
  }
  
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Create Workout Plan'),
      ),
      body: Stepper(
        currentStep: _currentStep,
        onStepContinue: () {
          if (_currentStep == 0) {
            if (_formKey.currentState!.validate()) {
              setState(() {
                _currentStep += 1;
              });
            }
          } else if (_currentStep == 1) {
            if (_selectedExercises.isNotEmpty) {
              _generateWorkouts();
            } else {
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(
                  content: Text('Please select at least one exercise'),
                  backgroundColor: Colors.red,
                ),
              );
            }
          } else if (_currentStep == 2) {
            _createWorkoutPlan();
          }
        },
        onStepCancel: () {
          if (_currentStep > 0) {
            setState(() {
              _currentStep -= 1;
            });
          } else {
            Navigator.pop(context);
          }
        },
        steps: [
          Step(
            title: const Text('Basic Information'),
            content: _buildBasicInfoStep(),
            isActive: _currentStep >= 0,
          ),
          Step(
            title: const Text('Select Exercises'),
            content: _buildSelectExercisesStep(),
            isActive: _currentStep >= 1,
          ),
          Step(
            title: const Text('Review Plan'),
            content: _buildReviewPlanStep(),
            isActive: _currentStep >= 2,
          ),
        ],
      ),
    );
  }
  
  Widget _buildBasicInfoStep() {
    return Form(
      key: _formKey,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          TextFormField(
            controller: _nameController,
            decoration: const InputDecoration(
              labelText: 'Plan Name',
              hintText: 'Enter a name for your workout plan',
              border: OutlineInputBorder(),
            ),
            validator: (value) {
              if (value == null || value.isEmpty) {
                return 'Please enter a name';
              }
              return null;
            },
          ),
          const SizedBox(height: 16),
          TextFormField(
            controller: _descriptionController,
            decoration: const InputDecoration(
              labelText: 'Description',
              hintText: 'Describe your workout plan',
              border: OutlineInputBorder(),
            ),
            maxLines: 3,
          ),
          const SizedBox(height: 16),
          DropdownButtonFormField<String>(
            decoration: const InputDecoration(
              labelText: 'Difficulty Level',
              border: OutlineInputBorder(),
            ),
            value: _selectedLevel,
            items: _levels.map((level) {
              return DropdownMenuItem(
                value: level,
                child: Text(level),
              );
            }).toList(),
            onChanged: (value) {
              setState(() {
                _selectedLevel = value!;
              });
            },
          ),
          const SizedBox(height: 16),
          DropdownButtonFormField<String>(
            decoration: const InputDecoration(
              labelText: 'Duration',
              border: OutlineInputBorder(),
            ),
            value: _selectedDuration,
            items: _durations.map((duration) {
              return DropdownMenuItem(
                value: duration,
                child: Text(duration),
              );
            }).toList(),
            onChanged: (value) {
              setState(() {
                _selectedDuration = value!;
              });
            },
          ),
        ],
      ),
    );
  }
  
  Widget _buildSelectExercisesStep() {
    // Group exercises by category
    final exercisesByCategory = <String, List<Map<String, dynamic>>>{};
    
    for (final exercise in _exerciseLibrary) {
      final category = exercise['category'] as String;
      if (!exercisesByCategory.containsKey(category)) {
        exercisesByCategory[category] = [];
      }
      exercisesByCategory[category]!.add(exercise);
    }
    
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Select exercises for your plan',
          style: Theme.of(context).textTheme.titleMedium,
        ),
        const SizedBox(height: 8),
        Text(
          'Selected: ${_selectedExercises.length} exercises',
          style: TextStyle(
            color: AppTheme.primaryColor,
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 16),
        ...exercisesByCategory.entries.map((entry) {
          return Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                entry.key,
                style: Theme.of(context).textTheme.titleMedium?.copyWith(
                  fontWeight: FontWeight.bold,
                ),
              ),
              const SizedBox(height: 8),
              GridView.builder(
                shrinkWrap: true,
                physics: const NeverScrollableScrollPhysics(),
                gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
                  crossAxisCount: 2,
                  childAspectRatio: 1.5,
                  crossAxisSpacing: 10,
                  mainAxisSpacing: 10,
                ),
                itemCount: entry.value.length,
                itemBuilder: (context, index) {
                  final exercise = entry.value[index];
                  final isSelected = _selectedExercises.contains(exercise['id']);
                  
                  return _buildExerciseCard(exercise, isSelected);
                },
              ),
              const SizedBox(height: 16),
            ],
          );
        }),
      ],
    );
  }
  
  Widget _buildExerciseCard(Map<String, dynamic> exercise, bool isSelected) {
    return InkWell(
      onTap: () => _addExercise(exercise['id']),
      borderRadius: BorderRadius.circular(12),
      child: Container(
        decoration: BoxDecoration(
          color: isSelected ? AppTheme.primaryColor.withValues(alpha: 26) : Colors.white,
          borderRadius: BorderRadius.circular(12),
          border: Border.all(
            color: isSelected ? AppTheme.primaryColor : Colors.grey.shade300,
            width: isSelected ? 2 : 1,
          ),
        ),
        child: Stack(
          children: [
            Padding(
              padding: const EdgeInsets.all(8.0),
              child: Row(
                children: [
                  ClipRRect(
                    borderRadius: BorderRadius.circular(8),
                    child: Image.asset(
                      exercise['imageUrl'],
                      width: 60,
                      height: 60,
                      fit: BoxFit.cover,
                      errorBuilder: (context, error, stackTrace) {
                        return Container(
                          width: 60,
                          height: 60,
                          color: Colors.grey.shade200,
                          child: const Icon(Icons.fitness_center, color: Colors.grey),
                        );
                      },
                    ),
                  ),
                  const SizedBox(width: 8),
                  Expanded(
                    child: Text(
                      exercise['name'],
                      style: const TextStyle(
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
                ],
              ),
            ),
            if (isSelected)
              Positioned(
                top: 4,
                right: 4,
                child: Container(
                  decoration: BoxDecoration(
                    color: AppTheme.primaryColor,
                    shape: BoxShape.circle,
                  ),
                  child: const Icon(
                    Icons.check,
                    color: Colors.white,
                    size: 16,
                  ),
                ),
              ),
          ],
        ),
      ),
    );
  }
  
  Widget _buildReviewPlanStep() {
    if (_workouts.isEmpty) {
      return const Center(
        child: Text('No workouts generated yet.'),
      );
    }
    
    final workout = _workouts.first;
    
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Review Your Plan',
          style: Theme.of(context).textTheme.titleLarge?.copyWith(
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 16),
        Container(
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(12),
            boxShadow: [
              BoxShadow(
                color: Colors.grey.withValues(alpha: 38), // 0.15 opacity
                blurRadius: 6,
                offset: const Offset(0, 3),
              ),
            ],
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                _nameController.text,
                style: const TextStyle(
                  fontWeight: FontWeight.bold,
                  fontSize: 18,
                ),
              ),
              const SizedBox(height: 4),
              Text(
                _descriptionController.text.isEmpty
                    ? 'No description provided'
                    : _descriptionController.text,
                style: TextStyle(
                  color: Colors.grey.shade600,
                  fontSize: 14,
                ),
              ),
              const SizedBox(height: 8),
              Row(
                children: [
                  Container(
                    padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                    decoration: BoxDecoration(
                      color: AppTheme.primaryColor.withValues(alpha: 26),
                      borderRadius: BorderRadius.circular(4),
                    ),
                    child: Text(
                      _selectedLevel,
                      style: TextStyle(
                        color: AppTheme.primaryColor,
                        fontWeight: FontWeight.bold,
                        fontSize: 12,
                      ),
                    ),
                  ),
                  const SizedBox(width: 8),
                  Container(
                    padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                    decoration: BoxDecoration(
                      color: Colors.orange.withValues(alpha: 26),
                      borderRadius: BorderRadius.circular(4),
                    ),
                    child: Text(
                      _selectedDuration,
                      style: const TextStyle(
                        color: Colors.orange,
                        fontWeight: FontWeight.bold,
                        fontSize: 12,
                      ),
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 16),
              const Text(
                'Exercises',
                style: TextStyle(
                  fontWeight: FontWeight.bold,
                  fontSize: 16,
                ),
              ),
              const SizedBox(height: 8),
              ...workout.exercises.map((exercise) {
                return Padding(
                  padding: const EdgeInsets.only(bottom: 8.0),
                  child: Row(
                    children: [
                      ClipRRect(
                        borderRadius: BorderRadius.circular(4),
                        child: Image.asset(
                          exercise.imageUrl,
                          width: 40,
                          height: 40,
                          fit: BoxFit.cover,
                          errorBuilder: (context, error, stackTrace) {
                            return Container(
                              width: 40,
                              height: 40,
                              color: Colors.grey.shade200,
                              child: const Icon(Icons.fitness_center, color: Colors.grey, size: 20),
                            );
                          },
                        ),
                      ),
                      const SizedBox(width: 8),
                      Expanded(
                        child: Text(
                          exercise.name,
                          style: const TextStyle(
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                      ),
                      Text(
                        '${exercise.sets} × ${exercise.reps}',
                        style: TextStyle(
                          color: Colors.grey.shade600,
                          fontSize: 14,
                        ),
                      ),
                    ],
                  ),
                );
              }),
              const SizedBox(height: 16),
              ElevatedButton(
                onPressed: () {
                  Navigator.push(
                    context,
                    MaterialPageRoute(
                      builder: (context) => WorkoutDayScreen(
                        workoutId: workout.id,
                        dayTitle: 'Day 1: ${workout.name}',
                      ),
                    ),
                  );
                },
                style: ElevatedButton.styleFrom(
                  backgroundColor: AppTheme.primaryColor,
                  foregroundColor: Colors.white,
                  minimumSize: const Size(double.infinity, 48),
                ),
                child: const Text('PREVIEW WORKOUT'),
              ),
            ],
          ),
        ),
      ],
    );
  }
}
