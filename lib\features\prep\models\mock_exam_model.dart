import 'package:flutter/material.dart';

enum QuestionType {
  multipleChoice,
  trueFalse,
  shortAnswer,
  essay,
}

class MockExamModel {
  final String id;
  final String title;
  final String description;
  final String category;
  final int timeLimit; // in minutes
  final List<ExamQuestionModel> questions;
  final bool isPremium;
  final Color color;
  final DateTime createdAt;
  final int totalAttempts;
  final double averageScore;

  MockExamModel({
    required this.id,
    required this.title,
    required this.description,
    required this.category,
    required this.timeLimit,
    required this.questions,
    this.isPremium = false,
    required this.color,
    required this.createdAt,
    this.totalAttempts = 0,
    this.averageScore = 0.0,
  });

  // Calculate total points possible
  int get totalPoints {
    return questions.fold(0, (sum, question) => sum + question.points);
  }

  // Calculate estimated completion time (in minutes)
  int get estimatedTime {
    return timeLimit > 0 ? timeLimit : (questions.length * 1.5).round();
  }
}

class ExamQuestionModel {
  final String id;
  final String text;
  final QuestionType type;
  final List<String> options;
  final List<int> correctAnswers; // Indices of correct options
  final String? correctAnswer; // For short answer questions
  final String? explanation;
  final int points;
  final String? imageUrl;

  ExamQuestionModel({
    required this.id,
    required this.text,
    required this.type,
    this.options = const [],
    this.correctAnswers = const [],
    this.correctAnswer,
    this.explanation,
    this.points = 1,
    this.imageUrl,
  });

  // Check if a multiple choice answer is correct
  bool isMultipleChoiceAnswerCorrect(List<int> selectedIndices) {
    if (type != QuestionType.multipleChoice) return false;
    
    // If only one correct answer is expected
    if (correctAnswers.length == 1) {
      return selectedIndices.length == 1 && selectedIndices[0] == correctAnswers[0];
    }
    
    // If multiple correct answers are expected
    if (selectedIndices.length != correctAnswers.length) return false;
    
    // Check if all selected answers are correct
    for (var index in selectedIndices) {
      if (!correctAnswers.contains(index)) return false;
    }
    
    return true;
  }

  // Check if a true/false answer is correct
  bool isTrueFalseAnswerCorrect(bool answer) {
    if (type != QuestionType.trueFalse) return false;
    return (answer && correctAnswers[0] == 0) || (!answer && correctAnswers[0] == 1);
  }

  // Check if a short answer is correct
  bool isShortAnswerCorrect(String answer) {
    if (type != QuestionType.shortAnswer || correctAnswer == null) return false;
    return answer.trim().toLowerCase() == correctAnswer!.trim().toLowerCase();
  }
}

class ExamAttemptModel {
  final String id;
  final String examId;
  final String userId;
  final DateTime startTime;
  final DateTime? endTime;
  final Map<String, dynamic> answers; // Question ID -> Answer
  final int score;
  final int totalPossible;
  final bool isCompleted;

  ExamAttemptModel({
    required this.id,
    required this.examId,
    required this.userId,
    required this.startTime,
    this.endTime,
    required this.answers,
    this.score = 0,
    this.totalPossible = 0,
    this.isCompleted = false,
  });

  // Calculate score percentage
  double get scorePercentage {
    if (totalPossible == 0) return 0.0;
    return (score / totalPossible) * 100;
  }

  // Calculate time taken in minutes
  int get timeTakenMinutes {
    if (endTime == null) return 0;
    return endTime!.difference(startTime).inMinutes;
  }

  // Check if the attempt was successful (e.g., passed)
  bool get isPassed {
    return scorePercentage >= 60.0; // 60% is passing
  }
}

class ExamResultModel {
  final String attemptId;
  final String examId;
  final String examTitle;
  final int score;
  final int totalPossible;
  final double percentage;
  final int timeTakenMinutes;
  final DateTime completedAt;
  final Map<String, bool> questionResults; // Question ID -> Correct/Incorrect
  final Map<String, String> feedback; // Question ID -> Feedback

  ExamResultModel({
    required this.attemptId,
    required this.examId,
    required this.examTitle,
    required this.score,
    required this.totalPossible,
    required this.percentage,
    required this.timeTakenMinutes,
    required this.completedAt,
    required this.questionResults,
    required this.feedback,
  });

  // Get number of correct answers
  int get correctAnswers {
    return questionResults.values.where((correct) => correct).length;
  }

  // Get number of incorrect answers
  int get incorrectAnswers {
    return questionResults.values.where((correct) => !correct).length;
  }

  // Get performance grade (A, B, C, D, F)
  String get grade {
    if (percentage >= 90) return 'A';
    if (percentage >= 80) return 'B';
    if (percentage >= 70) return 'C';
    if (percentage >= 60) return 'D';
    return 'F';
  }
}
