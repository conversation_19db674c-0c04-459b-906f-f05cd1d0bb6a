import 'package:flutter/foundation.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import 'package:fit_4_force/core/config/supabase_config.dart';
import 'package:fit_4_force/core/services/error_handling_service.dart';
import 'package:logger/logger.dart';

/// Enhanced authentication service with comprehensive features
class EnhancedAuthService {
  static final EnhancedAuthService _instance = EnhancedAuthService._internal();
  factory EnhancedAuthService() => _instance;
  EnhancedAuthService._internal();

  final Logger _logger = Logger();
  final ErrorHandlingService _errorHandler = ErrorHandlingService();
  
  /// Get Supabase client
  SupabaseClient get _client => SupabaseConfig.client;

  /// Get current user
  User? get currentUser => _client.auth.currentUser;

  /// Check if user is authenticated
  bool get isAuthenticated => currentUser != null;

  /// Get current user ID
  String? get currentUserId => currentUser?.id;

  /// Sign up with email and password
  Future<AuthResponse?> signUp({
    required String email,
    required String password,
    required String fullName,
    required String targetAgency,
    Map<String, dynamic>? additionalData,
  }) async {
    try {
      _logger.i('🔐 Attempting sign up for: $email');

      final response = await _client.auth.signUp(
        email: email,
        password: password,
        data: {
          'full_name': fullName,
          'target_agency': targetAgency,
          ...?additionalData,
        },
      );

      if (response.user != null) {
        _logger.i('✅ Sign up successful for: $email');
        
        // Create user preferences record
        await _createUserPreferences(response.user!.id, targetAgency);
        
        return response;
      } else {
        _logger.w('⚠️ Sign up failed: No user returned');
        return null;
      }
    } catch (e) {
      _logger.e('❌ Sign up error: $e');
      _errorHandler.logError('signUp', e);
      rethrow;
    }
  }

  /// Sign in with email and password
  Future<AuthResponse?> signIn({
    required String email,
    required String password,
  }) async {
    try {
      _logger.i('🔐 Attempting sign in for: $email');

      final response = await _client.auth.signInWithPassword(
        email: email,
        password: password,
      );

      if (response.user != null) {
        _logger.i('✅ Sign in successful for: $email');
        
        // Update last login timestamp
        await _updateLastLogin(response.user!.id);
        
        return response;
      } else {
        _logger.w('⚠️ Sign in failed: No user returned');
        return null;
      }
    } catch (e) {
      _logger.e('❌ Sign in error: $e');
      _errorHandler.logError('signIn', e);
      rethrow;
    }
  }

  /// Sign out
  Future<void> signOut() async {
    try {
      _logger.i('🔐 Signing out user');
      await _client.auth.signOut();
      _logger.i('✅ Sign out successful');
    } catch (e) {
      _logger.e('❌ Sign out error: $e');
      _errorHandler.logError('signOut', e);
      rethrow;
    }
  }

  /// Send password reset email
  Future<void> resetPassword(String email) async {
    try {
      _logger.i('🔐 Sending password reset email to: $email');
      
      await _client.auth.resetPasswordForEmail(
        email,
        redirectTo: kIsWeb ? '${Uri.base.origin}/reset-password' : null,
      );
      
      _logger.i('✅ Password reset email sent to: $email');
    } catch (e) {
      _logger.e('❌ Password reset error: $e');
      _errorHandler.logError('resetPassword', e);
      rethrow;
    }
  }

  /// Update password
  Future<void> updatePassword(String newPassword) async {
    try {
      if (!isAuthenticated) {
        throw Exception('User not authenticated');
      }

      _logger.i('🔐 Updating password for user: $currentUserId');
      
      await _client.auth.updateUser(
        UserAttributes(password: newPassword),
      );
      
      _logger.i('✅ Password updated successfully');
    } catch (e) {
      _logger.e('❌ Password update error: $e');
      _errorHandler.logError('updatePassword', e);
      rethrow;
    }
  }

  /// Update user profile
  Future<void> updateProfile({
    String? fullName,
    String? avatarUrl,
    Map<String, dynamic>? additionalData,
  }) async {
    try {
      if (!isAuthenticated) {
        throw Exception('User not authenticated');
      }

      _logger.i('🔐 Updating profile for user: $currentUserId');

      final updates = <String, dynamic>{};
      if (fullName != null) updates['full_name'] = fullName;
      if (avatarUrl != null) updates['avatar_url'] = avatarUrl;
      if (additionalData != null) updates.addAll(additionalData);

      if (updates.isNotEmpty) {
        await _client.auth.updateUser(UserAttributes(data: updates));
        _logger.i('✅ Profile updated successfully');
      }
    } catch (e) {
      _logger.e('❌ Profile update error: $e');
      _errorHandler.logError('updateProfile', e);
      rethrow;
    }
  }

  /// Resend email confirmation
  Future<void> resendEmailConfirmation(String email) async {
    try {
      _logger.i('📧 Resending email confirmation to: $email');
      
      await _client.auth.resend(
        type: OtpType.signup,
        email: email,
      );
      
      _logger.i('✅ Email confirmation resent to: $email');
    } catch (e) {
      _logger.e('❌ Resend email confirmation error: $e');
      _errorHandler.logError('resendEmailConfirmation', e);
      rethrow;
    }
  }

  /// Check if email is verified
  bool get isEmailVerified => currentUser?.emailConfirmedAt != null;

  /// Get user metadata
  Map<String, dynamic>? get userMetadata => currentUser?.userMetadata;

  /// Get user app metadata
  Map<String, dynamic>? get appMetadata => currentUser?.appMetadata;

  /// Create user preferences record
  Future<void> _createUserPreferences(String userId, String targetAgency) async {
    try {
      // Get agency ID from agency code/name
      final agencyResponse = await _client
          .from('agencies')
          .select('id')
          .eq('code', targetAgency)
          .maybeSingle();

      if (agencyResponse == null) {
        _logger.w('⚠️ Agency not found: $targetAgency');
        return;
      }

      final agencyId = agencyResponse['id'] as String;

      // Create user preferences
      await _client.from('user_preferences').insert({
        'user_id': userId,
        'target_agency_id': agencyId,
        'is_premium': false,
        'notification_settings': {
          'email_notifications': true,
          'push_notifications': true,
          'study_reminders': true,
          'community_updates': true,
        },
        'study_preferences': {
          'daily_study_goal': 30, // minutes
          'preferred_study_time': 'evening',
          'difficulty_preference': 'medium',
        },
        'created_at': DateTime.now().toIso8601String(),
        'updated_at': DateTime.now().toIso8601String(),
      });

      _logger.i('✅ User preferences created for agency: $targetAgency');
    } catch (e) {
      _logger.e('❌ Error creating user preferences: $e');
      // Don't rethrow as this is not critical for sign up
    }
  }

  /// Update last login timestamp
  Future<void> _updateLastLogin(String userId) async {
    try {
      await _client.from('user_preferences').update({
        'last_login_at': DateTime.now().toIso8601String(),
        'updated_at': DateTime.now().toIso8601String(),
      }).eq('user_id', userId);

      _logger.d('📅 Updated last login for user: $userId');
    } catch (e) {
      _logger.e('❌ Error updating last login: $e');
      // Don't rethrow as this is not critical
    }
  }

  /// Get user preferences
  Future<Map<String, dynamic>?> getUserPreferences() async {
    try {
      if (!isAuthenticated) return null;

      final response = await _client
          .from('user_preferences')
          .select('''
            *,
            agencies!inner(
              id,
              name,
              code,
              full_name
            )
          ''')
          .eq('user_id', currentUserId!)
          .maybeSingle();

      return response;
    } catch (e) {
      _logger.e('❌ Error getting user preferences: $e');
      return null;
    }
  }

  /// Update user preferences
  Future<bool> updateUserPreferences(Map<String, dynamic> preferences) async {
    try {
      if (!isAuthenticated) return false;

      await _client.from('user_preferences').update({
        ...preferences,
        'updated_at': DateTime.now().toIso8601String(),
      }).eq('user_id', currentUserId!);

      _logger.i('✅ User preferences updated');
      return true;
    } catch (e) {
      _logger.e('❌ Error updating user preferences: $e');
      return false;
    }
  }

  /// Check if user has premium access
  Future<bool> isPremiumUser() async {
    try {
      final preferences = await getUserPreferences();
      if (preferences == null) return false;

      final isPremium = preferences['is_premium'] as bool? ?? false;
      final expiryDate = preferences['premium_expiry_date'] as String?;

      if (!isPremium) return false;

      // Check if premium hasn't expired
      if (expiryDate != null) {
        final expiry = DateTime.parse(expiryDate);
        return DateTime.now().isBefore(expiry);
      }

      return isPremium;
    } catch (e) {
      _logger.e('❌ Error checking premium status: $e');
      return false;
    }
  }

  /// Listen to auth state changes
  Stream<AuthState> get authStateChanges => _client.auth.onAuthStateChange;

  /// Delete user account
  Future<void> deleteAccount() async {
    try {
      if (!isAuthenticated) {
        throw Exception('User not authenticated');
      }

      _logger.i('🗑️ Deleting user account: $currentUserId');

      // Note: Supabase doesn't have a direct delete user method
      // This would typically be handled by a server-side function
      // For now, we'll just sign out the user
      await signOut();
      
      _logger.i('✅ User account deletion initiated');
    } catch (e) {
      _logger.e('❌ Account deletion error: $e');
      _errorHandler.logError('deleteAccount', e);
      rethrow;
    }
  }
}
