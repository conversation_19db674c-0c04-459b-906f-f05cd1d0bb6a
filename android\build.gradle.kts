buildscript {
    repositories {
        // Add <PERSON>yun mirror for Google Maven repository
        maven { url = uri("https://maven.aliyun.com/repository/google") }
        maven { url = uri("https://maven.aliyun.com/repository/public") }
        maven { url = uri("https://maven.aliyun.com/repository/gradle-plugin") }
        // Fallback to original repositories
        google()
        mavenCentral()
        jcenter() // Add jcenter as fallback
    }
    dependencies {
        // No dependencies needed
    }
}

allprojects {
    repositories {
        // Add Aliyun mirror for Google Maven repository
        maven { url = uri("https://maven.aliyun.com/repository/google") }
        maven { url = uri("https://maven.aliyun.com/repository/public") }
        maven { url = uri("https://maven.aliyun.com/repository/gradle-plugin") }
        // Fallback to original repositories
        google()
        mavenCentral()
        jcenter() // Add jcenter as fallback
    }

    // Force all projects to use the same SDK version
    afterEvaluate {
        project.extensions.findByName("android")?.let { androidExt ->
            if (androidExt is com.android.build.gradle.BaseExtension) {
                androidExt.compileSdkVersion(36)
            }
        }
    }
}

val newBuildDir: Directory = rootProject.layout.buildDirectory.dir("../../build").get()
rootProject.layout.buildDirectory.value(newBuildDir)

subprojects {
    val newSubprojectBuildDir: Directory = newBuildDir.dir(project.name)
    project.layout.buildDirectory.value(newSubprojectBuildDir)
}

tasks.register<Delete>("clean") {
    delete(rootProject.layout.buildDirectory)
}
