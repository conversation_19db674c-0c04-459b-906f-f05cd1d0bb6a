import 'package:flutter/material.dart';
import '../utils/responsive_utils.dart';

/// Widget for testing responsive design across different screen sizes
class ResponsiveTestWidget extends StatefulWidget {
  final Widget child;

  const ResponsiveTestWidget({super.key, required this.child});

  @override
  State<ResponsiveTestWidget> createState() => _ResponsiveTestWidgetState();
}

class _ResponsiveTestWidgetState extends State<ResponsiveTestWidget> {
  TestDevice _selectedDevice = TestDevice.actual;
  bool _showOverlay = false;

  @override
  Widget build(BuildContext context) {
    if (_selectedDevice == TestDevice.actual) {
      return Stack(
        children: [
          widget.child,
          if (_showOverlay) _buildResponsiveOverlay(context),
          _buildTestControls(context),
        ],
      );
    }

    return Stack(
      children: [
        Center(
          child: Container(
            width: _selectedDevice.size.width,
            height: _selectedDevice.size.height,
            decoration: BoxDecoration(
              border: Border.all(color: Colors.red, width: 2),
              borderRadius: BorderRadius.circular(8),
            ),
            child: ClipRRect(
              borderRadius: BorderRadius.circular(6),
              child: MediaQuery(
                data: MediaQuery.of(context).copyWith(
                  size: _selectedDevice.size,
                ),
                child: widget.child,
              ),
            ),
          ),
        ),
        if (_showOverlay) _buildResponsiveOverlay(context),
        _buildTestControls(context),
      ],
    );
  }

  Widget _buildTestControls(BuildContext context) {
    return Positioned(
      top: 50,
      right: 16,
      child: Container(
        padding: const EdgeInsets.all(8),
        decoration: BoxDecoration(
          color: Colors.black87,
          borderRadius: BorderRadius.circular(8),
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            IconButton(
              icon: Icon(
                _showOverlay ? Icons.visibility_off : Icons.visibility,
                color: Colors.white,
                size: 20,
              ),
              onPressed: () {
                setState(() {
                  _showOverlay = !_showOverlay;
                });
              },
            ),
            const SizedBox(height: 8),
            DropdownButton<TestDevice>(
              value: _selectedDevice,
              dropdownColor: Colors.black87,
              style: const TextStyle(color: Colors.white, fontSize: 12),
              underline: Container(),
              items: TestDevice.values.map((device) {
                return DropdownMenuItem(
                  value: device,
                  child: Text(
                    device.name,
                    style: const TextStyle(color: Colors.white, fontSize: 12),
                  ),
                );
              }).toList(),
              onChanged: (device) {
                if (device != null) {
                  setState(() {
                    _selectedDevice = device;
                  });
                }
              },
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildResponsiveOverlay(BuildContext context) {
    final mediaQuery = MediaQuery.of(context);
    final size = _selectedDevice == TestDevice.actual 
        ? mediaQuery.size 
        : _selectedDevice.size;
    
    final layoutType = ResponsiveUtils.getLayoutType(context);
    final isTablet = ResponsiveUtils.isTablet(context);
    final isMobile = ResponsiveUtils.isMobile(context);
    final isDesktop = ResponsiveUtils.isDesktop(context);

    return Positioned(
      top: 100,
      left: 16,
      child: Container(
        padding: const EdgeInsets.all(12),
        decoration: BoxDecoration(
          color: Colors.black87,
          borderRadius: BorderRadius.circular(8),
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          mainAxisSize: MainAxisSize.min,
          children: [
            Text(
              'Screen Info',
              style: const TextStyle(
                color: Colors.white,
                fontWeight: FontWeight.bold,
                fontSize: 14,
              ),
            ),
            const SizedBox(height: 8),
            Text(
              'Size: ${size.width.toInt()} x ${size.height.toInt()}',
              style: const TextStyle(color: Colors.white, fontSize: 12),
            ),
            Text(
              'Layout: ${layoutType.name}',
              style: const TextStyle(color: Colors.white, fontSize: 12),
            ),
            Text(
              'Type: ${isMobile ? 'Mobile' : isTablet ? 'Tablet' : isDesktop ? 'Desktop' : 'Unknown'}',
              style: const TextStyle(color: Colors.white, fontSize: 12),
            ),
            Text(
              'Pixel Ratio: ${mediaQuery.devicePixelRatio.toStringAsFixed(1)}',
              style: const TextStyle(color: Colors.white, fontSize: 12),
            ),
            Text(
              'Text Scale: ${ResponsiveUtils.getTextScaleFactor(context).toStringAsFixed(1)}',
              style: const TextStyle(color: Colors.white, fontSize: 12),
            ),
          ],
        ),
      ),
    );
  }
}

/// Test device configurations for responsive testing
enum TestDevice {
  actual('Actual Device', Size(0, 0)),
  iphoneSE('iPhone SE', Size(375, 667)),
  iphone12('iPhone 12', Size(390, 844)),
  iphone12ProMax('iPhone 12 Pro Max', Size(428, 926)),
  pixel4('Pixel 4', Size(353, 745)),
  pixel4XL('Pixel 4 XL', Size(411, 823)),
  ipadMini('iPad Mini', Size(744, 1133)),
  ipad('iPad', Size(820, 1180)),
  ipadPro11('iPad Pro 11"', Size(834, 1194)),
  ipadPro129('iPad Pro 12.9"', Size(1024, 1366)),
  macbook('MacBook', Size(1280, 832)),
  desktop('Desktop', Size(1920, 1080));

  const TestDevice(this.displayName, this.size);

  final String displayName;
  final Size size;

  String get name => displayName;
}

/// Extension to add responsive testing to any widget
extension ResponsiveTestExtension on Widget {
  Widget withResponsiveTest() {
    return ResponsiveTestWidget(child: this);
  }
}
