import 'package:flutter/material.dart';

class StudyProgressModel {
  final String id;
  final String userId;
  final String category;
  final DateTime date;
  final int studyMinutes;
  final int flashcardsReviewed;
  final int quizzesTaken;
  final double quizAvgScore;
  final Color color;

  StudyProgressModel({
    required this.id,
    required this.userId,
    required this.category,
    required this.date,
    required this.studyMinutes,
    required this.flashcardsReviewed,
    required this.quizzesTaken,
    required this.quizAvgScore,
    required this.color,
  });

  // Calculate total study points
  int get totalPoints {
    // Points formula: 
    // 1 point per minute studied
    // 2 points per flashcard reviewed
    // 10 points per quiz taken
    // Bonus points for quiz scores (0-100% of quiz points)
    
    final studyPoints = studyMinutes;
    final flashcardPoints = flashcardsReviewed * 2;
    final quizBasePoints = quizzesTaken * 10;
    final quizBonusPoints = quizzesTaken > 0 
        ? (quizAvgScore / 100 * quizBasePoints).round() 
        : 0;
    
    return studyPoints + flashcardPoints + quizBasePoints + quizBonusPoints;
  }
}

class WeeklyProgressModel {
  final DateTime weekStart;
  final DateTime weekEnd;
  final Map<String, int> minutesByCategory;
  final int totalMinutes;
  final int totalFlashcards;
  final int totalQuizzes;
  final double avgQuizScore;
  final int totalPoints;
  final bool isCurrentWeek;

  WeeklyProgressModel({
    required this.weekStart,
    required this.weekEnd,
    required this.minutesByCategory,
    required this.totalMinutes,
    required this.totalFlashcards,
    required this.totalQuizzes,
    required this.avgQuizScore,
    required this.totalPoints,
    this.isCurrentWeek = false,
  });
}

class StudyStreakModel {
  final int currentStreak;
  final int longestStreak;
  final DateTime lastStudyDate;
  final List<DateTime> studyDates;

  StudyStreakModel({
    required this.currentStreak,
    required this.longestStreak,
    required this.lastStudyDate,
    required this.studyDates,
  });

  // Check if studied today
  bool get studiedToday {
    final now = DateTime.now();
    final today = DateTime(now.year, now.month, now.day);
    final lastStudy = DateTime(
      lastStudyDate.year,
      lastStudyDate.month,
      lastStudyDate.day,
    );
    return today.isAtSameMomentAs(lastStudy);
  }

  // Check if streak is at risk (last study was yesterday)
  bool get streakAtRisk {
    if (studiedToday) return false;
    
    final now = DateTime.now();
    final today = DateTime(now.year, now.month, now.day);
    final yesterday = today.subtract(const Duration(days: 1));
    final lastStudy = DateTime(
      lastStudyDate.year,
      lastStudyDate.month,
      lastStudyDate.day,
    );
    
    return yesterday.isAtSameMomentAs(lastStudy);
  }
}

class StudyGoalModel {
  final String id;
  final String userId;
  final String title;
  final String description;
  final DateTime createdAt;
  final DateTime targetDate;
  final int targetMinutes;
  final int targetFlashcards;
  final int targetQuizzes;
  final double targetQuizScore;
  final int currentMinutes;
  final int currentFlashcards;
  final int currentQuizzes;
  final double currentQuizScore;
  final bool isCompleted;
  final Color color;

  StudyGoalModel({
    required this.id,
    required this.userId,
    required this.title,
    required this.description,
    required this.createdAt,
    required this.targetDate,
    required this.targetMinutes,
    required this.targetFlashcards,
    required this.targetQuizzes,
    required this.targetQuizScore,
    required this.currentMinutes,
    required this.currentFlashcards,
    required this.currentQuizzes,
    required this.currentQuizScore,
    required this.isCompleted,
    required this.color,
  });

  // Calculate overall progress percentage
  double get overallProgress {
    double minutesProgress = targetMinutes > 0 
        ? (currentMinutes / targetMinutes).clamp(0.0, 1.0) 
        : 1.0;
    
    double flashcardsProgress = targetFlashcards > 0 
        ? (currentFlashcards / targetFlashcards).clamp(0.0, 1.0) 
        : 1.0;
    
    double quizzesProgress = targetQuizzes > 0 
        ? (currentQuizzes / targetQuizzes).clamp(0.0, 1.0) 
        : 1.0;
    
    double scoreProgress = targetQuizScore > 0 
        ? (currentQuizScore / targetQuizScore).clamp(0.0, 1.0) 
        : 1.0;
    
    // Average of all progress metrics
    return (minutesProgress + flashcardsProgress + quizzesProgress + scoreProgress) / 4;
  }

  // Check if goal is overdue
  bool get isOverdue {
    return !isCompleted && DateTime.now().isAfter(targetDate);
  }

  // Calculate days remaining
  int get daysRemaining {
    final now = DateTime.now();
    final difference = targetDate.difference(now).inDays;
    return difference > 0 ? difference : 0;
  }
}
