import 'package:flutter/material.dart';
import 'package:fit_4_force/core/theme/app_theme.dart';
import 'package:fit_4_force/features/fitness/models/workout_model.dart';
import 'package:fit_4_force/features/fitness/screens/workout_day_screen.dart';
import 'package:fit_4_force/features/fitness/screens/create_workout_plan_screen.dart';

class CustomWorkoutScreen extends StatefulWidget {
  const CustomWorkoutScreen({super.key});

  @override
  State<CustomWorkoutScreen> createState() => _CustomWorkoutScreenState();
}

class _CustomWorkoutScreenState extends State<CustomWorkoutScreen> {
  final List<WorkoutPlanModel> _workoutPlans = [];
  bool _isLoading = true;

  @override
  void initState() {
    super.initState();
    _loadWorkoutPlans();
  }

  Future<void> _loadWorkoutPlans() async {
    // In a real app, you would fetch this from your service
    await Future.delayed(const Duration(milliseconds: 500));

    final mockWorkouts = [
      WorkoutModel(
        id: 'workout1',
        name: 'Full Body Workout',
        description: 'A complete workout targeting all major muscle groups',
        imageUrl: 'assets/images/workouts/full_body.jpg',
        category: 'Strength',
        duration: 45,
        calories: 350,
        exercises: _getMockExercises(),
        icon: Icons.fitness_center,
        color: Colors.blue,
      ),
      WorkoutModel(
        id: 'workout2',
        name: 'Upper Body Focus',
        description: 'Strengthen your arms, chest, and back',
        imageUrl: 'assets/images/workouts/upper_body.jpg',
        category: 'Strength',
        duration: 30,
        calories: 250,
        exercises: _getMockExercises().sublist(0, 3),
        icon: Icons.fitness_center,
        color: Colors.green,
      ),
      WorkoutModel(
        id: 'workout3',
        name: 'Core Crusher',
        description: 'Build a strong and stable core',
        imageUrl: 'assets/images/workouts/core.jpg',
        category: 'Core',
        duration: 20,
        calories: 180,
        exercises: _getMockExercises().sublist(2, 5),
        icon: Icons.accessibility_new,
        color: Colors.purple,
      ),
    ];

    final plans = [
      WorkoutPlanModel(
        id: 'plan1',
        name: 'Military Fitness Bootcamp',
        description: 'A 4-week program to build military-level fitness',
        imageUrl: 'assets/images/workouts/military.jpg',
        duration: '4 weeks',
        level: 'Intermediate',
        color: Colors.blue,
        workouts: mockWorkouts,
        isPremium: true,
      ),
      WorkoutPlanModel(
        id: 'plan2',
        name: 'Beginner Strength Builder',
        description: 'Perfect for those new to strength training',
        imageUrl: 'assets/images/workouts/beginner.jpg',
        duration: '6 weeks',
        level: 'Beginner',
        color: Colors.green,
        workouts: mockWorkouts.sublist(0, 2),
      ),
      WorkoutPlanModel(
        id: 'plan3',
        name: 'Custom Plan',
        description: 'Your personalized workout plan',
        imageUrl: 'assets/images/workouts/custom.jpg',
        duration: 'Ongoing',
        level: 'Custom',
        color: Colors.orange,
        workouts: [mockWorkouts[0]],
      ),
    ];

    setState(() {
      _workoutPlans.addAll(plans);
      _isLoading = false;
    });
  }

  List<ExerciseModel> _getMockExercises() {
    return [
      ExerciseModel(
        id: '1',
        name: 'Jumping Jacks',
        description: 'A classic cardio exercise to warm up',
        imageUrl: 'assets/images/exercises/jumping_jacks.png',
        videoUrl: 'https://example.com/videos/jumping_jacks.mp4',
        duration: 20, // seconds
        sets: 1,
        reps: 0, // Time-based exercise
        restTime: 10,
      ),
      ExerciseModel(
        id: '2',
        name: 'Incline Push-Ups',
        description: 'Push-ups with hands elevated on a bench or step',
        imageUrl: 'assets/images/exercises/incline_pushups.png',
        videoUrl: 'https://example.com/videos/incline_pushups.mp4',
        duration: 0, // Not time-based
        sets: 3,
        reps: 16,
        restTime: 30,
      ),
      ExerciseModel(
        id: '3',
        name: 'Knee Push-Ups',
        description: 'Modified push-ups with knees on the ground',
        imageUrl: 'assets/images/exercises/knee_pushups.png',
        videoUrl: 'https://example.com/videos/knee_pushups.mp4',
        duration: 0,
        sets: 3,
        reps: 10,
        restTime: 30,
      ),
      ExerciseModel(
        id: '4',
        name: 'Push-Ups',
        description: 'Standard push-ups',
        imageUrl: 'assets/images/exercises/pushups.png',
        videoUrl: 'https://example.com/videos/pushups.mp4',
        duration: 0,
        sets: 3,
        reps: 8,
        restTime: 45,
      ),
      ExerciseModel(
        id: '5',
        name: 'Wide Arm Push-Ups',
        description: 'Push-ups with arms placed wider than shoulders',
        imageUrl: 'assets/images/exercises/wide_pushups.png',
        videoUrl: 'https://example.com/videos/wide_pushups.mp4',
        duration: 0,
        sets: 3,
        reps: 6,
        restTime: 60,
      ),
    ];
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Custom Workout Plans'),
        actions: [
          IconButton(
            icon: const Icon(Icons.search),
            onPressed: () {
              // Show search dialog
            },
          ),
        ],
      ),
      body:
          _isLoading
              ? const Center(child: CircularProgressIndicator())
              : _buildContent(),
      floatingActionButton: FloatingActionButton(
        onPressed: () {
          _showCreatePlanDialog();
        },
        backgroundColor: AppTheme.primaryColor,
        child: const Icon(Icons.add),
      ),
    );
  }

  Widget _buildContent() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Your Workout Plans',
            style: Theme.of(
              context,
            ).textTheme.titleLarge?.copyWith(fontWeight: FontWeight.bold),
          ),
          const SizedBox(height: 16),
          ..._workoutPlans.map((plan) => _buildPlanCard(plan)),
          const SizedBox(height: 24),
          _buildCreatePlanCard(),
        ],
      ),
    );
  }

  Widget _buildPlanCard(WorkoutPlanModel plan) {
    return Container(
      margin: const EdgeInsets.only(bottom: 16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withValues(alpha: 38), // 0.15 opacity
            blurRadius: 10,
            offset: const Offset(0, 5),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Header with image
          Stack(
            children: [
              ClipRRect(
                borderRadius: const BorderRadius.vertical(
                  top: Radius.circular(16),
                ),
                child: Image.asset(
                  plan.imageUrl,
                  height: 150,
                  width: double.infinity,
                  fit: BoxFit.cover,
                  errorBuilder: (context, error, stackTrace) {
                    return Container(
                      height: 150,
                      color: plan.color.withValues(alpha: 51), // 0.2 opacity
                      child: Center(
                        child: Icon(
                          Icons.fitness_center,
                          size: 50,
                          color: plan.color,
                        ),
                      ),
                    );
                  },
                ),
              ),
              // Premium badge
              if (plan.isPremium)
                Positioned(
                  top: 12,
                  right: 12,
                  child: Container(
                    padding: const EdgeInsets.symmetric(
                      horizontal: 12,
                      vertical: 6,
                    ),
                    decoration: BoxDecoration(
                      color: Colors.amber,
                      borderRadius: BorderRadius.circular(16),
                    ),
                    child: const Row(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        Icon(Icons.star, color: Colors.white, size: 16),
                        SizedBox(width: 4),
                        Text(
                          'PREMIUM',
                          style: TextStyle(
                            color: Colors.white,
                            fontWeight: FontWeight.bold,
                            fontSize: 12,
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
            ],
          ),

          // Content
          Padding(
            padding: const EdgeInsets.all(16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    Expanded(
                      child: Text(
                        plan.name,
                        style: const TextStyle(
                          fontWeight: FontWeight.bold,
                          fontSize: 18,
                        ),
                      ),
                    ),
                    Container(
                      padding: const EdgeInsets.symmetric(
                        horizontal: 8,
                        vertical: 4,
                      ),
                      decoration: BoxDecoration(
                        color: plan.color.withValues(alpha: 26), // 0.1 opacity
                        borderRadius: BorderRadius.circular(8),
                      ),
                      child: Text(
                        plan.level,
                        style: TextStyle(
                          color: plan.color,
                          fontWeight: FontWeight.bold,
                          fontSize: 12,
                        ),
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 8),
                Text(
                  plan.description,
                  style: TextStyle(color: Colors.grey.shade600, fontSize: 14),
                ),
                const SizedBox(height: 16),
                Row(
                  children: [
                    _buildPlanStat(Icons.calendar_today, plan.duration),
                    const SizedBox(width: 16),
                    _buildPlanStat(
                      Icons.fitness_center,
                      '${plan.workouts.length} workouts',
                    ),
                  ],
                ),
                const SizedBox(height: 16),
                Row(
                  children: [
                    Expanded(
                      child: OutlinedButton(
                        onPressed: () {
                          // View plan details
                        },
                        style: OutlinedButton.styleFrom(
                          side: BorderSide(color: plan.color),
                          foregroundColor: plan.color,
                          padding: const EdgeInsets.symmetric(vertical: 12),
                        ),
                        child: const Text('VIEW DETAILS'),
                      ),
                    ),
                    const SizedBox(width: 16),
                    Expanded(
                      child: ElevatedButton(
                        onPressed: () {
                          _startWorkoutPlan(plan);
                        },
                        style: ElevatedButton.styleFrom(
                          backgroundColor: plan.color,
                          foregroundColor: Colors.white,
                          padding: const EdgeInsets.symmetric(vertical: 12),
                        ),
                        child: const Text('START'),
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildCreatePlanCard() {
    return InkWell(
      onTap: () {
        _showCreatePlanDialog();
      },
      borderRadius: BorderRadius.circular(16),
      child: Container(
        padding: const EdgeInsets.all(24),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(16),
          border: Border.all(
            color: Colors.grey.withValues(alpha: 51), // 0.2 opacity
            width: 2,
          ),
        ),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Container(
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: AppTheme.primaryColor.withValues(
                  alpha: 26,
                ), // 0.1 opacity
                shape: BoxShape.circle,
              ),
              child: Icon(Icons.add, color: AppTheme.primaryColor, size: 32),
            ),
            const SizedBox(height: 16),
            const Text(
              'Create Custom Plan',
              style: TextStyle(fontWeight: FontWeight.bold, fontSize: 18),
            ),
            const SizedBox(height: 8),
            Text(
              'Design your own workout plan tailored to your goals',
              style: TextStyle(color: Colors.grey.shade600, fontSize: 14),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildPlanStat(IconData icon, String text) {
    return Row(
      children: [
        Icon(icon, size: 16, color: Colors.grey.shade600),
        const SizedBox(width: 4),
        Text(text, style: TextStyle(color: Colors.grey.shade600, fontSize: 14)),
      ],
    );
  }

  void _startWorkoutPlan(WorkoutPlanModel plan) {
    if (plan.workouts.isNotEmpty) {
      Navigator.push(
        context,
        MaterialPageRoute(
          builder:
              (context) => WorkoutDayScreen(
                workoutId: plan.workouts[0].id,
                dayTitle: 'Day 1: ${plan.name}',
              ),
        ),
      );
    }
  }

  void _showCreatePlanDialog() {
    showDialog(
      context: context,
      builder:
          (context) => AlertDialog(
            title: const Text('Create Custom Plan'),
            content: const Text(
              'Create your own personalized workout plan by selecting exercises and organizing them into a schedule that fits your goals.',
            ),
            actions: [
              TextButton(
                onPressed: () {
                  Navigator.pop(context);
                },
                child: const Text('CANCEL'),
              ),
              ElevatedButton(
                onPressed: () {
                  Navigator.pop(context);
                  // Navigate to plan creation screen
                  Navigator.push(
                    context,
                    MaterialPageRoute(
                      builder: (context) => const CreateWorkoutPlanScreen(),
                    ),
                  );
                },
                style: ElevatedButton.styleFrom(
                  backgroundColor: AppTheme.primaryColor,
                ),
                child: const Text('CREATE'),
              ),
            ],
          ),
    );
  }
}
