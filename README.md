# Fit4Force 🇳🇬

**A comprehensive Flutter app for Nigerian military aspirants**

Fit4Force is a mobile application designed to help Nigerian citizens prepare for military recruitment across various agencies including the Nigerian Army, Navy, Air Force, Police, Civil Defence, Immigration, Customs, and Fire Service.

## 🎯 Features

### 📚 **Study & Preparation**
- Agency-specific study materials and past questions
- Interactive flashcards and mock exams
- Progress tracking and performance analytics
- Pomodoro timer for focused study sessions
- Smart learning assistant with AI recommendations

### 💪 **Fitness Training**
- Comprehensive workout categories (Fat Loss, Strength, Military Fitness, Core & Abs, Flexibility)
- Personalized training plans based on target agency
- Exercise video demonstrations and proper form guidance
- Fitness challenges and progress tracking
- Nutrition guidance and recovery tracking

### 👥 **Community Features**
- Agency-specific forums and discussions
- Study group formation and collaboration
- Success stories from successful candidates
- Achievement badges and leaderboards
- Real-time messaging and notifications

### 💎 **Premium Features**
- Advanced study materials and exclusive content
- Personalized training plans and nutrition guidance
- Priority support and early access to new features
- Ad-free experience and offline content access

## 🏗️ Architecture

### **Frontend (Flutter)**
- **Clean Architecture** with feature-based organization
- **Responsive Design** for mobile, tablet, and desktop
- **State Management** using BLoC pattern
- **Real-time Updates** via Supabase subscriptions
- **Offline Support** for core features

### **Backend (Supabase)**
- **Authentication** with email/password and social login
- **Real-time Database** with PostgreSQL and Row Level Security
- **File Storage** for images, videos, and documents
- **Edge Functions** for serverless business logic
- **Push Notifications** for engagement and reminders

### **Content Management**
- **Admin Dashboard**: Content managed via Supabase web interface
- **User App**: Content consumption and profile management only
- **Clean Separation**: No admin features in mobile app

## 🚀 Getting Started

### Prerequisites
- Flutter SDK (3.0+)
- Dart SDK (3.0+)
- Android Studio / VS Code
- Git

### Installation

1. **Clone the repository**
   ```bash
   git clone https://github.com/nehemiahandrew78/fit_4_force.git
   cd fit_4_force
   ```

2. **Install dependencies**
   ```bash
   flutter pub get
   ```

3. **Configure Supabase**
   - Create a Supabase project at [supabase.com](https://supabase.com)
   - Update `lib/core/config/supabase_config.dart` with your credentials
   - Run the SQL setup scripts in the `database_*.sql` files

4. **Run the app**
   ```bash
   flutter run
   ```

## 📱 Supported Platforms

- ✅ **Android** (Primary target)
- ✅ **iOS** (Secondary support)
- ✅ **Web** (Progressive Web App)
- ✅ **Windows** (Desktop support)

## 🔧 Configuration

### Environment Setup
1. Copy `.env.example` to `.env`
2. Configure your Supabase credentials
3. Set up storage buckets and RLS policies
4. Configure push notification settings

### Database Setup
Run the following SQL files in order:
1. `supabase_setup_part1.sql` - Core tables
2. `supabase_setup_part2.sql` - Additional features
3. `supabase_setup_part3_rls.sql` - Security policies

## 📖 Documentation

- [**Supabase Setup Guide**](SUPABASE_SETUP_GUIDE.md) - Complete backend configuration
- [**Content Management**](SUPABASE_CONTENT_MANAGEMENT.md) - Admin content upload guide
- [**Storage Setup**](SUPABASE_STORAGE_SETUP.md) - File storage configuration
- [**Responsive Design**](RESPONSIVE_DESIGN_GUIDE.md) - UI/UX guidelines
- [**Backend Implementation**](BACKEND_IMPLEMENTATION.md) - Technical architecture

## 🛡️ Security

- **Row Level Security (RLS)** on all database tables
- **JWT Authentication** with secure token management
- **File Upload Validation** with type and size restrictions
- **API Rate Limiting** and abuse prevention
- **Data Encryption** for sensitive information

## 🧪 Testing

```bash
# Run unit tests
flutter test

# Run integration tests
flutter test integration_test/

# Run widget tests
flutter test test/widget_test.dart
```

## 📦 Build & Deploy

### Android APK
```bash
flutter build apk --release
```

### Web Deployment
```bash
flutter build web --release
```

### Play Store Bundle
```bash
flutter build appbundle --release
```

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch (`git checkout -b feature/amazing-feature`)
3. Commit your changes (`git commit -m 'Add amazing feature'`)
4. Push to the branch (`git push origin feature/amazing-feature`)
5. Open a Pull Request

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 👨‍💻 Author

**Nehemiah Andrew**
- Email: <EMAIL>
- GitHub: [@nehemiahandrew78](https://github.com/nehemiahandrew78)

## 🙏 Acknowledgments

- Nigerian Armed Forces for inspiration
- Flutter and Supabase communities
- All contributors and testers
- Military aspirants across Nigeria

## 📞 Support

For support and questions:
- Create an issue on GitHub
- Email: <EMAIL>
- Join our community discussions

---

**Made with ❤️ for Nigerian military aspirants**
