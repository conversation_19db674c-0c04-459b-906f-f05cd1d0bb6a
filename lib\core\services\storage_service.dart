import 'dart:typed_data';
import 'package:flutter/foundation.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import 'package:fit_4_force/core/config/supabase_config.dart';
import 'package:logger/logger.dart';
import 'package:image/image.dart' as img;

/// Service for managing file storage in Supabase
class StorageService {
  static final StorageService _instance = StorageService._internal();
  factory StorageService() => _instance;
  StorageService._internal();

  final Logger _logger = Logger();

  /// Get Supabase client
  SupabaseClient get _client => SupabaseConfig.client;

  /// Storage bucket names
  static const String profileImagesBucket = 'profile-images';
  static const String workoutImagesBucket = 'workout-images';
  static const String postImagesBucket = 'post-images';
  static const String exerciseVideosBucket = 'exercise-videos';
  static const String documentsBucket = 'documents';
  static const String studyMaterialsBucket = 'study-materials';

  /// Initialize storage buckets
  Future<void> initializeBuckets() async {
    final buckets = [
      profileImagesBucket,
      workoutImagesBucket,
      postImagesBucket,
      exerciseVideosBucket,
      documentsBucket,
      studyMaterialsBucket,
    ];

    for (final bucketName in buckets) {
      try {
        await _createBucketIfNotExists(bucketName);
      } catch (e) {
        _logger.e('❌ Error initializing bucket $bucketName: $e');
      }
    }
  }

  /// Create bucket if it doesn't exist
  Future<void> _createBucketIfNotExists(String bucketName) async {
    try {
      final buckets = await _client.storage.listBuckets();
      final bucketExists = buckets.any((bucket) => bucket.name == bucketName);

      if (!bucketExists) {
        await _client.storage.createBucket(
          bucketName,
          BucketOptions(
            public:
                bucketName == profileImagesBucket ||
                bucketName == workoutImagesBucket,
            allowedMimeTypes: _getAllowedMimeTypes(bucketName),
          ),
        );
        _logger.i('✅ Created storage bucket: $bucketName');
      }
    } catch (e) {
      _logger.w(
        '⚠️ Bucket $bucketName might already exist or error occurred: $e',
      );
    }
  }

  /// Get allowed MIME types for bucket
  List<String> _getAllowedMimeTypes(String bucketName) {
    switch (bucketName) {
      case profileImagesBucket:
      case workoutImagesBucket:
      case postImagesBucket:
        return ['image/jpeg', 'image/png', 'image/webp'];
      case exerciseVideosBucket:
        return ['video/mp4', 'video/webm', 'video/quicktime'];
      case documentsBucket:
      case studyMaterialsBucket:
        return [
          'application/pdf',
          'text/plain',
          'application/msword',
          'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
        ];
      default:
        return ['*/*'];
    }
  }

  /// Get file size limit for bucket (in bytes)
  int _getFileSizeLimit(String bucketName) {
    switch (bucketName) {
      case profileImagesBucket:
        return 5 * 1024 * 1024; // 5MB
      case workoutImagesBucket:
      case postImagesBucket:
        return 10 * 1024 * 1024; // 10MB
      case exerciseVideosBucket:
        return 100 * 1024 * 1024; // 100MB
      case documentsBucket:
      case studyMaterialsBucket:
        return 50 * 1024 * 1024; // 50MB
      default:
        return 10 * 1024 * 1024; // 10MB default
    }
  }

  /// Upload profile image
  Future<String?> uploadProfileImage(
    String userId,
    Uint8List imageData,
    String fileName,
  ) async {
    try {
      // Compress image for profile
      final compressedData = await _compressImage(
        imageData,
        maxWidth: 400,
        maxHeight: 400,
        quality: 85,
      );

      final filePath =
          'users/$userId/profile/${DateTime.now().millisecondsSinceEpoch}_$fileName';

      await _client.storage
          .from(profileImagesBucket)
          .uploadBinary(
            filePath,
            compressedData,
            fileOptions: FileOptions(cacheControl: '3600', upsert: true),
          );

      final publicUrl = _client.storage
          .from(profileImagesBucket)
          .getPublicUrl(filePath);
      _logger.i('✅ Profile image uploaded: $filePath');
      return publicUrl;
    } catch (e) {
      _logger.e('❌ Error uploading profile image: $e');
      return null;
    }
  }

  /// Upload workout image
  Future<String?> uploadWorkoutImage(
    String workoutId,
    Uint8List imageData,
    String fileName,
  ) async {
    try {
      // Compress image for workout
      final compressedData = await _compressImage(
        imageData,
        maxWidth: 800,
        maxHeight: 600,
        quality: 80,
      );

      final filePath =
          'workouts/$workoutId/${DateTime.now().millisecondsSinceEpoch}_$fileName';

      await _client.storage
          .from(workoutImagesBucket)
          .uploadBinary(
            filePath,
            compressedData,
            fileOptions: FileOptions(cacheControl: '3600', upsert: true),
          );

      final publicUrl = _client.storage
          .from(workoutImagesBucket)
          .getPublicUrl(filePath);
      _logger.i('✅ Workout image uploaded: $filePath');
      return publicUrl;
    } catch (e) {
      _logger.e('❌ Error uploading workout image: $e');
      return null;
    }
  }

  /// Upload community post image
  Future<String?> uploadPostImage(
    String postId,
    Uint8List imageData,
    String fileName,
  ) async {
    try {
      // Compress image for posts
      final compressedData = await _compressImage(
        imageData,
        maxWidth: 1200,
        maxHeight: 800,
        quality: 75,
      );

      final filePath =
          'posts/$postId/${DateTime.now().millisecondsSinceEpoch}_$fileName';

      await _client.storage
          .from(postImagesBucket)
          .uploadBinary(
            filePath,
            compressedData,
            fileOptions: FileOptions(cacheControl: '3600', upsert: true),
          );

      final publicUrl = _client.storage
          .from(postImagesBucket)
          .getPublicUrl(filePath);
      _logger.i('✅ Post image uploaded: $filePath');
      return publicUrl;
    } catch (e) {
      _logger.e('❌ Error uploading post image: $e');
      return null;
    }
  }

  /// Upload exercise video
  Future<String?> uploadExerciseVideo(
    String exerciseId,
    Uint8List videoData,
    String fileName,
  ) async {
    try {
      final filePath =
          'exercises/$exerciseId/videos/${DateTime.now().millisecondsSinceEpoch}_$fileName';

      await _client.storage
          .from(exerciseVideosBucket)
          .uploadBinary(
            filePath,
            videoData,
            fileOptions: FileOptions(cacheControl: '3600', upsert: true),
          );

      final publicUrl = _client.storage
          .from(exerciseVideosBucket)
          .getPublicUrl(filePath);
      _logger.i('✅ Exercise video uploaded: $filePath');
      return publicUrl;
    } catch (e) {
      _logger.e('❌ Error uploading exercise video: $e');
      return null;
    }
  }

  /// Upload study material document
  Future<String?> uploadStudyMaterial(
    String categoryId,
    Uint8List documentData,
    String fileName,
  ) async {
    try {
      final filePath =
          'study-materials/$categoryId/${DateTime.now().millisecondsSinceEpoch}_$fileName';

      await _client.storage
          .from(studyMaterialsBucket)
          .uploadBinary(
            filePath,
            documentData,
            fileOptions: FileOptions(cacheControl: '3600', upsert: true),
          );

      final publicUrl = _client.storage
          .from(studyMaterialsBucket)
          .getPublicUrl(filePath);
      _logger.i('✅ Study material uploaded: $filePath');
      return publicUrl;
    } catch (e) {
      _logger.e('❌ Error uploading study material: $e');
      return null;
    }
  }

  /// Compress image for optimal mobile performance
  Future<Uint8List> _compressImage(
    Uint8List imageData, {
    int maxWidth = 1200,
    int maxHeight = 800,
    int quality = 80,
  }) async {
    try {
      final image = img.decodeImage(imageData);
      if (image == null) return imageData;

      // Resize if needed
      img.Image resized = image;
      if (image.width > maxWidth || image.height > maxHeight) {
        resized = img.copyResize(
          image,
          width: image.width > maxWidth ? maxWidth : null,
          height: image.height > maxHeight ? maxHeight : null,
          maintainAspect: true,
        );
      }

      // Compress as JPEG
      final compressedData = img.encodeJpg(resized, quality: quality);

      _logger.d(
        '🗜️ Image compressed: ${imageData.length} -> ${compressedData.length} bytes',
      );
      return Uint8List.fromList(compressedData);
    } catch (e) {
      _logger.w('⚠️ Image compression failed, using original: $e');
      return imageData;
    }
  }

  /// Delete file from storage
  Future<bool> deleteFile(String bucketName, String filePath) async {
    try {
      await _client.storage.from(bucketName).remove([filePath]);
      _logger.i('✅ File deleted: $bucketName/$filePath');
      return true;
    } catch (e) {
      _logger.e('❌ Error deleting file: $e');
      return false;
    }
  }

  /// Get file download URL
  Future<String?> getDownloadUrl(String bucketName, String filePath) async {
    try {
      final response = await _client.storage
          .from(bucketName)
          .createSignedUrl(filePath, 3600); // 1 hour expiry
      return response;
    } catch (e) {
      _logger.e('❌ Error getting download URL: $e');
      return null;
    }
  }

  /// List files in a bucket path
  Future<List<FileObject>> listFiles(String bucketName, {String? path}) async {
    try {
      final files = await _client.storage.from(bucketName).list(path: path);
      return files;
    } catch (e) {
      _logger.e('❌ Error listing files: $e');
      return [];
    }
  }

  /// Get storage usage statistics
  Future<Map<String, dynamic>> getStorageStats() async {
    try {
      final buckets = [
        profileImagesBucket,
        workoutImagesBucket,
        postImagesBucket,
        exerciseVideosBucket,
        documentsBucket,
        studyMaterialsBucket,
      ];

      final stats = <String, dynamic>{};

      for (final bucket in buckets) {
        try {
          final files = await listFiles(bucket);
          stats[bucket] = {
            'file_count': files.length,
            'total_size': files.fold<int>(
              0,
              (sum, file) => sum + ((file.metadata?['size'] as int?) ?? 0),
            ),
          };
        } catch (e) {
          stats[bucket] = {'error': e.toString()};
        }
      }

      return stats;
    } catch (e) {
      _logger.e('❌ Error getting storage stats: $e');
      return {};
    }
  }
}
