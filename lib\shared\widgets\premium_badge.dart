import 'package:flutter/material.dart';
import 'package:fit_4_force/core/theme/app_theme.dart';

/// A widget that displays a premium badge
class PremiumBadge extends StatelessWidget {
  /// Whether to show the badge in a small size
  final bool isSmall;

  /// Constructor
  const PremiumBadge({
    super.key,
    this.isSmall = false,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: EdgeInsets.symmetric(
        horizontal: isSmall ? 4 : 6,
        vertical: isSmall ? 2 : 3,
      ),
      decoration: BoxDecoration(
        color: AppTheme.premiumColor.withAlpha(51), // 0.2 opacity
        borderRadius: BorderRadius.circular(isSmall ? 4 : 6),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(
            Icons.workspace_premium,
            size: isSmall ? 10 : 14,
            color: AppTheme.premiumColor,
          ),
          SizedBox(width: isSmall ? 2 : 4),
          Text(
            'PREMIUM',
            style: TextStyle(
              fontSize: isSmall ? 8 : 10,
              fontWeight: FontWeight.bold,
              color: AppTheme.premiumColor,
            ),
          ),
        ],
      ),
    );
  }
}
