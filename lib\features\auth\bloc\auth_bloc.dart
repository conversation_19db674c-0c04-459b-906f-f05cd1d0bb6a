import 'package:fit_4_force/shared/bloc/base_bloc.dart';
import 'package:fit_4_force/shared/models/user_model.dart';
import 'package:fit_4_force/core/services/supabase_auth_service.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

// Events
abstract class AuthEvent extends BaseEvent {}

class AuthenticatedEvent extends AuthEvent {
  final UserModel user;

  AuthenticatedEvent(this.user);

  @override
  List<Object?> get props => [user];
}

class SignUpEvent extends AuthEvent {
  final String email;
  final String password;
  final String fullName;
  final int age;
  final String gender;
  final double height;
  final double weight;
  final String targetAgency;
  final String fitnessGoal;
  final Map<String, bool> notificationPreferences;

  SignUpEvent({
    required this.email,
    required this.password,
    required this.fullName,
    required this.age,
    required this.gender,
    required this.height,
    required this.weight,
    required this.targetAgency,
    required this.fitnessGoal,
    required this.notificationPreferences,
  });

  @override
  List<Object?> get props => [
    email,
    password,
    fullName,
    age,
    gender,
    height,
    weight,
    targetAgency,
    fitnessGoal,
    notificationPreferences,
  ];
}

class SignInEvent extends AuthEvent {
  final String email;
  final String password;

  SignInEvent({required this.email, required this.password});

  @override
  List<Object?> get props => [email, password];
}

class SignOutEvent extends AuthEvent {}

class ResetPasswordEvent extends AuthEvent {
  final String email;

  ResetPasswordEvent({required this.email});

  @override
  List<Object?> get props => [email];
}

class UpdateProfileImageEvent extends AuthEvent {
  final String? imageUrl;

  UpdateProfileImageEvent(this.imageUrl);

  @override
  List<Object?> get props => [imageUrl];
}

class UpdateEmailEvent extends AuthEvent {
  final String newEmail;
  final String password;

  UpdateEmailEvent({required this.newEmail, required this.password});

  @override
  List<Object?> get props => [newEmail, password];
}

class UpdateNotificationPreferencesEvent extends AuthEvent {
  final Map<String, bool> notificationPreferences;

  UpdateNotificationPreferencesEvent({required this.notificationPreferences});

  @override
  List<Object?> get props => [notificationPreferences];
}

class UpdatePasswordEvent extends AuthEvent {
  final String currentPassword;
  final String newPassword;

  UpdatePasswordEvent({
    required this.currentPassword,
    required this.newPassword,
  });

  @override
  List<Object?> get props => [currentPassword, newPassword];
}

class DeleteAccountEvent extends AuthEvent {
  final String password;

  DeleteAccountEvent({required this.password});

  @override
  List<Object?> get props => [password];
}

// States
abstract class AuthState extends BaseState {}

class AuthInitial extends AuthState {}

class AuthLoading extends AuthState {}

class Authenticated extends AuthState {
  final UserModel user;

  Authenticated(this.user);

  @override
  List<Object?> get props => [user];
}

class Unauthenticated extends AuthState {}

class AuthError extends AuthState {
  final String message;

  AuthError(this.message);

  @override
  List<Object?> get props => [message];
}

// Bloc
class AuthBloc extends BaseBloc<AuthEvent, AuthState> {
  AuthBloc() : super(AuthInitial()) {
    on<AuthenticatedEvent>(_handleAuthenticated);
    on<SignUpEvent>(_handleSignUp);
    on<SignInEvent>(_handleSignIn);
    on<SignOutEvent>(_handleSignOut);
    on<ResetPasswordEvent>(_handleResetPassword);
    on<UpdateProfileImageEvent>(_handleUpdateProfileImage);
    on<UpdateEmailEvent>(_handleUpdateEmail);
    on<UpdateNotificationPreferencesEvent>(
      _handleUpdateNotificationPreferences,
    );
    on<UpdatePasswordEvent>(_handleUpdatePassword);
    on<DeleteAccountEvent>(_handleDeleteAccount);
  }

  @override
  Future<void> handleEvent(AuthEvent event, Emitter<AuthState> emit) async {
    if (event is AuthenticatedEvent) {
      await _handleAuthenticated(event, emit);
    } else if (event is SignUpEvent) {
      await _handleSignUp(event, emit);
    } else if (event is SignInEvent) {
      await _handleSignIn(event, emit);
    } else if (event is SignOutEvent) {
      await _handleSignOut(event, emit);
    } else if (event is ResetPasswordEvent) {
      await _handleResetPassword(event, emit);
    } else if (event is UpdateProfileImageEvent) {
      await _handleUpdateProfileImage(event, emit);
    } else if (event is UpdateEmailEvent) {
      await _handleUpdateEmail(event, emit);
    } else if (event is UpdateNotificationPreferencesEvent) {
      await _handleUpdateNotificationPreferences(event, emit);
    } else if (event is UpdatePasswordEvent) {
      await _handleUpdatePassword(event, emit);
    } else if (event is DeleteAccountEvent) {
      await _handleDeleteAccount(event, emit);
    }
  }

  Future<void> _handleAuthenticated(
    AuthenticatedEvent event,
    Emitter<AuthState> emit,
  ) async {
    emit(Authenticated(event.user));
  }

  Future<void> _handleSignUp(SignUpEvent event, Emitter<AuthState> emit) async {
    try {
      emit(AuthLoading());

      // Sign up with Supabase
      final authResponse = await SupabaseAuthService.signUp(
        email: event.email,
        password: event.password,
        fullName: event.fullName,
        targetAgency: event.targetAgency,
        additionalData: {
          'age': event.age,
          'gender': event.gender,
          'height': event.height,
          'weight': event.weight,
          'fitness_goal': event.fitnessGoal,
          'notification_preferences': event.notificationPreferences,
        },
      );

      if (authResponse.user != null) {
        // Get the user profile from our custom table
        final userProfile = await SupabaseAuthService.getUserProfile(
          userId: authResponse.user!.id,
        );

        if (userProfile != null) {
          emit(Authenticated(userProfile));
        } else {
          emit(AuthError('Failed to create user profile'));
        }
      } else {
        emit(AuthError('Failed to create user account'));
      }
    } catch (e) {
      emit(AuthError(SupabaseAuthService.getErrorMessage(e)));
    }
  }

  Future<void> _handleSignIn(SignInEvent event, Emitter<AuthState> emit) async {
    try {
      emit(AuthLoading());

      // Sign in with Supabase
      final authResponse = await SupabaseAuthService.signIn(
        email: event.email,
        password: event.password,
      );

      if (authResponse.user != null) {
        // Get the user profile from our custom table
        final userProfile = await SupabaseAuthService.getUserProfile(
          userId: authResponse.user!.id,
        );

        if (userProfile != null) {
          emit(Authenticated(userProfile));
        } else {
          emit(AuthError('Failed to load user profile'));
        }
      } else {
        emit(AuthError('Failed to sign in'));
      }
    } catch (e) {
      emit(AuthError(SupabaseAuthService.getErrorMessage(e)));
    }
  }

  Future<void> _handleSignOut(
    SignOutEvent event,
    Emitter<AuthState> emit,
  ) async {
    try {
      emit(AuthLoading());
      await SupabaseAuthService.signOut();
      emit(Unauthenticated());
    } catch (e) {
      emit(AuthError(SupabaseAuthService.getErrorMessage(e)));
    }
  }

  Future<void> _handleResetPassword(
    ResetPasswordEvent event,
    Emitter<AuthState> emit,
  ) async {
    try {
      emit(AuthLoading());
      await SupabaseAuthService.resetPassword(email: event.email);
      emit(Unauthenticated());
    } catch (e) {
      emit(AuthError(SupabaseAuthService.getErrorMessage(e)));
    }
  }

  Future<void> _handleUpdateProfileImage(
    UpdateProfileImageEvent event,
    Emitter<AuthState> emit,
  ) async {
    try {
      if (state is! Authenticated) {
        return;
      }

      final currentUser = (state as Authenticated).user;

      // Update user model with new profile image URL
      final updatedUser = currentUser.copyWith(
        profileImageUrl: event.imageUrl,
        updatedAt: DateTime.now(),
      );

      // Update user in Supabase
      await SupabaseAuthService.updateUserProfile(
        userId: updatedUser.id,
        updates: {
          'profile_image_url': event.imageUrl,
          'updated_at': DateTime.now().toIso8601String(),
        },
      );

      // Emit authenticated state with updated user
      emit(Authenticated(updatedUser));
    } catch (e) {
      emit(AuthError(SupabaseAuthService.getErrorMessage(e)));
    }
  }

  Future<void> _handleUpdateEmail(
    UpdateEmailEvent event,
    Emitter<AuthState> emit,
  ) async {
    try {
      emit(AuthLoading());

      if (state is! Authenticated) {
        emit(AuthError('User not authenticated'));
        return;
      }

      final currentUser = (state as Authenticated).user;

      // Update email in Supabase Auth and Database
      await SupabaseAuthService.updateEmail(newEmail: event.newEmail);

      // Update user model with new email
      final updatedUser = currentUser.copyWith(
        email: event.newEmail,
        updatedAt: DateTime.now(),
      );

      // Emit authenticated state with updated user
      emit(Authenticated(updatedUser));
    } catch (e) {
      emit(AuthError(SupabaseAuthService.getErrorMessage(e)));
    }
  }

  Future<void> _handleUpdateNotificationPreferences(
    UpdateNotificationPreferencesEvent event,
    Emitter<AuthState> emit,
  ) async {
    try {
      if (state is! Authenticated) {
        emit(AuthError('User not authenticated'));
        return;
      }

      final currentUser = (state as Authenticated).user;

      // Update user model with new notification preferences
      final updatedUser = currentUser.copyWith(
        notificationPreferences: event.notificationPreferences,
        updatedAt: DateTime.now(),
      );

      // Update user in Supabase
      await SupabaseAuthService.updateUserProfile(
        userId: updatedUser.id,
        updates: {
          'notification_preferences': event.notificationPreferences,
          'updated_at': DateTime.now().toIso8601String(),
        },
      );

      // Emit authenticated state with updated user
      emit(Authenticated(updatedUser));
    } catch (e) {
      emit(AuthError(SupabaseAuthService.getErrorMessage(e)));
    }
  }

  Future<void> _handleUpdatePassword(
    UpdatePasswordEvent event,
    Emitter<AuthState> emit,
  ) async {
    try {
      emit(AuthLoading());

      if (state is! Authenticated) {
        emit(AuthError('User not authenticated'));
        return;
      }

      // Update password in Supabase Auth
      await SupabaseAuthService.updatePassword(newPassword: event.newPassword);

      // Password updated successfully, keep the current user state
      emit(state);
    } catch (e) {
      emit(AuthError(SupabaseAuthService.getErrorMessage(e)));
    }
  }

  Future<void> _handleDeleteAccount(
    DeleteAccountEvent event,
    Emitter<AuthState> emit,
  ) async {
    try {
      emit(AuthLoading());

      if (state is! Authenticated) {
        emit(AuthError('User not authenticated'));
        return;
      }

      // Delete account in Supabase
      await SupabaseAuthService.deleteAccount();

      // Account deleted successfully, sign out the user
      emit(Unauthenticated());
    } catch (e) {
      emit(AuthError(SupabaseAuthService.getErrorMessage(e)));
    }
  }
}
