import 'dart:async';
import 'package:supabase_flutter/supabase_flutter.dart';
import 'package:fit_4_force/core/config/supabase_config.dart';
import 'package:logger/logger.dart';

/// Service for managing real-time subscriptions in Fit4Force
class RealtimeService {
  static final RealtimeService _instance = RealtimeService._internal();
  factory RealtimeService() => _instance;
  RealtimeService._internal();

  final Logger _logger = Logger();
  final Map<String, RealtimeChannel> _activeChannels = {};
  final Map<String, StreamController> _streamControllers = {};

  /// Get Supabase client
  SupabaseClient get _client => SupabaseConfig.client;

  /// Subscribe to community posts real-time updates
  Stream<Map<String, dynamic>> subscribeToPosts() {
    const channelName = 'posts_channel';

    if (_streamControllers.containsKey(channelName)) {
      return _streamControllers[channelName]!.stream
          .cast<Map<String, dynamic>>();
    }

    final controller = StreamController<Map<String, dynamic>>.broadcast();
    _streamControllers[channelName] = controller;

    try {
      final channel =
          _client
              .channel(channelName)
              .onPostgresChanges(
                event: PostgresChangeEvent.all,
                schema: 'public',
                table: 'posts',
                callback: (payload) {
                  _logger.d('📝 Posts real-time update: ${payload.eventType}');
                  controller.add({
                    'type': 'posts',
                    'event': payload.eventType.name,
                    'data': payload.newRecord ?? payload.oldRecord ?? {},
                    'timestamp': DateTime.now().toIso8601String(),
                  });
                },
              )
              .subscribe();

      _activeChannels[channelName] = channel;
      _logger.i('✅ Subscribed to posts real-time updates');
    } catch (e) {
      _logger.e('❌ Error subscribing to posts: $e');
      controller.addError(e);
    }

    return controller.stream;
  }

  /// Subscribe to comments real-time updates
  Stream<Map<String, dynamic>> subscribeToComments(String postId) {
    final channelName = 'comments_$postId';

    if (_streamControllers.containsKey(channelName)) {
      return _streamControllers[channelName]!.stream
          .cast<Map<String, dynamic>>();
    }

    final controller = StreamController<Map<String, dynamic>>.broadcast();
    _streamControllers[channelName] = controller;

    try {
      final channel =
          _client
              .channel(channelName)
              .onPostgresChanges(
                event: PostgresChangeEvent.all,
                schema: 'public',
                table: 'comments',
                filter: PostgresChangeFilter(
                  type: PostgresChangeFilterType.eq,
                  column: 'post_id',
                  value: postId,
                ),
                callback: (payload) {
                  _logger.d(
                    '💬 Comments real-time update: ${payload.eventType}',
                  );
                  controller.add({
                    'type': 'comments',
                    'event': payload.eventType.name,
                    'data': payload.newRecord ?? payload.oldRecord,
                    'post_id': postId,
                    'timestamp': DateTime.now().toIso8601String(),
                  });
                },
              )
              .subscribe();

      _activeChannels[channelName] = channel;
      _logger.i('✅ Subscribed to comments for post $postId');
    } catch (e) {
      _logger.e('❌ Error subscribing to comments: $e');
      controller.addError(e);
    }

    return controller.stream;
  }

  /// Subscribe to likes real-time updates
  Stream<Map<String, dynamic>> subscribeToLikes(String postId) {
    final channelName = 'likes_$postId';

    if (_streamControllers.containsKey(channelName)) {
      return _streamControllers[channelName]!.stream
          .cast<Map<String, dynamic>>();
    }

    final controller = StreamController<Map<String, dynamic>>.broadcast();
    _streamControllers[channelName] = controller;

    try {
      final channel =
          _client
              .channel(channelName)
              .onPostgresChanges(
                event: PostgresChangeEvent.all,
                schema: 'public',
                table: 'likes',
                filter: PostgresChangeFilter(
                  type: PostgresChangeFilterType.eq,
                  column: 'post_id',
                  value: postId,
                ),
                callback: (payload) {
                  _logger.d('❤️ Likes real-time update: ${payload.eventType}');
                  controller.add({
                    'type': 'likes',
                    'event': payload.eventType.name,
                    'data': payload.newRecord ?? payload.oldRecord,
                    'post_id': postId,
                    'timestamp': DateTime.now().toIso8601String(),
                  });
                },
              )
              .subscribe();

      _activeChannels[channelName] = channel;
      _logger.i('✅ Subscribed to likes for post $postId');
    } catch (e) {
      _logger.e('❌ Error subscribing to likes: $e');
      controller.addError(e);
    }

    return controller.stream;
  }

  /// Subscribe to workout progress real-time updates
  Stream<Map<String, dynamic>> subscribeToWorkoutProgress(String userId) {
    final channelName = 'workout_progress_$userId';

    if (_streamControllers.containsKey(channelName)) {
      return _streamControllers[channelName]!.stream
          .cast<Map<String, dynamic>>();
    }

    final controller = StreamController<Map<String, dynamic>>.broadcast();
    _streamControllers[channelName] = controller;

    try {
      final channel =
          _client
              .channel(channelName)
              .onPostgresChanges(
                event: PostgresChangeEvent.all,
                schema: 'public',
                table: 'user_workouts',
                filter: PostgresChangeFilter(
                  type: PostgresChangeFilterType.eq,
                  column: 'user_id',
                  value: userId,
                ),
                callback: (payload) {
                  _logger.d(
                    '🏋️ Workout progress update: ${payload.eventType}',
                  );
                  controller.add({
                    'type': 'workout_progress',
                    'event': payload.eventType.name,
                    'data': payload.newRecord ?? payload.oldRecord,
                    'user_id': userId,
                    'timestamp': DateTime.now().toIso8601String(),
                  });
                },
              )
              .subscribe();

      _activeChannels[channelName] = channel;
      _logger.i('✅ Subscribed to workout progress for user $userId');
    } catch (e) {
      _logger.e('❌ Error subscribing to workout progress: $e');
      controller.addError(e);
    }

    return controller.stream;
  }

  /// Subscribe to leaderboard updates
  Stream<Map<String, dynamic>> subscribeToLeaderboard() {
    const channelName = 'leaderboard_channel';

    if (_streamControllers.containsKey(channelName)) {
      return _streamControllers[channelName]!.stream
          .cast<Map<String, dynamic>>();
    }

    final controller = StreamController<Map<String, dynamic>>.broadcast();
    _streamControllers[channelName] = controller;

    try {
      final channel =
          _client
              .channel(channelName)
              .onPostgresChanges(
                event: PostgresChangeEvent.all,
                schema: 'public',
                table: 'progress',
                callback: (payload) {
                  _logger.d('🏆 Leaderboard update: ${payload.eventType}');
                  controller.add({
                    'type': 'leaderboard',
                    'event': payload.eventType.name,
                    'data': payload.newRecord ?? payload.oldRecord,
                    'timestamp': DateTime.now().toIso8601String(),
                  });
                },
              )
              .subscribe();

      _activeChannels[channelName] = channel;
      _logger.i('✅ Subscribed to leaderboard updates');
    } catch (e) {
      _logger.e('❌ Error subscribing to leaderboard: $e');
      controller.addError(e);
    }

    return controller.stream;
  }

  /// Subscribe to study group messages
  Stream<Map<String, dynamic>> subscribeToStudyGroupMessages(String groupId) {
    final channelName = 'study_group_$groupId';

    if (_streamControllers.containsKey(channelName)) {
      return _streamControllers[channelName]!.stream
          .cast<Map<String, dynamic>>();
    }

    final controller = StreamController<Map<String, dynamic>>.broadcast();
    _streamControllers[channelName] = controller;

    try {
      final channel =
          _client
              .channel(channelName)
              .onPostgresChanges(
                event: PostgresChangeEvent.all,
                schema: 'public',
                table: 'study_group_messages',
                filter: PostgresChangeFilter(
                  type: PostgresChangeFilterType.eq,
                  column: 'group_id',
                  value: groupId,
                ),
                callback: (payload) {
                  _logger.d('📚 Study group message: ${payload.eventType}');
                  controller.add({
                    'type': 'study_group_messages',
                    'event': payload.eventType.name,
                    'data': payload.newRecord ?? payload.oldRecord,
                    'group_id': groupId,
                    'timestamp': DateTime.now().toIso8601String(),
                  });
                },
              )
              .subscribe();

      _activeChannels[channelName] = channel;
      _logger.i('✅ Subscribed to study group $groupId messages');
    } catch (e) {
      _logger.e('❌ Error subscribing to study group messages: $e');
      controller.addError(e);
    }

    return controller.stream;
  }

  /// Unsubscribe from a specific channel
  Future<void> unsubscribe(String channelName) async {
    try {
      if (_activeChannels.containsKey(channelName)) {
        await _activeChannels[channelName]!.unsubscribe();
        _activeChannels.remove(channelName);
        _logger.i('✅ Unsubscribed from $channelName');
      }

      if (_streamControllers.containsKey(channelName)) {
        await _streamControllers[channelName]!.close();
        _streamControllers.remove(channelName);
      }
    } catch (e) {
      _logger.e('❌ Error unsubscribing from $channelName: $e');
    }
  }

  /// Unsubscribe from all channels (cleanup)
  Future<void> unsubscribeAll() async {
    _logger.i('🧹 Cleaning up all real-time subscriptions...');

    final channelNames = List<String>.from(_activeChannels.keys);
    for (final channelName in channelNames) {
      await unsubscribe(channelName);
    }

    _logger.i('✅ All real-time subscriptions cleaned up');
  }

  /// Get active subscription count
  int get activeSubscriptionCount => _activeChannels.length;

  /// Check if a specific channel is active
  bool isChannelActive(String channelName) =>
      _activeChannels.containsKey(channelName);
}
