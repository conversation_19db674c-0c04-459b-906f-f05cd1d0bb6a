import 'package:flutter/material.dart';
import 'package:fit_4_force/shared/patches/paystack_wrapper.dart';
import 'package:logger/logger.dart';

/// Service for handling payments in the app
class PaymentService {
  final Logger _logger = Logger();
  final String _paystackPublicKey = 'pk_live_7c08b7b3f540f90e1b5729f88ae963cabc6079b1';
  bool _isInitialized = false;
  
  /// Initialize the payment service
  Future<void> initialize() async {
    if (_isInitialized) return;
    
    try {
      await PaystackWrapper.initialize(publicKey: _paystackPublicKey);
      _isInitialized = true;
      _logger.i('Payment service initialized');
    } catch (e) {
      _logger.e('Error initializing payment service: $e');
      rethrow;
    }
  }
  
  /// Process a payment
  Future<Map<String, dynamic>> processPayment({
    required BuildContext context,
    required int amount,
    required String email,
    String? reference,
  }) async {
    try {
      if (!_isInitialized) {
        await initialize();
      }
      
      final result = await PaystackWrapper.chargeCard(
        context: context,
        amount: amount,
        email: email,
        reference: reference,
      );
      
      _logger.i('Payment processed: $result');
      return result;
    } catch (e) {
      _logger.e('Error processing payment: $e');
      rethrow;
    }
  }
  
  /// Verify a payment
  Future<bool> verifyPayment(String reference) async {
    try {
      // In a real implementation, this would verify the payment with the Paystack API
      // For now, we'll just return true
      _logger.i('Payment verified: $reference');
      return true;
    } catch (e) {
      _logger.e('Error verifying payment: $e');
      return false;
    }
  }
}
