import 'package:flutter/material.dart';
import 'package:fit_4_force/shared/widgets/user_file_upload_widget.dart';
import 'package:fit_4_force/core/theme/app_theme.dart';
import 'package:fit_4_force/shared/models/user_model.dart';

class SimpleProfileScreen extends StatefulWidget {
  final UserModel user;

  const SimpleProfileScreen({
    super.key,
    required this.user,
  });

  @override
  State<SimpleProfileScreen> createState() => _SimpleProfileScreenState();
}

class _SimpleProfileScreenState extends State<SimpleProfileScreen> {
  late UserModel _currentUser;
  bool _isEditingProfile = false;

  @override
  void initState() {
    super.initState();
    _currentUser = widget.user;
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Profile'),
        backgroundColor: AppTheme.primaryColor,
        foregroundColor: Colors.white,
        actions: [
          IconButton(
            icon: Icon(_isEditingProfile ? Icons.save : Icons.edit),
            onPressed: () {
              setState(() {
                _isEditingProfile = !_isEditingProfile;
              });
            },
          ),
        ],
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(24),
        child: Column(
          children: [
            // Profile Image Section
            _buildProfileImageSection(),
            
            const SizedBox(height: 32),
            
            // User Information
            _buildUserInformationCard(),
            
            const SizedBox(height: 24),
            
            // Military Preferences
            _buildMilitaryPreferencesCard(),
          ],
        ),
      ),
    );
  }

  Widget _buildProfileImageSection() {
    return Center(
      child: Stack(
        children: [
          // Profile Image
          Container(
            width: 150,
            height: 150,
            decoration: BoxDecoration(
              shape: BoxShape.circle,
              border: Border.all(
                color: AppTheme.primaryColor,
                width: 4,
              ),
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withValues(alpha: 0.1),
                  blurRadius: 20,
                  offset: const Offset(0, 10),
                ),
              ],
            ),
            child: ClipOval(
              child: _currentUser.profileImageUrl != null
                  ? Image.network(
                      _currentUser.profileImageUrl!,
                      fit: BoxFit.cover,
                      width: 150,
                      height: 150,
                      errorBuilder: (context, error, stackTrace) => _buildProfilePlaceholder(),
                    )
                  : _buildProfilePlaceholder(),
            ),
          ),
          
          // Edit Button (only in edit mode)
          if (_isEditingProfile)
            Positioned(
              bottom: 0,
              right: 0,
              child: Container(
                decoration: BoxDecoration(
                  color: AppTheme.primaryColor,
                  shape: BoxShape.circle,
                  boxShadow: [
                    BoxShadow(
                      color: Colors.black.withValues(alpha: 0.2),
                      blurRadius: 8,
                      offset: const Offset(0, 4),
                    ),
                  ],
                ),
                child: IconButton(
                  icon: const Icon(Icons.camera_alt, color: Colors.white),
                  onPressed: _showProfileImageUploadDialog,
                ),
              ),
            ),
        ],
      ),
    );
  }

  Widget _buildProfilePlaceholder() {
    return Container(
      width: 150,
      height: 150,
      color: Colors.grey.shade200,
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.person,
            size: 60,
            color: Colors.grey.shade400,
          ),
          const SizedBox(height: 8),
          Text(
            _currentUser.fullName.isNotEmpty 
                ? _currentUser.fullName.substring(0, 1).toUpperCase()
                : 'U',
            style: TextStyle(
              fontSize: 32,
              fontWeight: FontWeight.bold,
              color: Colors.grey.shade600,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildUserInformationCard() {
    return Card(
      elevation: 2,
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                const Icon(Icons.person, color: AppTheme.primaryColor),
                const SizedBox(width: 8),
                Text(
                  'Personal Information',
                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            
            _buildInfoRow('Full Name', _currentUser.fullName),
            _buildInfoRow('Email', _currentUser.email),
            _buildInfoRow('Phone', 'Not provided'),
            _buildInfoRow('Date of Birth', 'Not provided'),
            _buildInfoRow('State of Origin', 'Not provided'),
          ],
        ),
      ),
    );
  }

  Widget _buildMilitaryPreferencesCard() {
    return Card(
      elevation: 2,
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                const Icon(Icons.military_tech, color: AppTheme.primaryColor),
                const SizedBox(width: 8),
                Text(
                  'Military Preferences',
                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            
            _buildInfoRow('Target Agency', _currentUser.targetAgency),
            _buildInfoRow('Subscription', _currentUser.isPremium ? 'Premium' : 'Free'),
            _buildInfoRow('Member Since', _formatDate(_currentUser.createdAt)),
          ],
        ),
      ),
    );
  }

  Widget _buildInfoRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 8),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 120,
            child: Text(
              label,
              style: TextStyle(
                fontWeight: FontWeight.w500,
                color: Colors.grey[600],
              ),
            ),
          ),
          Expanded(
            child: Text(
              value,
              style: const TextStyle(
                fontWeight: FontWeight.w400,
              ),
            ),
          ),
        ],
      ),
    );
  }

  void _showProfileImageUploadDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Update Profile Picture'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            const Text('Choose a new profile picture'),
            const SizedBox(height: 20),
            UserFileUploadWidget(
              uploadType: UserUploadType.profileImage,
              entityId: _currentUser.id,
              buttonText: 'Select Image',
              onUploadComplete: (url) {
                if (url != null) {
                  setState(() {
                    _currentUser = _currentUser.copyWith(profileImageUrl: url);
                  });
                  Navigator.pop(context);
                  ScaffoldMessenger.of(context).showSnackBar(
                    const SnackBar(
                      content: Text('Profile picture updated successfully!'),
                      backgroundColor: Colors.green,
                    ),
                  );
                }
              },
              onUploadError: (error) {
                ScaffoldMessenger.of(context).showSnackBar(
                  SnackBar(
                    content: Text('Upload failed: $error'),
                    backgroundColor: Colors.red,
                  ),
                );
              },
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Cancel'),
          ),
        ],
      ),
    );
  }

  String _formatDate(DateTime? date) {
    if (date == null) return 'Unknown';
    return '${date.day}/${date.month}/${date.year}';
  }
}
