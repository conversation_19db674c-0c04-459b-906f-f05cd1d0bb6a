import 'package:flutter/foundation.dart';
import 'package:logger/logger.dart';
import 'package:supabase_flutter/supabase_flutter.dart';

/// Centralized error handling service for the Fit4Force app
class ErrorHandlingService {
  static final ErrorHandlingService _instance = ErrorHandlingService._internal();
  factory ErrorHandlingService() => _instance;
  ErrorHandlingService._internal();

  final Logger _logger = Logger();

  /// Handle Supabase errors with user-friendly messages
  String handleSupabaseError(dynamic error) {
    if (error is AuthException) {
      return _handleAuthError(error);
    } else if (error is PostgrestException) {
      return _handlePostgrestError(error);
    } else if (error is StorageException) {
      return _handleStorageError(error);
    } else {
      _logger.e('❌ Unknown Supabase error: $error');
      return 'An unexpected error occurred. Please try again.';
    }
  }

  /// Handle authentication errors
  String _handleAuthError(AuthException error) {
    _logger.e('🔐 Auth error: ${error.message}');
    
    switch (error.message.toLowerCase()) {
      case 'invalid login credentials':
        return 'Invalid email or password. Please check your credentials.';
      case 'email not confirmed':
        return 'Please verify your email address before signing in.';
      case 'user not found':
        return 'No account found with this email address.';
      case 'weak password':
        return 'Password is too weak. Please choose a stronger password.';
      case 'email already registered':
        return 'An account with this email already exists.';
      case 'signup disabled':
        return 'New registrations are currently disabled.';
      default:
        return error.message.isNotEmpty 
            ? error.message 
            : 'Authentication failed. Please try again.';
    }
  }

  /// Handle database errors
  String _handlePostgrestError(PostgrestException error) {
    _logger.e('🗄️ Database error: ${error.message}');
    
    if (error.code == '23505') {
      return 'This record already exists.';
    } else if (error.code == '23503') {
      return 'Cannot delete this record as it is being used elsewhere.';
    } else if (error.code == '42501') {
      return 'You do not have permission to perform this action.';
    } else if (error.code == 'PGRST116') {
      return 'No data found matching your request.';
    } else {
      return error.message.isNotEmpty 
          ? error.message 
          : 'Database operation failed. Please try again.';
    }
  }

  /// Handle storage errors
  String _handleStorageError(StorageException error) {
    _logger.e('📁 Storage error: ${error.message}');
    
    switch (error.message.toLowerCase()) {
      case 'file too large':
        return 'File is too large. Please choose a smaller file.';
      case 'invalid file type':
        return 'File type not supported. Please choose a different file.';
      case 'bucket not found':
        return 'Storage location not found. Please contact support.';
      case 'object not found':
        return 'File not found. It may have been deleted.';
      default:
        return error.message.isNotEmpty 
            ? error.message 
            : 'File operation failed. Please try again.';
    }
  }

  /// Handle network connectivity errors
  String handleNetworkError(dynamic error) {
    _logger.e('🌐 Network error: $error');
    
    final errorString = error.toString().toLowerCase();
    
    if (errorString.contains('no internet') || 
        errorString.contains('network unreachable')) {
      return 'No internet connection. Please check your network and try again.';
    } else if (errorString.contains('timeout')) {
      return 'Request timed out. Please check your connection and try again.';
    } else if (errorString.contains('host lookup failed')) {
      return 'Cannot connect to server. Please check your internet connection.';
    } else {
      return 'Network error occurred. Please try again.';
    }
  }

  /// Handle file operation errors
  String handleFileError(dynamic error) {
    _logger.e('📄 File error: $error');
    
    final errorString = error.toString().toLowerCase();
    
    if (errorString.contains('permission denied')) {
      return 'Permission denied. Please check file permissions.';
    } else if (errorString.contains('file not found')) {
      return 'File not found. Please select a valid file.';
    } else if (errorString.contains('no space left')) {
      return 'Not enough storage space. Please free up some space.';
    } else {
      return 'File operation failed. Please try again.';
    }
  }

  /// Handle payment errors
  String handlePaymentError(dynamic error) {
    _logger.e('💳 Payment error: $error');
    
    final errorString = error.toString().toLowerCase();
    
    if (errorString.contains('card declined')) {
      return 'Payment declined. Please check your card details or try another card.';
    } else if (errorString.contains('insufficient funds')) {
      return 'Insufficient funds. Please check your account balance.';
    } else if (errorString.contains('expired card')) {
      return 'Card has expired. Please use a valid card.';
    } else if (errorString.contains('invalid card')) {
      return 'Invalid card details. Please check and try again.';
    } else {
      return 'Payment failed. Please try again or contact support.';
    }
  }

  /// Log error for debugging (development only)
  void logError(String context, dynamic error, [StackTrace? stackTrace]) {
    if (kDebugMode) {
      _logger.e('❌ Error in $context: $error', error: error, stackTrace: stackTrace);
    }
  }

  /// Log warning
  void logWarning(String context, String message) {
    _logger.w('⚠️ Warning in $context: $message');
  }

  /// Log info
  void logInfo(String context, String message) {
    _logger.i('ℹ️ Info in $context: $message');
  }

  /// Get user-friendly error message for any error
  String getUserFriendlyMessage(dynamic error) {
    if (error is AuthException || error is PostgrestException || error is StorageException) {
      return handleSupabaseError(error);
    } else if (error.toString().contains('network') || 
               error.toString().contains('internet') ||
               error.toString().contains('connection')) {
      return handleNetworkError(error);
    } else if (error.toString().contains('file') || 
               error.toString().contains('storage')) {
      return handleFileError(error);
    } else if (error.toString().contains('payment') || 
               error.toString().contains('card')) {
      return handlePaymentError(error);
    } else {
      logError('Unknown error', error);
      return 'An unexpected error occurred. Please try again.';
    }
  }

  /// Check if error is recoverable (user can retry)
  bool isRecoverableError(dynamic error) {
    if (error is AuthException) {
      // Most auth errors are not recoverable without user action
      return false;
    } else if (error is PostgrestException) {
      // Database constraint errors are usually not recoverable
      return !['23505', '23503', '42501'].contains(error.code);
    } else if (error is StorageException) {
      // File size/type errors are not recoverable
      return !error.message.toLowerCase().contains('file too large') &&
             !error.message.toLowerCase().contains('invalid file type');
    } else {
      // Network errors are usually recoverable
      final errorString = error.toString().toLowerCase();
      return errorString.contains('network') || 
             errorString.contains('timeout') ||
             errorString.contains('connection');
    }
  }

  /// Get retry delay based on error type
  Duration getRetryDelay(dynamic error, int attemptNumber) {
    // Exponential backoff with jitter
    final baseDelay = Duration(seconds: 2 * attemptNumber);
    final jitter = Duration(milliseconds: (baseDelay.inMilliseconds * 0.1).round());
    
    if (error.toString().contains('rate limit')) {
      // Longer delay for rate limit errors
      return Duration(seconds: 30 * attemptNumber) + jitter;
    } else if (error.toString().contains('server error')) {
      // Medium delay for server errors
      return Duration(seconds: 10 * attemptNumber) + jitter;
    } else {
      // Standard delay for other errors
      return baseDelay + jitter;
    }
  }
}

/// Extension for easier error handling in widgets
extension ErrorHandlingExtension on dynamic {
  String get userFriendlyMessage => ErrorHandlingService().getUserFriendlyMessage(this);
  bool get isRecoverable => ErrorHandlingService().isRecoverableError(this);
}
