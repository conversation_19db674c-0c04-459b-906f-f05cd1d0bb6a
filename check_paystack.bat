@echo off
echo Checking Flutter Paystack package...

set PAYSTACK_PATH=%LOCALAPPDATA%\Pub\Cache\git\flutter_paystack-a4a33c3dd0a12f46d655a2e63d11e9f20ba82d01

if exist "%PAYSTACK_PATH%" (
    echo Found Flutter Paystack package at %PAYSTACK_PATH%
    
    echo Checking checkout_widget.dart...
    if exist "%PAYSTACK_PATH%\lib\src\widgets\checkout\checkout_widget.dart" (
        echo File exists: %PAYSTACK_PATH%\lib\src\widgets\checkout\checkout_widget.dart
        type "%PAYSTACK_PATH%\lib\src\widgets\checkout\checkout_widget.dart" | findstr "bodyText1"
        type "%PAYSTACK_PATH%\lib\src\widgets\checkout\checkout_widget.dart" | findstr "vsync: this"
    ) else (
        echo File not found: %PAYSTACK_PATH%\lib\src\widgets\checkout\checkout_widget.dart
    )
    
    echo Checking buttons.dart...
    if exist "%PAYSTACK_PATH%\lib\src\widgets\buttons.dart" (
        echo File exists: %PAYSTACK_PATH%\lib\src\widgets\buttons.dart
        type "%PAYSTACK_PATH%\lib\src\widgets\buttons.dart" | findstr "accentColor"
    ) else (
        echo File not found: %PAYSTACK_PATH%\lib\src\widgets\buttons.dart
    )
) else (
    echo Could not find Flutter Paystack package at %PAYSTACK_PATH%
)

echo Done.
