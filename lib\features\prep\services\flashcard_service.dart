import 'package:flutter/material.dart';
import 'package:fit_4_force/features/prep/models/flashcard_model.dart';
import 'package:fit_4_force/core/theme/app_theme.dart';

class FlashcardService {
  // Singleton pattern
  static final FlashcardService _instance = FlashcardService._internal();
  
  factory FlashcardService() {
    return _instance;
  }
  
  FlashcardService._internal();
  
  // Sample flashcard categories
  final List<Map<String, dynamic>> _categories = [
    {
      'name': 'General Knowledge',
      'color': Colors.blue,
      'icon': Icons.lightbulb_outline,
    },
    {
      'name': 'Mathematics',
      'color': Colors.green,
      'icon': Icons.calculate,
    },
    {
      'name': 'English',
      'color': Colors.purple,
      'icon': Icons.menu_book,
    },
    {
      'name': 'Current Affairs',
      'color': Colors.orange,
      'icon': Icons.public,
    },
    {
      'name': 'Physical Training',
      'color': Colors.red,
      'icon': Icons.fitness_center,
    },
    {
      'name': 'Agency Specific',
      'color': AppTheme.primaryColor,
      'icon': Icons.shield,
    },
  ];
  
  // Sample flashcards
  final List<FlashcardModel> _flashcards = [
    FlashcardModel(
      id: '1',
      question: 'What is the capital of Nigeria?',
      answer: 'Abuja',
      category: 'General Knowledge',
      color: Colors.blue,
      createdAt: DateTime.now().subtract(const Duration(days: 30)),
      lastReviewed: DateTime.now().subtract(const Duration(days: 5)),
      confidenceLevel: 4,
      tags: ['Geography', 'Nigeria'],
    ),
    FlashcardModel(
      id: '2',
      question: 'Who is the current President of Nigeria?',
      answer: 'Bola Ahmed Tinubu',
      category: 'Current Affairs',
      color: Colors.orange,
      createdAt: DateTime.now().subtract(const Duration(days: 20)),
      lastReviewed: DateTime.now().subtract(const Duration(days: 2)),
      confidenceLevel: 3,
      tags: ['Politics', 'Nigeria'],
    ),
    FlashcardModel(
      id: '3',
      question: 'What is the formula for calculating the area of a circle?',
      answer: 'A = πr²',
      category: 'Mathematics',
      color: Colors.green,
      createdAt: DateTime.now().subtract(const Duration(days: 15)),
      lastReviewed: DateTime.now().subtract(const Duration(days: 7)),
      confidenceLevel: 2,
      tags: ['Geometry', 'Formulas'],
    ),
    FlashcardModel(
      id: '4',
      question: 'What is the difference between "affect" and "effect"?',
      answer: '"Affect" is usually a verb meaning to influence something, while "effect" is usually a noun meaning the result of something.',
      category: 'English',
      color: Colors.purple,
      createdAt: DateTime.now().subtract(const Duration(days: 10)),
      lastReviewed: DateTime.now().subtract(const Duration(days: 1)),
      confidenceLevel: 3,
      tags: ['Grammar', 'Vocabulary'],
    ),
    FlashcardModel(
      id: '5',
      question: 'What is the minimum number of push-ups required for military fitness test?',
      answer: 'The minimum requirement varies by agency, but generally ranges from 30-50 push-ups in 2 minutes.',
      category: 'Physical Training',
      color: Colors.red,
      createdAt: DateTime.now().subtract(const Duration(days: 5)),
      confidenceLevel: 1,
      tags: ['Fitness', 'Requirements'],
    ),
    FlashcardModel(
      id: '6',
      question: 'What is the rank structure of the Nigerian Army?',
      answer: 'Private, Lance Corporal, Corporal, Sergeant, Staff Sergeant, Warrant Officer, Second Lieutenant, Lieutenant, Captain, Major, Lieutenant Colonel, Colonel, Brigadier General, Major General, Lieutenant General, General.',
      category: 'Agency Specific',
      color: AppTheme.primaryColor,
      createdAt: DateTime.now().subtract(const Duration(days: 2)),
      isPremium: true,
      tags: ['Army', 'Ranks'],
    ),
  ];
  
  // Get all flashcard categories
  List<Map<String, dynamic>> getAllCategories() {
    return _categories;
  }
  
  // Get all flashcards
  List<FlashcardModel> getAllFlashcards() {
    return _flashcards;
  }
  
  // Get flashcards by category
  List<FlashcardModel> getFlashcardsByCategory(String category) {
    return _flashcards.where((card) => card.category == category).toList();
  }
  
  // Get flashcards due for review
  List<FlashcardModel> getFlashcardsDueForReview() {
    return _flashcards.where((card) => card.isDueForReview()).toList();
  }
  
  // Add a new flashcard
  void addFlashcard(FlashcardModel flashcard) {
    _flashcards.add(flashcard);
  }
  
  // Update a flashcard
  void updateFlashcard(FlashcardModel flashcard) {
    final index = _flashcards.indexWhere((card) => card.id == flashcard.id);
    if (index != -1) {
      _flashcards[index] = flashcard;
    }
  }
  
  // Delete a flashcard
  void deleteFlashcard(String id) {
    _flashcards.removeWhere((card) => card.id == id);
  }
  
  // Update confidence level after review
  void updateConfidenceLevel(String id, int confidenceLevel) {
    final index = _flashcards.indexWhere((card) => card.id == id);
    if (index != -1) {
      _flashcards[index] = _flashcards[index].copyWith(
        confidenceLevel: confidenceLevel,
        lastReviewed: DateTime.now(),
      );
    }
  }
}
