import 'package:flutter/material.dart';
import 'package:fit_4_force/core/theme/app_theme.dart';
import 'package:fit_4_force/features/fitness/models/nutrition_model.dart';
import 'package:fit_4_force/shared/models/user_model.dart';

class NutritionScreen extends StatefulWidget {
  final UserModel user;

  const NutritionScreen({super.key, required this.user});

  @override
  State<NutritionScreen> createState() => _NutritionScreenState();
}

class _NutritionScreenState extends State<NutritionScreen>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;
  final TextEditingController _searchController = TextEditingController();
  String _selectedCategory = 'All';
  final List<String> _categories = [
    'All',
    'Weight Loss',
    'Muscle Gain',
    'Maintenance',
    'Performance',
  ];

  // Mock data for nutrition plans
  final List<NutritionPlanModel> _nutritionPlans = [
    NutritionPlanModel(
      id: '1',
      name: 'Military Fitness Diet',
      description: 'A balanced nutrition plan designed for military aspirants to maintain energy levels and support physical training.',
      imageUrl: 'assets/images/content/nutrition1.jpg',
      category: 'Performance',
      targetAgency: 'Nigerian Army',
      meals: [
        MealModel(
          id: '1',
          name: 'High-Protein Breakfast',
          description: 'Start your day with a protein-rich breakfast to fuel your morning training.',
          imageUrl: 'assets/images/content/breakfast.jpg',
          mealType: 'Breakfast',
          foodItems: [
            FoodItemModel(
              id: '1',
              name: 'Eggs',
              description: 'Scrambled eggs with vegetables',
              imageUrl: 'assets/images/content/eggs.jpg',
              category: 'Protein',
              quantity: 3,
              unit: 'whole',
              calories: 210,
              macros: {'protein': 18, 'carbs': 3, 'fat': 15},
            ),
            FoodItemModel(
              id: '2',
              name: 'Oatmeal',
              description: 'Cooked oatmeal with honey',
              imageUrl: 'assets/images/content/oatmeal.jpg',
              category: 'Carbs',
              quantity: 1,
              unit: 'cup',
              calories: 150,
              macros: {'protein': 5, 'carbs': 27, 'fat': 3},
            ),
          ],
          calories: 360,
          macros: {'protein': 23, 'carbs': 30, 'fat': 18},
          preparationTimeMinutes: 15,
          instructions: [
            'Scramble eggs with diced vegetables',
            'Cook oatmeal according to package instructions',
            'Add honey to taste',
          ],
        ),
      ],
      icon: Icons.restaurant,
      color: Colors.green,
      isPremium: false,
      tags: ['Military', 'Performance', 'Energy'],
      calories: 2500,
      macros: {'protein': 150, 'carbs': 300, 'fat': 70},
    ),
    NutritionPlanModel(
      id: '2',
      name: 'Navy Strength Diet',
      description: 'A high-protein nutrition plan designed for Navy aspirants focusing on strength and endurance.',
      imageUrl: 'assets/images/content/nutrition2.jpg',
      category: 'Muscle Gain',
      targetAgency: 'Navy',
      meals: [],
      icon: Icons.restaurant,
      color: Colors.blue,
      isPremium: true,
      tags: ['Navy', 'Strength', 'Protein'],
      calories: 3000,
      macros: {'protein': 180, 'carbs': 350, 'fat': 80},
    ),
    NutritionPlanModel(
      id: '3',
      name: 'Air Force Agility Diet',
      description: 'A balanced nutrition plan designed for Air Force aspirants focusing on agility and quick recovery.',
      imageUrl: 'assets/images/content/nutrition3.jpg',
      category: 'Maintenance',
      targetAgency: 'Air Force',
      meals: [],
      icon: Icons.restaurant,
      color: Colors.indigo,
      isPremium: true,
      tags: ['Air Force', 'Agility', 'Recovery'],
      calories: 2800,
      macros: {'protein': 160, 'carbs': 330, 'fat': 75},
    ),
  ];

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 3, vsync: this);
  }

  @override
  void dispose() {
    _tabController.dispose();
    _searchController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text(
          'Nutrition Guidance',
          style: TextStyle(fontWeight: FontWeight.bold),
        ),
        actions: [
          IconButton(
            icon: const Icon(Icons.search),
            onPressed: () {
              _showSearchDialog();
            },
          ),
          IconButton(
            icon: const Icon(Icons.filter_list),
            onPressed: () {
              _showFilterDialog();
            },
          ),
        ],
        bottom: TabBar(
          controller: _tabController,
          labelColor: AppTheme.primaryColor,
          unselectedLabelColor: AppTheme.textSecondaryLight,
          indicatorColor: AppTheme.primaryColor,
          indicatorWeight: 3,
          tabs: const [
            Tab(text: 'MEAL PLANS'),
            Tab(text: 'MY PLANS'),
            Tab(text: 'NUTRITION TIPS'),
          ],
        ),
      ),
      body: TabBarView(
        controller: _tabController,
        children: [
          _buildMealPlansTab(),
          _buildMyPlansTab(),
          _buildNutritionTipsTab(),
        ],
      ),
    );
  }

  Widget _buildMealPlansTab() {
    final filteredPlans = _selectedCategory == 'All'
        ? _nutritionPlans
        : _nutritionPlans
            .where((plan) => plan.category == _selectedCategory)
            .toList();

    return filteredPlans.isEmpty
        ? _buildEmptyState(
            'No nutrition plans available',
            'Check back later for new nutrition plans!',
          )
        : ListView.builder(
            padding: const EdgeInsets.all(16),
            itemCount: filteredPlans.length,
            itemBuilder: (context, index) {
              return _buildNutritionPlanCard(filteredPlans[index]);
            },
          );
  }

  Widget _buildMyPlansTab() {
    final myPlans = _nutritionPlans
        .where((plan) => plan.targetAgency == widget.user.targetAgency)
        .toList();

    return myPlans.isEmpty
        ? _buildEmptyState(
            'No personalized plans yet',
            'Select a nutrition plan to add it to your personal plans!',
          )
        : ListView.builder(
            padding: const EdgeInsets.all(16),
            itemCount: myPlans.length,
            itemBuilder: (context, index) {
              return _buildNutritionPlanCard(myPlans[index]);
            },
          );
  }

  Widget _buildNutritionTipsTab() {
    return ListView(
      padding: const EdgeInsets.all(16),
      children: [
        _buildTipCard(
          'Hydration for Military Training',
          'Proper hydration is crucial for military training. Aim to drink at least 3-4 liters of water daily, especially during intense physical activity.',
          Icons.water_drop,
          Colors.blue,
        ),
        _buildTipCard(
          'Pre-Workout Nutrition',
          'Consume a balanced meal with carbs and protein 2-3 hours before training. For example, chicken with rice and vegetables.',
          Icons.fitness_center,
          Colors.green,
        ),
        _buildTipCard(
          'Post-Workout Recovery',
          'Consume protein and carbs within 30 minutes after training to optimize recovery. A protein shake with a banana is a good option.',
          Icons.restore,
          Colors.orange,
        ),
        _buildTipCard(
          'Meal Timing for Military Aspirants',
          'Spread your calories across 4-6 smaller meals throughout the day to maintain energy levels during long training days.',
          Icons.access_time,
          Colors.purple,
        ),
        _buildTipCard(
          'Importance of Protein',
          'Military aspirants should aim for 1.6-2g of protein per kg of body weight daily to support muscle recovery and growth.',
          Icons.egg,
          Colors.amber,
        ),
      ],
    );
  }

  Widget _buildEmptyState(String title, String message) {
    return Center(
      child: Padding(
        padding: const EdgeInsets.all(24.0),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.restaurant,
              size: 80,
              color: Colors.grey.withValues(alpha: 0.3 * 255),
            ),
            const SizedBox(height: 16),
            Text(
              title,
              style: const TextStyle(
                fontSize: 20,
                fontWeight: FontWeight.bold,
                color: Colors.black87,
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 8),
            Text(
              message,
              style: TextStyle(
                fontSize: 16,
                color: Colors.grey.shade600,
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildNutritionPlanCard(NutritionPlanModel plan) {
    return Card(
      margin: const EdgeInsets.only(bottom: 16),
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(16),
      ),
      child: InkWell(
        onTap: () => _navigateToPlanDetail(plan),
        borderRadius: BorderRadius.circular(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Plan image
            Container(
              height: 150,
              width: double.infinity,
              decoration: BoxDecoration(
                borderRadius: const BorderRadius.only(
                  topLeft: Radius.circular(16),
                  topRight: Radius.circular(16),
                ),
                image: DecorationImage(
                  image: AssetImage(plan.imageUrl),
                  fit: BoxFit.cover,
                ),
              ),
              child: Stack(
                children: [
                  // Gradient overlay
                  Container(
                    decoration: BoxDecoration(
                      borderRadius: const BorderRadius.only(
                        topLeft: Radius.circular(16),
                        topRight: Radius.circular(16),
                      ),
                      gradient: LinearGradient(
                        begin: Alignment.topCenter,
                        end: Alignment.bottomCenter,
                        colors: [
                          Colors.transparent,
                          Colors.black.withValues(alpha: 0.7 * 255),
                        ],
                      ),
                    ),
                  ),
                  // Plan name and category
                  Positioned(
                    bottom: 16,
                    left: 16,
                    right: 16,
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          plan.name,
                          style: const TextStyle(
                            fontSize: 20,
                            fontWeight: FontWeight.bold,
                            color: Colors.white,
                            shadows: [
                              Shadow(
                                offset: Offset(0, 1),
                                blurRadius: 3.0,
                                color: Colors.black,
                              ),
                            ],
                          ),
                        ),
                        const SizedBox(height: 4),
                        Row(
                          children: [
                            Container(
                              padding: const EdgeInsets.symmetric(
                                horizontal: 8,
                                vertical: 2,
                              ),
                              decoration: BoxDecoration(
                                color: plan.color.withValues(alpha: 0.8 * 255),
                                borderRadius: BorderRadius.circular(12),
                              ),
                              child: Text(
                                plan.category,
                                style: const TextStyle(
                                  fontSize: 12,
                                  color: Colors.white,
                                  fontWeight: FontWeight.w500,
                                ),
                              ),
                            ),
                            const SizedBox(width: 8),
                            Container(
                              padding: const EdgeInsets.symmetric(
                                horizontal: 8,
                                vertical: 2,
                              ),
                              decoration: BoxDecoration(
                                color: _getAgencyColor(plan.targetAgency)
                                    .withValues(alpha: 0.8 * 255),
                                borderRadius: BorderRadius.circular(12),
                              ),
                              child: Text(
                                plan.targetAgency,
                                style: const TextStyle(
                                  fontSize: 12,
                                  color: Colors.white,
                                  fontWeight: FontWeight.w500,
                                ),
                              ),
                            ),
                            if (plan.isPremium) ...[
                              const SizedBox(width: 8),
                              Container(
                                padding: const EdgeInsets.symmetric(
                                  horizontal: 8,
                                  vertical: 2,
                                ),
                                decoration: BoxDecoration(
                                  color: Colors.amber.withValues(alpha: 0.8 * 255),
                                  borderRadius: BorderRadius.circular(12),
                                ),
                                child: const Text(
                                  'Premium',
                                  style: TextStyle(
                                    fontSize: 12,
                                    color: Colors.white,
                                    fontWeight: FontWeight.w500,
                                  ),
                                ),
                              ),
                            ],
                          ],
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ),
            // Plan details
            Padding(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    plan.description,
                    style: const TextStyle(
                      fontSize: 14,
                      color: Colors.black87,
                    ),
                    maxLines: 2,
                    overflow: TextOverflow.ellipsis,
                  ),
                  const SizedBox(height: 16),
                  // Macros
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceAround,
                    children: [
                      _buildMacroIndicator(
                        'Calories',
                        '${plan.calories}',
                        Icons.local_fire_department,
                        Colors.red,
                      ),
                      _buildMacroIndicator(
                        'Protein',
                        '${plan.macros['protein']?.toInt() ?? 0}g',
                        Icons.fitness_center,
                        Colors.blue,
                      ),
                      _buildMacroIndicator(
                        'Carbs',
                        '${plan.macros['carbs']?.toInt() ?? 0}g',
                        Icons.grain,
                        Colors.orange,
                      ),
                      _buildMacroIndicator(
                        'Fat',
                        '${plan.macros['fat']?.toInt() ?? 0}g',
                        Icons.opacity,
                        Colors.green,
                      ),
                    ],
                  ),
                  const SizedBox(height: 16),
                  // Tags
                  Wrap(
                    spacing: 8,
                    runSpacing: 8,
                    children: plan.tags.map((tag) {
                      return Container(
                        padding: const EdgeInsets.symmetric(
                          horizontal: 8,
                          vertical: 4,
                        ),
                        decoration: BoxDecoration(
                          color: Colors.grey.withValues(alpha: 0.1 * 255),
                          borderRadius: BorderRadius.circular(12),
                        ),
                        child: Text(
                          '#$tag',
                          style: TextStyle(
                            fontSize: 12,
                            color: Colors.grey.shade700,
                          ),
                        ),
                      );
                    }).toList(),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildMacroIndicator(
    String label,
    String value,
    IconData icon,
    Color color,
  ) {
    return Column(
      children: [
        Container(
          padding: const EdgeInsets.all(8),
          decoration: BoxDecoration(
            color: color.withValues(alpha: 0.1 * 255),
            shape: BoxShape.circle,
          ),
          child: Icon(
            icon,
            color: color,
            size: 20,
          ),
        ),
        const SizedBox(height: 4),
        Text(
          value,
          style: TextStyle(
            fontSize: 14,
            fontWeight: FontWeight.bold,
            color: color,
          ),
        ),
        Text(
          label,
          style: TextStyle(
            fontSize: 12,
            color: Colors.grey.shade600,
          ),
        ),
      ],
    );
  }

  Widget _buildTipCard(
    String title,
    String content,
    IconData icon,
    Color color,
  ) {
    return Card(
      margin: const EdgeInsets.only(bottom: 16),
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(16),
      ),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Container(
                  padding: const EdgeInsets.all(8),
                  decoration: BoxDecoration(
                    color: color.withValues(alpha: 0.1 * 255),
                    shape: BoxShape.circle,
                  ),
                  child: Icon(
                    icon,
                    color: color,
                    size: 24,
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: Text(
                    title,
                    style: const TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                      color: Colors.black87,
                    ),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 12),
            Text(
              content,
              style: const TextStyle(
                fontSize: 14,
                color: Colors.black87,
              ),
            ),
          ],
        ),
      ),
    );
  }

  void _showSearchDialog() {
    showDialog(
      context: context,
      builder: (context) {
        return AlertDialog(
          title: const Text('Search Nutrition Plans'),
          content: TextField(
            controller: _searchController,
            decoration: const InputDecoration(
              hintText: 'Enter keywords...',
              prefixIcon: Icon(Icons.search),
            ),
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.pop(context),
              child: const Text('CANCEL'),
            ),
            TextButton(
              onPressed: () {
                // Perform search
                Navigator.pop(context);
                // Update UI with search results
              },
              child: const Text('SEARCH'),
            ),
          ],
        );
      },
    );
  }

  void _showFilterDialog() {
    showDialog(
      context: context,
      builder: (context) {
        return StatefulBuilder(
          builder: (context, setState) {
            return AlertDialog(
              title: const Text('Filter Nutrition Plans'),
              content: Column(
                mainAxisSize: MainAxisSize.min,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  const Text(
                    'Category',
                    style: TextStyle(fontWeight: FontWeight.bold, fontSize: 16),
                  ),
                  const SizedBox(height: 8),
                  Wrap(
                    spacing: 8,
                    runSpacing: 8,
                    children: _categories.map((category) {
                      return ChoiceChip(
                        label: Text(category),
                        selected: _selectedCategory == category,
                        onSelected: (selected) {
                          setState(() {
                            _selectedCategory = category;
                          });
                        },
                      );
                    }).toList(),
                  ),
                ],
              ),
              actions: [
                TextButton(
                  onPressed: () => Navigator.pop(context),
                  child: const Text('CANCEL'),
                ),
                TextButton(
                  onPressed: () {
                    // Apply filters
                    Navigator.pop(context);
                    // Update UI with filtered plans
                    this.setState(() {});
                  },
                  child: const Text('APPLY'),
                ),
              ],
            );
          },
        );
      },
    );
  }

  void _navigateToPlanDetail(NutritionPlanModel plan) {
    // Navigate to plan detail screen
    if (plan.isPremium && !widget.user.isPremium) {
      _showPremiumDialog();
    } else {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('Viewing plan: ${plan.name}')),
      );
    }
  }

  void _showPremiumDialog() {
    showDialog(
      context: context,
      builder: (context) {
        return AlertDialog(
          title: const Text('Premium Content'),
          content: const Text(
            'This nutrition plan is only available to premium users. Upgrade to access all premium content!',
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.pop(context),
              child: const Text('CANCEL'),
            ),
            TextButton(
              onPressed: () {
                Navigator.pop(context);
                // Navigate to subscription screen
              },
              child: const Text('UPGRADE'),
            ),
          ],
        );
      },
    );
  }

  Color _getAgencyColor(String agency) {
    switch (agency) {
      case 'Nigerian Army':
        return Colors.green;
      case 'Navy':
        return Colors.blue;
      case 'Air Force':
        return Colors.indigo;
      case 'DSSC':
        return Colors.purple;
      case 'NDA':
        return Colors.red;
      case 'NSCDC':
        return Colors.orange;
      case 'EFCC':
        return Colors.teal;
      default:
        return Colors.grey;
    }
  }
}
