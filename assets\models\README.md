# TensorFlow Lite Models for Fit4Force App

This directory will contain TensorFlow Lite models for the Smart Learning Assistant and other AI features.

## Required Models

1. `query_understanding.tflite` - For understanding user queries in the Smart Learning Assistant
2. `content_recommender.tflite` - For recommending study content based on user needs

These models will be added as they are trained and optimized for mobile deployment.

## Development Note

During development, the app uses rule-based responses instead of actual model inference. The models will be integrated in future updates.
