import 'package:flutter/material.dart';
import 'package:fit_4_force/core/theme/app_theme.dart';
import 'package:fit_4_force/features/training/models/fitness_assessment.dart';
import 'package:fit_4_force/features/training/services/training_plan_service.dart';
import 'package:fit_4_force/shared/services/auth_service.dart';
import 'package:fit_4_force/shared/widgets/base_button.dart';
import 'package:uuid/uuid.dart';

class FitnessAssessmentScreen extends StatefulWidget {
  final String? militaryAgency;
  
  const FitnessAssessmentScreen({
    super.key,
    this.militaryAgency,
  });

  @override
  State<FitnessAssessmentScreen> createState() => _FitnessAssessmentScreenState();
}

class _FitnessAssessmentScreenState extends State<FitnessAssessmentScreen> {
  final _formKey = GlobalKey<FormState>();
  final _pushUpsController = TextEditingController();
  final _sitUpsController = TextEditingController();
  final _runTimeMinutesController = TextEditingController();
  final _runTimeSecondsController = TextEditingController();
  final _heightController = TextEditingController();
  final _weightController = TextEditingController();
  
  final TrainingPlanService _trainingService = TrainingPlanService();
  final AuthService _authService = AuthService();
  final Uuid _uuid = const Uuid();
  
  String _selectedAgency = 'Nigerian Army';
  bool _isLoading = false;
  
  final List<String> _agencies = [
    'Nigerian Army',
    'Nigerian Navy',
    'Nigerian Air Force',
    'Nigerian Police',
  ];

  @override
  void initState() {
    super.initState();
    if (widget.militaryAgency != null) {
      _selectedAgency = widget.militaryAgency!;
    }
    _loadPreviousAssessment();
  }
  
  Future<void> _loadPreviousAssessment() async {
    setState(() {
      _isLoading = true;
    });
    
    try {
      final userId = _authService.currentUserId;
      if (userId != null) {
        final assessment = await _trainingService.getLatestAssessment(userId);
        
        if (assessment != null) {
          setState(() {
            _pushUpsController.text = assessment.pushUps.toString();
            _sitUpsController.text = assessment.sitUps.toString();
            
            // Split run time into minutes and seconds
            final minutes = assessment.runTime.floor();
            final seconds = ((assessment.runTime - minutes) * 60).round();
            
            _runTimeMinutesController.text = minutes.toString();
            _runTimeSecondsController.text = seconds.toString();
            
            if (assessment.height != null) {
              _heightController.text = assessment.height!.toString();
            }
            
            if (assessment.weight != null) {
              _weightController.text = assessment.weight!.toString();
            }
            
            _selectedAgency = assessment.militaryAgency;
          });
        }
      }
    } catch (e) {
      // Handle error
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  @override
  void dispose() {
    _pushUpsController.dispose();
    _sitUpsController.dispose();
    _runTimeMinutesController.dispose();
    _runTimeSecondsController.dispose();
    _heightController.dispose();
    _weightController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Fitness Assessment'),
        backgroundColor: AppTheme.primaryColor,
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : SingleChildScrollView(
              padding: const EdgeInsets.all(16.0),
              child: Form(
                key: _formKey,
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Complete Your Fitness Assessment',
                      style: Theme.of(context).textTheme.titleLarge,
                    ),
                    const SizedBox(height: 8),
                    Text(
                      'This information will be used to create your personalized training plan.',
                      style: Theme.of(context).textTheme.bodyMedium,
                    ),
                    const SizedBox(height: 24),
                    
                    // Military Agency Selection
                    Text(
                      'Military Agency',
                      style: Theme.of(context).textTheme.titleMedium?.copyWith(
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 8),
                    DropdownButtonFormField<String>(
                      value: _selectedAgency,
                      decoration: InputDecoration(
                        border: OutlineInputBorder(
                          borderRadius: BorderRadius.circular(12),
                        ),
                        contentPadding: const EdgeInsets.symmetric(
                          horizontal: 16,
                          vertical: 16,
                        ),
                      ),
                      items: _agencies.map((agency) {
                        return DropdownMenuItem<String>(
                          value: agency,
                          child: Text(agency),
                        );
                      }).toList(),
                      onChanged: (value) {
                        if (value != null) {
                          setState(() {
                            _selectedAgency = value;
                          });
                        }
                      },
                    ),
                    const SizedBox(height: 24),
                    
                    // Physical Metrics
                    Text(
                      'Physical Metrics',
                      style: Theme.of(context).textTheme.titleMedium?.copyWith(
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 16),
                    
                    // Push-ups
                    TextFormField(
                      controller: _pushUpsController,
                      decoration: InputDecoration(
                        labelText: 'Push-ups (max in 2 minutes)',
                        border: OutlineInputBorder(
                          borderRadius: BorderRadius.circular(12),
                        ),
                        prefixIcon: const Icon(Icons.fitness_center),
                      ),
                      keyboardType: TextInputType.number,
                      validator: (value) {
                        if (value == null || value.isEmpty) {
                          return 'Please enter the number of push-ups';
                        }
                        final pushUps = int.tryParse(value);
                        if (pushUps == null || pushUps < 0) {
                          return 'Please enter a valid number';
                        }
                        return null;
                      },
                    ),
                    const SizedBox(height: 16),
                    
                    // Sit-ups
                    TextFormField(
                      controller: _sitUpsController,
                      decoration: InputDecoration(
                        labelText: 'Sit-ups (max in 2 minutes)',
                        border: OutlineInputBorder(
                          borderRadius: BorderRadius.circular(12),
                        ),
                        prefixIcon: const Icon(Icons.fitness_center),
                      ),
                      keyboardType: TextInputType.number,
                      validator: (value) {
                        if (value == null || value.isEmpty) {
                          return 'Please enter the number of sit-ups';
                        }
                        final sitUps = int.tryParse(value);
                        if (sitUps == null || sitUps < 0) {
                          return 'Please enter a valid number';
                        }
                        return null;
                      },
                    ),
                    const SizedBox(height: 16),
                    
                    // Run Time
                    Text(
                      '2.4km Run Time',
                      style: Theme.of(context).textTheme.titleSmall,
                    ),
                    const SizedBox(height: 8),
                    Row(
                      children: [
                        Expanded(
                          child: TextFormField(
                            controller: _runTimeMinutesController,
                            decoration: InputDecoration(
                              labelText: 'Minutes',
                              border: OutlineInputBorder(
                                borderRadius: BorderRadius.circular(12),
                              ),
                            ),
                            keyboardType: TextInputType.number,
                            validator: (value) {
                              if (value == null || value.isEmpty) {
                                return 'Required';
                              }
                              final minutes = int.tryParse(value);
                              if (minutes == null || minutes < 0) {
                                return 'Invalid';
                              }
                              return null;
                            },
                          ),
                        ),
                        const SizedBox(width: 16),
                        Expanded(
                          child: TextFormField(
                            controller: _runTimeSecondsController,
                            decoration: InputDecoration(
                              labelText: 'Seconds',
                              border: OutlineInputBorder(
                                borderRadius: BorderRadius.circular(12),
                              ),
                            ),
                            keyboardType: TextInputType.number,
                            validator: (value) {
                              if (value == null || value.isEmpty) {
                                return 'Required';
                              }
                              final seconds = int.tryParse(value);
                              if (seconds == null || seconds < 0 || seconds > 59) {
                                return 'Invalid';
                              }
                              return null;
                            },
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: 24),
                    
                    // Optional Metrics
                    Text(
                      'Optional Metrics',
                      style: Theme.of(context).textTheme.titleMedium?.copyWith(
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 16),
                    
                    // Height
                    TextFormField(
                      controller: _heightController,
                      decoration: InputDecoration(
                        labelText: 'Height (cm)',
                        border: OutlineInputBorder(
                          borderRadius: BorderRadius.circular(12),
                        ),
                        prefixIcon: const Icon(Icons.height),
                      ),
                      keyboardType: TextInputType.number,
                      validator: (value) {
                        if (value != null && value.isNotEmpty) {
                          final height = double.tryParse(value);
                          if (height == null || height <= 0) {
                            return 'Please enter a valid height';
                          }
                        }
                        return null;
                      },
                    ),
                    const SizedBox(height: 16),
                    
                    // Weight
                    TextFormField(
                      controller: _weightController,
                      decoration: InputDecoration(
                        labelText: 'Weight (kg)',
                        border: OutlineInputBorder(
                          borderRadius: BorderRadius.circular(12),
                        ),
                        prefixIcon: const Icon(Icons.monitor_weight),
                      ),
                      keyboardType: TextInputType.number,
                      validator: (value) {
                        if (value != null && value.isNotEmpty) {
                          final weight = double.tryParse(value);
                          if (weight == null || weight <= 0) {
                            return 'Please enter a valid weight';
                          }
                        }
                        return null;
                      },
                    ),
                    const SizedBox(height: 32),
                    
                    // Submit Button
                    SizedBox(
                      width: double.infinity,
                      child: BaseButton(
                        text: 'Create My Training Plan',
                        onPressed: _submitAssessment,
                        backgroundColor: AppTheme.primaryColor,
                      ),
                    ),
                  ],
                ),
              ),
            ),
    );
  }
  
  Future<void> _submitAssessment() async {
    if (!_formKey.currentState!.validate()) {
      return;
    }
    
    setState(() {
      _isLoading = true;
    });
    
    try {
      final userId = _authService.currentUserId;
      if (userId == null) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('You must be logged in to create a training plan')),
        );
        return;
      }
      
      // Parse form values
      final pushUps = int.parse(_pushUpsController.text);
      final sitUps = int.parse(_sitUpsController.text);
      
      final minutes = int.parse(_runTimeMinutesController.text);
      final seconds = int.parse(_runTimeSecondsController.text);
      final runTime = minutes + (seconds / 60);
      
      double? height;
      if (_heightController.text.isNotEmpty) {
        height = double.parse(_heightController.text);
      }
      
      double? weight;
      if (_weightController.text.isNotEmpty) {
        weight = double.parse(_weightController.text);
      }
      
      // Create assessment
      final assessment = FitnessAssessment(
        id: _uuid.v4(),
        userId: userId,
        pushUps: pushUps,
        sitUps: sitUps,
        runTime: runTime,
        militaryAgency: _selectedAgency,
        assessmentDate: DateTime.now(),
        height: height,
        weight: weight,
      );
      
      // Save assessment
      await _trainingService.saveAssessment(assessment);
      
      // Generate training plan
      final plan = await _trainingService.generateTrainingPlan(userId);
      
      if (mounted) {
        // Navigate to training plan screen
        Navigator.pushReplacementNamed(
          context, 
          '/training_plan',
          arguments: plan,
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Error creating training plan: $e')),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }
}
