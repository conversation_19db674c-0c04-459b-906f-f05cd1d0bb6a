import 'package:flutter/material.dart';
import 'package:fit_4_force/core/theme/app_theme.dart';
import 'package:fit_4_force/core/utils/navigation_service.dart';
import 'package:fit_4_force/features/fitness/models/notification_model.dart';
import 'package:fit_4_force/features/fitness/services/notification_service.dart';

class NotificationsScreen extends StatefulWidget {
  const NotificationsScreen({super.key});

  @override
  State<NotificationsScreen> createState() => _NotificationsScreenState();
}

class _NotificationsScreenState extends State<NotificationsScreen> {
  final NotificationService _notificationService = NotificationService();
  late List<NotificationModel> _notifications;

  @override
  void initState() {
    super.initState();
    _notifications = _notificationService.getAllNotifications();
  }

  void _markAllAsRead() {
    _notificationService.markAllAsRead();
    setState(() {
      _notifications = _notificationService.getAllNotifications();
    });
  }

  void _handleNotificationTap(NotificationModel notification) {
    // Mark notification as read
    _notificationService.markAsRead(notification.id);
    setState(() {
      _notifications = _notificationService.getAllNotifications();
    });

    // Navigate based on notification type
    final navigationService = NavigationService();

    switch (notification.type) {
      case NotificationType.workout:
        if (notification.relatedId != null) {
          navigationService.navigateTo(
            '/workout-detail',
            arguments: notification.relatedId,
          );
        }
        break;
      case NotificationType.plan:
        if (notification.relatedId != null) {
          navigationService.navigateTo(
            '/plan-detail',
            arguments: notification.relatedId,
          );
        }
        break;
      case NotificationType.achievement:
        // Navigate to achievements screen
        break;
      case NotificationType.reminder:
        if (notification.relatedId != null) {
          navigationService.navigateTo(
            '/workout-detail',
            arguments: notification.relatedId,
          );
        }
        break;
      case NotificationType.general:
        // No specific navigation
        break;
    }
  }

  @override
  Widget build(BuildContext context) {
    final unreadCount = _notificationService.getUnreadCount();

    return Scaffold(
      appBar: AppBar(
        title: const Text('Notifications'),
        actions: [
          if (unreadCount > 0)
            TextButton(
              onPressed: _markAllAsRead,
              child: Text(
                'Mark all as read',
                style: TextStyle(color: AppTheme.primaryColor),
              ),
            ),
        ],
      ),
      body:
          _notifications.isEmpty
              ? _buildEmptyState()
              : _buildNotificationsList(),
    );
  }

  Widget _buildEmptyState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.notifications_off_outlined,
            size: 80,
            color: Colors.grey.shade400,
          ),
          const SizedBox(height: 16),
          Text(
            'No notifications yet',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: AppTheme.textPrimaryLight,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            'We\'ll notify you when something important happens',
            style: TextStyle(fontSize: 14, color: AppTheme.textSecondaryLight),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildNotificationsList() {
    return ListView.separated(
      padding: const EdgeInsets.all(16),
      itemCount: _notifications.length,
      separatorBuilder: (context, index) => const Divider(),
      itemBuilder: (context, index) {
        final notification = _notifications[index];
        return _buildNotificationItem(notification);
      },
    );
  }

  Widget _buildNotificationItem(NotificationModel notification) {
    return InkWell(
      onTap: () => _handleNotificationTap(notification),
      child: Container(
        padding: const EdgeInsets.symmetric(vertical: 12),
        child: Row(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Container(
              padding: const EdgeInsets.all(10),
              decoration: BoxDecoration(
                color: notification.color.withValues(
                  alpha: (0.1 * 255).round().toDouble(),
                ),
                shape: BoxShape.circle,
              ),
              child: Icon(
                notification.icon,
                color: notification.color,
                size: 24,
              ),
            ),
            const SizedBox(width: 16),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      Expanded(
                        child: Text(
                          notification.title,
                          style: TextStyle(
                            fontSize: 16,
                            fontWeight:
                                notification.isRead
                                    ? FontWeight.normal
                                    : FontWeight.bold,
                            color: AppTheme.textPrimaryLight,
                          ),
                        ),
                      ),
                      Text(
                        notification.formattedTime,
                        style: TextStyle(
                          fontSize: 12,
                          color: AppTheme.textSecondaryLight,
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 4),
                  Text(
                    notification.message,
                    style: TextStyle(
                      fontSize: 14,
                      color: AppTheme.textSecondaryLight,
                    ),
                  ),
                  if (!notification.isRead)
                    Container(
                      margin: const EdgeInsets.only(top: 8),
                      width: 8,
                      height: 8,
                      decoration: BoxDecoration(
                        color: AppTheme.primaryColor,
                        shape: BoxShape.circle,
                      ),
                    ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
}
