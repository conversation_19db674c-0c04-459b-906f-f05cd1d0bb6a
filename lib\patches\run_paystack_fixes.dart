import 'dart:io';
import 'package:logger/logger.dart';

void main() async {
  final logger = Logger(
    printer: PrettyPrinter(
      methodCount: 0,
      errorMethodCount: 5,
      lineLength: 80,
      colors: true,
      printEmojis: true,
      dateTimeFormat: DateTimeFormat.none,
    ),
  );

  logger.i('Running Flutter Paystack fixes...');

  final pubCachePath =
      Platform.isWindows
          ? '${Platform.environment['LOCALAPPDATA']}\\Pub\\Cache'
          : '${Platform.environment['HOME']}/.pub-cache';

  final paystackPath =
      '$pubCachePath\\git\\flutter_paystack-a4a33c3dd0a12f46d655a2e63d11e9f20ba82d01';

  // Check if the directory exists
  final paystackDir = Directory(paystackPath);
  if (!paystackDir.existsSync()) {
    logger.e('Flutter Paystack package not found at $paystackPath');
    return;
  }

  logger.i('Found Flutter Paystack package at $paystackPath');

  // Fix checkout_widget.dart
  final checkoutWidgetPath =
      '$paystackPath\\lib\\src\\widgets\\checkout\\checkout_widget.dart';
  final checkoutWidgetFile = File(checkoutWidgetPath);

  if (checkoutWidgetFile.existsSync()) {
    logger.i('Found checkout_widget.dart at $checkoutWidgetPath');

    // Create backup
    final backupPath = '$checkoutWidgetPath.bak';
    checkoutWidgetFile.copySync(backupPath);
    logger.i('Created backup at $backupPath');

    // Read file content
    var content = checkoutWidgetFile.readAsStringSync();

    // Apply fixes
    content = content.replaceAll('bodyText1', 'bodyLarge');
    content = content.replaceAll('vsync: this,', '');

    // Write fixed content
    checkoutWidgetFile.writeAsStringSync(content);
    logger.i('Fixed checkout_widget.dart');
  } else {
    logger.w('File not found: $checkoutWidgetPath');
  }

  // Fix buttons.dart
  final buttonsPath = '$paystackPath\\lib\\src\\widgets\\buttons.dart';
  final buttonsFile = File(buttonsPath);

  if (buttonsFile.existsSync()) {
    logger.i('Found buttons.dart at $buttonsPath');

    // Create backup
    final backupPath = '$buttonsPath.bak';
    buttonsFile.copySync(backupPath);
    logger.i('Created backup at $backupPath');

    // Read file content
    var content = buttonsFile.readAsStringSync();

    // Apply fixes
    content = content.replaceAll('accentColor', 'colorScheme.secondary');

    // Write fixed content
    buttonsFile.writeAsStringSync(content);
    logger.i('Fixed buttons.dart');
  } else {
    logger.w('File not found: $buttonsPath');
  }

  logger.i('All fixes applied successfully!');
  logger.i('Run "flutter pub get" to update the package.');
}
