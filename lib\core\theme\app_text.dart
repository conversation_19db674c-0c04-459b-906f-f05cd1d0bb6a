import 'package:flutter/material.dart';
import 'package:fit_4_force/core/theme/app_theme.dart';

/// A utility class for consistent text styling
class AppText {
  // Prevent instantiation
  AppText._();
  
  /// Heading styles
  static TextStyle headingLarge(BuildContext context, {Color? color, FontWeight? fontWeight}) {
    return Theme.of(context).textTheme.headlineLarge!.copyWith(
      color: color,
      fontWeight: fontWeight ?? AppTheme.fontWeightBold,
    );
  }
  
  static TextStyle headingMedium(BuildContext context, {Color? color, FontWeight? fontWeight}) {
    return Theme.of(context).textTheme.headlineMedium!.copyWith(
      color: color,
      fontWeight: fontWeight ?? AppTheme.fontWeightBold,
    );
  }
  
  static TextStyle headingSmall(BuildContext context, {Color? color, FontWeight? fontWeight}) {
    return Theme.of(context).textTheme.headlineSmall!.copyWith(
      color: color,
      fontWeight: fontWeight ?? AppTheme.fontWeightSemiBold,
    );
  }
  
  /// Title styles
  static TextStyle titleLarge(BuildContext context, {Color? color, FontWeight? fontWeight}) {
    return Theme.of(context).textTheme.titleLarge!.copyWith(
      color: color,
      fontWeight: fontWeight ?? AppTheme.fontWeightSemiBold,
    );
  }
  
  static TextStyle titleMedium(BuildContext context, {Color? color, FontWeight? fontWeight}) {
    return Theme.of(context).textTheme.titleMedium!.copyWith(
      color: color,
      fontWeight: fontWeight ?? AppTheme.fontWeightMedium,
    );
  }
  
  static TextStyle titleSmall(BuildContext context, {Color? color, FontWeight? fontWeight}) {
    return Theme.of(context).textTheme.titleSmall!.copyWith(
      color: color,
      fontWeight: fontWeight ?? AppTheme.fontWeightMedium,
    );
  }
  
  /// Body styles
  static TextStyle bodyLarge(BuildContext context, {Color? color, FontWeight? fontWeight}) {
    return Theme.of(context).textTheme.bodyLarge!.copyWith(
      color: color,
      fontWeight: fontWeight ?? AppTheme.fontWeightRegular,
    );
  }
  
  static TextStyle bodyMedium(BuildContext context, {Color? color, FontWeight? fontWeight}) {
    return Theme.of(context).textTheme.bodyMedium!.copyWith(
      color: color,
      fontWeight: fontWeight ?? AppTheme.fontWeightRegular,
    );
  }
  
  static TextStyle bodySmall(BuildContext context, {Color? color, FontWeight? fontWeight}) {
    return Theme.of(context).textTheme.bodySmall!.copyWith(
      color: color,
      fontWeight: fontWeight ?? AppTheme.fontWeightRegular,
    );
  }
  
  /// Label styles
  static TextStyle labelLarge(BuildContext context, {Color? color, FontWeight? fontWeight}) {
    return Theme.of(context).textTheme.labelLarge!.copyWith(
      color: color,
      fontWeight: fontWeight ?? AppTheme.fontWeightMedium,
    );
  }
  
  static TextStyle labelMedium(BuildContext context, {Color? color, FontWeight? fontWeight}) {
    return Theme.of(context).textTheme.labelMedium!.copyWith(
      color: color,
      fontWeight: fontWeight ?? AppTheme.fontWeightMedium,
    );
  }
  
  static TextStyle labelSmall(BuildContext context, {Color? color, FontWeight? fontWeight}) {
    return Theme.of(context).textTheme.labelSmall!.copyWith(
      color: color,
      fontWeight: fontWeight ?? AppTheme.fontWeightRegular,
    );
  }
  
  /// Button text styles
  static TextStyle buttonLarge(BuildContext context, {Color? color, FontWeight? fontWeight}) {
    return Theme.of(context).textTheme.labelLarge!.copyWith(
      color: color ?? Colors.white,
      fontWeight: fontWeight ?? AppTheme.fontWeightSemiBold,
      letterSpacing: 0.5,
    );
  }
  
  static TextStyle buttonMedium(BuildContext context, {Color? color, FontWeight? fontWeight}) {
    return Theme.of(context).textTheme.labelMedium!.copyWith(
      color: color ?? Colors.white,
      fontWeight: fontWeight ?? AppTheme.fontWeightSemiBold,
      letterSpacing: 0.5,
    );
  }
  
  static TextStyle buttonSmall(BuildContext context, {Color? color, FontWeight? fontWeight}) {
    return Theme.of(context).textTheme.labelSmall!.copyWith(
      color: color ?? Colors.white,
      fontWeight: fontWeight ?? AppTheme.fontWeightMedium,
      letterSpacing: 0.5,
    );
  }
  
  /// Caption styles
  static TextStyle caption(BuildContext context, {Color? color, FontWeight? fontWeight}) {
    return Theme.of(context).textTheme.bodySmall!.copyWith(
      color: color ?? AppTheme.textSecondaryLight,
      fontWeight: fontWeight ?? AppTheme.fontWeightRegular,
    );
  }
  
  /// Overline styles
  static TextStyle overline(BuildContext context, {Color? color, FontWeight? fontWeight}) {
    return Theme.of(context).textTheme.labelSmall!.copyWith(
      color: color ?? AppTheme.textSecondaryLight,
      fontWeight: fontWeight ?? AppTheme.fontWeightMedium,
      letterSpacing: 1.0,
    );
  }
}
