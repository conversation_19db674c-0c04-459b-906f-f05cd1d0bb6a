import 'package:flutter/material.dart';
import 'package:fit_4_force/core/utils/legal_documents_helper.dart';
import 'package:fit_4_force/core/theme/app_theme.dart';

/// Widget for displaying legal document links in the app
class LegalDocumentsWidget extends StatelessWidget {
  final bool showAsCards;
  final bool showIcons;
  final EdgeInsetsGeometry? padding;
  final CrossAxisAlignment crossAxisAlignment;

  const LegalDocumentsWidget({
    super.key,
    this.showAsCards = false,
    this.showIcons = true,
    this.padding,
    this.crossAxisAlignment = CrossAxisAlignment.start,
  });

  @override
  Widget build(BuildContext context) {
    if (showAsCards) {
      return _buildCardLayout(context);
    } else {
      return _buildListLayout(context);
    }
  }

  Widget _buildListLayout(BuildContext context) {
    return Padding(
      padding: padding ?? const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: crossAxisAlignment,
        children: [
          Text(
            'Legal Documents',
            style: Theme.of(context).textTheme.titleLarge?.copyWith(
              fontWeight: FontWeight.bold,
              color: AppTheme.primaryColor,
            ),
          ),
          const SizedBox(height: 16),
          ...LegalDocumentType.values.map(
            (type) => _buildListTile(context, type),
          ),
        ],
      ),
    );
  }

  Widget _buildCardLayout(BuildContext context) {
    return Padding(
      padding: padding ?? const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: crossAxisAlignment,
        children: [
          Text(
            'Legal Documents',
            style: Theme.of(context).textTheme.titleLarge?.copyWith(
              fontWeight: FontWeight.bold,
              color: AppTheme.primaryColor,
            ),
          ),
          const SizedBox(height: 16),
          GridView.builder(
            shrinkWrap: true,
            physics: const NeverScrollableScrollPhysics(),
            gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
              crossAxisCount: 2,
              crossAxisSpacing: 12,
              mainAxisSpacing: 12,
              childAspectRatio: 1.2,
            ),
            itemCount: LegalDocumentType.values.length,
            itemBuilder: (context, index) {
              final type = LegalDocumentType.values[index];
              return _buildCard(context, type);
            },
          ),
        ],
      ),
    );
  }

  Widget _buildListTile(BuildContext context, LegalDocumentType type) {
    return Card(
      margin: const EdgeInsets.only(bottom: 8),
      child: ListTile(
        leading:
            showIcons
                ? Icon(_getIcon(type), color: AppTheme.primaryColor)
                : null,
        title: Text(
          type.displayName,
          style: const TextStyle(fontWeight: FontWeight.w500),
        ),
        subtitle: Text(_getDescription(type)),
        trailing: const Icon(Icons.open_in_new, size: 20),
        onTap: () => _launchDocument(context, type),
      ),
    );
  }

  Widget _buildCard(BuildContext context, LegalDocumentType type) {
    return Card(
      elevation: 2,
      child: InkWell(
        onTap: () => _launchDocument(context, type),
        borderRadius: BorderRadius.circular(8),
        child: Padding(
          padding: const EdgeInsets.all(12),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              if (showIcons) ...[
                Icon(_getIcon(type), size: 32, color: AppTheme.primaryColor),
                const SizedBox(height: 8),
              ],
              Text(
                type.displayName,
                textAlign: TextAlign.center,
                style: const TextStyle(
                  fontWeight: FontWeight.w600,
                  fontSize: 14,
                ),
              ),
              const SizedBox(height: 4),
              const Icon(Icons.open_in_new, size: 16, color: Colors.grey),
            ],
          ),
        ),
      ),
    );
  }

  IconData _getIcon(LegalDocumentType type) {
    switch (type) {
      case LegalDocumentType.termsOfService:
        return Icons.description;
      case LegalDocumentType.privacyPolicy:
        return Icons.privacy_tip;
      case LegalDocumentType.cookiePolicy:
        return Icons.cookie;
      case LegalDocumentType.disclaimer:
        return Icons.warning;
      case LegalDocumentType.contact:
        return Icons.contact_support;
      case LegalDocumentType.refundPolicy:
        return Icons.money_off;
      case LegalDocumentType.dataSecurity:
        return Icons.security;
      case LegalDocumentType.communityPolicy:
        return Icons.people;
      case LegalDocumentType.legalIndex:
        return Icons.folder_open;
    }
  }

  String _getDescription(LegalDocumentType type) {
    switch (type) {
      case LegalDocumentType.termsOfService:
        return 'Terms and conditions for using the app';
      case LegalDocumentType.privacyPolicy:
        return 'How we handle your personal information';
      case LegalDocumentType.cookiePolicy:
        return 'Information about cookies and tracking';
      case LegalDocumentType.disclaimer:
        return 'Important disclaimers and limitations';
      case LegalDocumentType.contact:
        return 'Get in touch with our support team';
      case LegalDocumentType.refundPolicy:
        return 'Information about refunds and cancellations';
      case LegalDocumentType.dataSecurity:
        return 'How we protect and retain your personal data';
      case LegalDocumentType.communityPolicy:
        return 'Rules for a respectful and supportive community';
      case LegalDocumentType.legalIndex:
        return 'All legal documents in one place';
    }
  }

  Future<void> _launchDocument(
    BuildContext context,
    LegalDocumentType type,
  ) async {
    // Show loading indicator
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => const Center(child: CircularProgressIndicator()),
    );

    try {
      final success = await type.launch();

      // Hide loading indicator
      if (context.mounted) {
        Navigator.of(context).pop();
      }

      if (!success && context.mounted) {
        _showErrorDialog(context, type);
      }
    } catch (e) {
      // Hide loading indicator
      if (context.mounted) {
        Navigator.of(context).pop();
        _showErrorDialog(context, type);
      }
    }
  }

  void _showErrorDialog(BuildContext context, LegalDocumentType type) {
    showDialog(
      context: context,
      builder:
          (context) => AlertDialog(
            title: const Text('Unable to Open Document'),
            content: Text(
              'Sorry, we couldn\'t open the ${type.displayName}. '
              'Please check your internet connection and try again.',
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.of(context).pop(),
                child: const Text('OK'),
              ),
              TextButton(
                onPressed: () {
                  Navigator.of(context).pop();
                  _launchDocument(context, type);
                },
                child: const Text('Retry'),
              ),
            ],
          ),
    );
  }
}

/// Simple legal links widget for footers or settings
class LegalLinksWidget extends StatelessWidget {
  final MainAxisAlignment mainAxisAlignment;
  final bool showDividers;

  const LegalLinksWidget({
    super.key,
    this.mainAxisAlignment = MainAxisAlignment.center,
    this.showDividers = true,
  });

  @override
  Widget build(BuildContext context) {
    final links = [
      LegalDocumentType.termsOfService,
      LegalDocumentType.privacyPolicy,
      LegalDocumentType.disclaimer,
    ];

    return Row(
      mainAxisAlignment: mainAxisAlignment,
      children: [
        for (int i = 0; i < links.length; i++) ...[
          TextButton(
            onPressed: () => links[i].launch(),
            child: Text(
              links[i].displayName,
              style: TextStyle(
                fontSize: 12,
                color: Theme.of(context).textTheme.bodySmall?.color,
              ),
            ),
          ),
          if (showDividers && i < links.length - 1)
            Container(
              height: 12,
              width: 1,
              color: Colors.grey.withValues(alpha: 0.5),
              margin: const EdgeInsets.symmetric(horizontal: 4),
            ),
        ],
      ],
    );
  }
}

/// Legal documents section for settings screen
class LegalDocumentsSection extends StatelessWidget {
  const LegalDocumentsSection({super.key});

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Padding(
          padding: const EdgeInsets.all(16),
          child: Text(
            'Legal & Support',
            style: Theme.of(context).textTheme.titleMedium?.copyWith(
              fontWeight: FontWeight.bold,
              color: AppTheme.primaryColor,
            ),
          ),
        ),
        ...LegalDocumentType.values.map(
          (type) => ListTile(
            leading: Icon(_getIcon(type)),
            title: Text(type.displayName),
            trailing: const Icon(Icons.open_in_new, size: 20),
            onTap: () => type.launch(),
          ),
        ),
      ],
    );
  }

  IconData _getIcon(LegalDocumentType type) {
    switch (type) {
      case LegalDocumentType.termsOfService:
        return Icons.description;
      case LegalDocumentType.privacyPolicy:
        return Icons.privacy_tip;
      case LegalDocumentType.cookiePolicy:
        return Icons.cookie;
      case LegalDocumentType.disclaimer:
        return Icons.warning;
      case LegalDocumentType.contact:
        return Icons.contact_support;
      case LegalDocumentType.refundPolicy:
        return Icons.money_off;
      case LegalDocumentType.dataSecurity:
        return Icons.security;
      case LegalDocumentType.communityPolicy:
        return Icons.people;
      case LegalDocumentType.legalIndex:
        return Icons.folder_open;
    }
  }
}
