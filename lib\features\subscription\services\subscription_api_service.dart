import 'package:fit_4_force/core/services/api_service.dart';
import 'package:fit_4_force/shared/models/subscription_model.dart';

/// API service for subscription-related endpoints
class SubscriptionApiService {
  /// Base API service
  final ApiService _apiService;

  /// Constructor
  SubscriptionApiService({ApiService? apiService})
      : _apiService = apiService ?? ApiService();

  /// Get user subscription
  Future<SubscriptionModel?> getUserSubscription(
    String userId,
    String token,
  ) async {
    final response = await _apiService.get(
      '/subscriptions/user/$userId',
      token: token,
    );

    if (response == null) {
      return null;
    }

    return SubscriptionModel.fromJson(response);
  }

  /// Create subscription
  Future<SubscriptionModel> createSubscription(
    Map<String, dynamic> data,
    String token,
  ) async {
    final response = await _apiService.post(
      '/subscriptions',
      body: data,
      token: token,
    );

    return SubscriptionModel.from<PERSON>son(response);
  }

  /// Verify payment
  Future<Map<String, dynamic>> verifyPayment(
    String reference,
    String token,
  ) async {
    final response = await _apiService.post(
      '/payments/verify',
      body: {
        'reference': reference,
      },
      token: token,
    );

    return response;
  }

  /// Cancel subscription
  Future<void> cancelSubscription(
    String subscriptionId,
    String token,
  ) async {
    await _apiService.post(
      '/subscriptions/$subscriptionId/cancel',
      token: token,
    );
  }

  /// Renew subscription
  Future<SubscriptionModel> renewSubscription(
    String subscriptionId,
    Map<String, dynamic> data,
    String token,
  ) async {
    final response = await _apiService.post(
      '/subscriptions/$subscriptionId/renew',
      body: data,
      token: token,
    );

    return SubscriptionModel.fromJson(response);
  }

  /// Get subscription history
  Future<List<SubscriptionModel>> getSubscriptionHistory(
    String userId,
    String token,
  ) async {
    final response = await _apiService.get(
      '/subscriptions/history/$userId',
      token: token,
    );

    return (response as List)
        .map((item) => SubscriptionModel.fromJson(item))
        .toList();
  }
}
