                        -HC:\Flutter\Soc\flutter\packages\flutter_tools\gradle\src\main\groovy
-DCMAKE_SYSTEM_NAME=Android
-DCMAKE_EXPORT_COMPILE_COMMANDS=ON
-DCMAKE_SYSTEM_VERSION=26
-D<PERSON><PERSON>OID_PLATFORM=android-26
-DANDROID_ABI=x86
-DCMAKE_ANDROID_ARCH_ABI=x86
-DANDROID_NDK=C:\Users\<USER>\AppData\Local\Android\sdk\ndk\27.0.12077973
-DCMAKE_ANDROID_NDK=C:\Users\<USER>\AppData\Local\Android\sdk\ndk\27.0.12077973
-DC<PERSON>KE_TOOLCHAIN_FILE=C:\Users\<USER>\AppData\Local\Android\sdk\ndk\27.0.12077973\build\cmake\android.toolchain.cmake
-DCMAKE_MAKE_PROGRAM=C:\Users\<USER>\AppData\Local\Android\sdk\cmake\3.22.1\bin\ninja.exe
-DCMAKE_LIBRARY_OUTPUT_DIRECTORY=C:\Flutter\Soc\Fit_4_force\build\app\intermediates\cxx\RelWithDebInfo\536v2f3s\obj\x86
-DCMAKE_RUNTIME_OUTPUT_DIRECTORY=C:\Flutter\Soc\Fit_4_force\build\app\intermediates\cxx\RelWithDebInfo\536v2f3s\obj\x86
-DCMAKE_BUILD_TYPE=RelWithDebInfo
-BC:\Flutter\Soc\Fit_4_force\android\app\.cxx\RelWithDebInfo\536v2f3s\x86
-GNinja
-Wno-dev
--no-warn-unused-cli
                        Build command args: []
                        Version: 2