import 'package:flutter/material.dart';
import 'package:fit_4_force/shared/models/base_model.dart';

/// Model representing an achievement badge
class BadgeModel extends BaseModel {
  final String name;
  final String description;
  final String category;
  final int level;
  final String imageUrl;
  final IconData icon;
  final Color color;
  final bool isUnlocked;
  final DateTime? unlockedAt;
  final int progress;
  final int requiredProgress;

  const BadgeModel({
    required super.id,
    required super.createdAt,
    super.updatedAt,
    required this.name,
    required this.description,
    required this.category,
    required this.level,
    required this.imageUrl,
    required this.icon,
    required this.color,
    required this.isUnlocked,
    this.unlockedAt,
    required this.progress,
    required this.requiredProgress,
  });

  @override
  List<Object?> get props => [
        ...super.props,
        name,
        description,
        category,
        level,
        imageUrl,
        icon,
        color,
        isUnlocked,
        unlockedAt,
        progress,
        requiredProgress,
      ];

  @override
  BadgeModel copyWith({
    String? id,
    DateTime? createdAt,
    DateTime? updatedAt,
    String? name,
    String? description,
    String? category,
    int? level,
    String? imageUrl,
    IconData? icon,
    Color? color,
    bool? isUnlocked,
    DateTime? unlockedAt,
    int? progress,
    int? requiredProgress,
  }) {
    return BadgeModel(
      id: id ?? this.id,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      name: name ?? this.name,
      description: description ?? this.description,
      category: category ?? this.category,
      level: level ?? this.level,
      imageUrl: imageUrl ?? this.imageUrl,
      icon: icon ?? this.icon,
      color: color ?? this.color,
      isUnlocked: isUnlocked ?? this.isUnlocked,
      unlockedAt: unlockedAt ?? this.unlockedAt,
      progress: progress ?? this.progress,
      requiredProgress: requiredProgress ?? this.requiredProgress,
    );
  }
}
