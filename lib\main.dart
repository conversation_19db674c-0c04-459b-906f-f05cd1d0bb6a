import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:fit_4_force/core/config/app_routes.dart';
import 'package:fit_4_force/core/theme/app_theme.dart';
import 'package:fit_4_force/core/utils/navigation_service.dart';
import 'package:fit_4_force/features/auth/bloc/auth_bloc.dart';
import 'package:fit_4_force/features/subscription/bloc/subscription_bloc.dart';
import 'package:fit_4_force/shared/models/user_model.dart';
import 'package:fit_4_force/shared/services/supabase_subscription_service.dart';
import 'package:fit_4_force/shared/services/supabase_auth_service.dart'
    as shared;
import 'package:fit_4_force/shared/providers/supabase_provider.dart';
import 'package:fit_4_force/core/config/supabase_config.dart';
import 'package:fit_4_force/core/config/environment_config.dart';
import 'package:fit_4_force/core/widgets/supabase_connection_test.dart';
import 'package:fit_4_force/core/services/backend_service_manager.dart';
import 'package:fit_4_force/core/testing/backend_test_suite.dart';
import 'package:fit_4_force/core/services/service_locator.dart';
import 'package:logger/logger.dart';
import 'package:flutter/foundation.dart';

void main() async {
  WidgetsFlutterBinding.ensureInitialized();

  // Initialize logger
  final logger = Logger();

  // Print environment information in debug mode
  if (kDebugMode) {
    EnvironmentConfig.printEnvironmentInfo();

    // Validate environment configuration
    if (!EnvironmentConfig.validateConfiguration()) {
      logger.e('❌ Environment configuration validation failed');
    }
  }

  // Initialize Supabase with enhanced error handling
  try {
    logger.i('🚀 Initializing Supabase for ${EnvironmentConfig.appName}...');
    await SupabaseConfig.initialize();
    logger.i('✅ Supabase initialized successfully');
    logger.i('🔗 Connected to: ${SupabaseConfig.supabaseUrl}');

    // Test connection
    if (SupabaseConfig.isInitialized) {
      logger.i('🔌 Supabase connection verified');
    } else {
      logger.w('⚠️ Supabase connection could not be verified');
    }
  } catch (e) {
    logger.e('❌ Error initializing Supabase: $e');

    // In debug mode, show more detailed error information
    if (kDebugMode) {
      logger.e('🔍 Debug Info:');
      logger.e('   URL: ${SupabaseConfig.supabaseUrl}');
      logger.e('   Environment: ${EnvironmentConfig.currentEnvironment}');
      logger.e('   Stack trace: $e');
    }

    // Don't crash the app, but log the error
    // The app can still run with limited functionality
  }

  // Initialize backend services
  try {
    logger.i('🔧 Initializing backend services...');
    await BackendServiceManager().initialize();
    logger.i('✅ Backend services initialized successfully');

    // Run development tests (DEBUG MODE ONLY)
    if (kDebugMode) {
      logger.i('🧪 Running backend tests...');
      final testSuite = BackendTestSuite();
      final testResults = await testSuite.runTests();
      testSuite.logTestResults(testResults);
    }
  } catch (e) {
    logger.e('❌ Error initializing backend services: $e');
    // Continue without backend services for now
  }

  // Initialize enhanced service locator
  await setupServiceLocator();

  runApp(
    SupabaseProvider(
      client: SupabaseConfig.client,
      child: const Fit4ForceApp(),
    ),
  );
}

class Fit4ForceApp extends StatelessWidget {
  const Fit4ForceApp({super.key});

  @override
  Widget build(BuildContext context) {
    return MultiBlocProvider(
      providers: [
        BlocProvider(
          create: (context) {
            final authBloc = AuthBloc();

            // Create a mock user and emit authenticated state for testing
            final mockUser = UserModel(
              id: '123456789',
              createdAt: DateTime.now(),
              fullName: 'Test User',
              email: '<EMAIL>',
              age: 25,
              gender: 'Male',
              height: 175.0,
              weight: 70.0,
              targetAgency: 'Nigerian Army',
              fitnessGoal: 'Pass fitness test',
              isPremium: false,
              notificationPreferences: {'push': true, 'email': true},
              completedQuizzes: [],
              savedWorkouts: [],
            );

            // Emit authenticated state after a short delay
            Future.delayed(const Duration(milliseconds: 100), () {
              authBloc.add(AuthenticatedEvent(mockUser));
            });

            return authBloc;
          },
        ),
        BlocProvider(
          create:
              (context) => SubscriptionBloc(
                SupabaseSubscriptionService(
                  authService: getIt<shared.SupabaseAuthService>(),
                ),
              ),
        ),
      ],
      child: BlocConsumer<AuthBloc, AuthState>(
        listener: (context, state) {
          // Get the singleton instance
          final navigationService = NavigationService();
          if (state is Authenticated) {
            // Only navigate if we have a valid navigator state
            navigationService.navigateToAndRemoveUntil(AppRoutes.home);
          } else if (state is Unauthenticated) {
            navigationService.navigateToAndRemoveUntil(AppRoutes.login);
          }
        },
        builder: (context, state) {
          // Use the same singleton instance for the navigator key
          final navigationService = NavigationService();
          return MaterialApp(
            title: EnvironmentConfig.appName,
            debugShowCheckedModeBanner: !EnvironmentConfig.isProduction,
            theme: AppTheme.lightTheme,
            darkTheme: AppTheme.darkTheme,
            navigatorKey: navigationService.navigatorKey,
            routes: AppRoutes.getRoutes(),
            initialRoute: AppRoutes.login,
          );
        },
      ),
    );
  }
}

class HomeScreen extends StatelessWidget {
  const HomeScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(EnvironmentConfig.appName),
        backgroundColor: AppTheme.primaryColor,
        foregroundColor: Colors.white,
        actions: [
          if (EnvironmentConfig.showDebugInfo)
            IconButton(
              icon: const Icon(Icons.info_outline),
              onPressed: () {
                showDialog(
                  context: context,
                  builder:
                      (context) => AlertDialog(
                        title: const Text('Environment Info'),
                        content: Column(
                          mainAxisSize: MainAxisSize.min,
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              'Environment: ${EnvironmentConfig.currentEnvironment}',
                            ),
                            Text('App Name: ${EnvironmentConfig.appName}'),
                            Text('Supabase URL: ${SupabaseConfig.supabaseUrl}'),
                            Text(
                              'Is Production: ${EnvironmentConfig.isProduction}',
                            ),
                          ],
                        ),
                        actions: [
                          TextButton(
                            onPressed: () => Navigator.pop(context),
                            child: const Text('OK'),
                          ),
                        ],
                      ),
                );
              },
            ),
        ],
      ),
      body: SingleChildScrollView(
        child: Column(
          children: [
            // Header Section
            Container(
              width: double.infinity,
              padding: const EdgeInsets.all(24),
              child: Column(
                children: [
                  const Icon(
                    Icons.fitness_center,
                    size: 80,
                    color: AppTheme.primaryColor,
                  ),
                  const SizedBox(height: 16),
                  Text(
                    EnvironmentConfig.appName,
                    style: Theme.of(context).textTheme.headlineLarge?.copyWith(
                      fontWeight: FontWeight.bold,
                      color: AppTheme.primaryColor,
                    ),
                  ),
                  const SizedBox(height: 8),
                  Text(
                    'Prepare. Train. Succeed.',
                    style: Theme.of(
                      context,
                    ).textTheme.titleMedium?.copyWith(color: Colors.grey[700]),
                  ),
                  if (!EnvironmentConfig.isProduction) ...[
                    const SizedBox(height: 8),
                    Container(
                      padding: const EdgeInsets.symmetric(
                        horizontal: 12,
                        vertical: 4,
                      ),
                      decoration: BoxDecoration(
                        color: Colors.orange.shade100,
                        borderRadius: BorderRadius.circular(12),
                        border: Border.all(color: Colors.orange),
                      ),
                      child: Text(
                        EnvironmentConfig.currentEnvironment.toUpperCase(),
                        style: TextStyle(
                          color: Colors.orange.shade800,
                          fontWeight: FontWeight.bold,
                          fontSize: 12,
                        ),
                      ),
                    ),
                  ],
                ],
              ),
            ),

            // Supabase Connection Test
            const SupabaseConnectionTest(),

            // Action Buttons
            Padding(
              padding: const EdgeInsets.all(16),
              child: Column(
                children: [
                  SizedBox(
                    width: double.infinity,
                    child: ElevatedButton.icon(
                      onPressed: () {
                        showDialog(
                          context: context,
                          builder:
                              (context) => AlertDialog(
                                title: const Text('Welcome to FIT4FORCE'),
                                content: const Text(
                                  'Supabase integration is now active! '
                                  'The app is ready for authentication, data storage, and real-time features.',
                                ),
                                actions: [
                                  TextButton(
                                    onPressed: () => Navigator.pop(context),
                                    child: const Text('OK'),
                                  ),
                                ],
                              ),
                        );
                      },
                      icon: const Icon(Icons.rocket_launch),
                      label: const Text('Get Started'),
                      style: ElevatedButton.styleFrom(
                        backgroundColor: AppTheme.primaryColor,
                        foregroundColor: Colors.white,
                        padding: const EdgeInsets.symmetric(vertical: 16),
                      ),
                    ),
                  ),
                  const SizedBox(height: 12),
                  SizedBox(
                    width: double.infinity,
                    child: OutlinedButton.icon(
                      onPressed: () {
                        // Navigate to login screen to test authentication
                        Navigator.pushNamed(context, '/login');
                      },
                      icon: const Icon(Icons.login),
                      label: const Text('Test Authentication'),
                      style: OutlinedButton.styleFrom(
                        padding: const EdgeInsets.symmetric(vertical: 16),
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
}

class SplashScreen extends StatelessWidget {
  const SplashScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return const Scaffold(body: Center(child: CircularProgressIndicator()));
  }
}
