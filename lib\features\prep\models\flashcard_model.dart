import 'package:flutter/material.dart';

class FlashcardModel {
  final String id;
  final String question;
  final String answer;
  final String category;
  final Color color;
  final DateTime createdAt;
  final DateTime? lastReviewed;
  final int confidenceLevel; // 1-5 scale (1: Not confident, 5: Very confident)
  final bool isPremium;
  final String? imageUrl;
  final List<String> tags;
  final String? notes;

  FlashcardModel({
    required this.id,
    required this.question,
    required this.answer,
    required this.category,
    required this.color,
    required this.createdAt,
    this.lastReviewed,
    this.confidenceLevel = 1,
    this.isPremium = false,
    this.imageUrl,
    this.tags = const [],
    this.notes,
  });

  FlashcardModel copyWith({
    String? id,
    String? question,
    String? answer,
    String? category,
    Color? color,
    DateTime? createdAt,
    DateTime? lastReviewed,
    int? confidenceLevel,
    bool? isPremium,
    String? imageUrl,
    List<String>? tags,
    String? notes,
  }) {
    return FlashcardModel(
      id: id ?? this.id,
      question: question ?? this.question,
      answer: answer ?? this.answer,
      category: category ?? this.category,
      color: color ?? this.color,
      createdAt: createdAt ?? this.createdAt,
      lastReviewed: lastReviewed ?? this.lastReviewed,
      confidenceLevel: confidenceLevel ?? this.confidenceLevel,
      isPremium: isPremium ?? this.isPremium,
      imageUrl: imageUrl ?? this.imageUrl,
      tags: tags ?? this.tags,
      notes: notes ?? this.notes,
    );
  }

  // Calculate the next review date based on spaced repetition algorithm
  DateTime getNextReviewDate() {
    final now = DateTime.now();
    
    if (lastReviewed == null) {
      return now; // If never reviewed, review now
    }
    
    // Simple spaced repetition algorithm
    // Higher confidence = longer interval between reviews
    int daysToAdd;
    
    switch (confidenceLevel) {
      case 1:
        daysToAdd = 1; // Review again tomorrow
        break;
      case 2:
        daysToAdd = 3; // Review in 3 days
        break;
      case 3:
        daysToAdd = 7; // Review in a week
        break;
      case 4:
        daysToAdd = 14; // Review in two weeks
        break;
      case 5:
        daysToAdd = 30; // Review in a month
        break;
      default:
        daysToAdd = 1;
    }
    
    return lastReviewed!.add(Duration(days: daysToAdd));
  }
  
  // Check if the card is due for review
  bool isDueForReview() {
    final nextReviewDate = getNextReviewDate();
    return DateTime.now().isAfter(nextReviewDate);
  }
}
