import 'base_model.dart';

class UserModel extends BaseModel {
  final String fullName;
  final String email;
  final int age;
  final String gender;
  final double height;
  final double weight;
  final String targetAgency;
  final String fitnessGoal;
  final bool isPremium;
  final Map<String, bool> notificationPreferences;
  final String? profileImageUrl;
  final List<String> completedQuizzes;
  final List<String> savedWorkouts;
  final DateTime? premiumExpiryDate;

  const UserModel({
    required super.id,
    required super.createdAt,
    super.updatedAt,
    required this.fullName,
    required this.email,
    required this.age,
    required this.gender,
    required this.height,
    required this.weight,
    required this.targetAgency,
    required this.fitnessGoal,
    required this.isPremium,
    required this.notificationPreferences,
    this.profileImageUrl,
    required this.completedQuizzes,
    required this.savedWorkouts,
    this.premiumExpiryDate,
  });

  @override
  List<Object?> get props => [
    ...super.props,
    fullName,
    email,
    age,
    gender,
    height,
    weight,
    targetAgency,
    fitnessGoal,
    isPremium,
    notificationPreferences,
    profileImageUrl,
    completedQuizzes,
    savedWorkouts,
    premiumExpiryDate,
  ];

  @override
  Map<String, dynamic> toJson() {
    return {
      ...super.toJson(),
      'fullName': fullName,
      'email': email,
      'age': age,
      'gender': gender,
      'height': height,
      'weight': weight,
      'targetAgency': targetAgency,
      'fitnessGoal': fitnessGoal,
      'isPremium': isPremium,
      'notificationPreferences': notificationPreferences,
      'profileImageUrl': profileImageUrl,
      'completedQuizzes': completedQuizzes,
      'savedWorkouts': savedWorkouts,
      'premiumExpiryDate': premiumExpiryDate?.toIso8601String(),
    };
  }

  factory UserModel.fromJson(Map<String, dynamic> json) {
    return UserModel(
      id: json['id'] as String,
      createdAt: DateTime.parse(json['createdAt'] as String),
      updatedAt:
          json['updatedAt'] != null
              ? DateTime.parse(json['updatedAt'] as String)
              : null,
      fullName: json['fullName'] as String,
      email: json['email'] as String,
      age: json['age'] as int,
      gender: json['gender'] as String,
      height: (json['height'] as num).toDouble(),
      weight: (json['weight'] as num).toDouble(),
      targetAgency: json['targetAgency'] as String,
      fitnessGoal: json['fitnessGoal'] as String,
      isPremium: json['isPremium'] as bool,
      notificationPreferences: Map<String, bool>.from(
        json['notificationPreferences'] as Map,
      ),
      profileImageUrl: json['profileImageUrl'] as String?,
      completedQuizzes: List<String>.from(json['completedQuizzes'] as List),
      savedWorkouts: List<String>.from(json['savedWorkouts'] as List),
      premiumExpiryDate:
          json['premiumExpiryDate'] != null
              ? DateTime.parse(json['premiumExpiryDate'] as String)
              : null,
    );
  }

  @override
  UserModel copyWith({
    String? id,
    DateTime? createdAt,
    DateTime? updatedAt,
    String? fullName,
    String? email,
    int? age,
    String? gender,
    double? height,
    double? weight,
    String? targetAgency,
    String? fitnessGoal,
    bool? isPremium,
    Map<String, bool>? notificationPreferences,
    String? profileImageUrl,
    List<String>? completedQuizzes,
    List<String>? savedWorkouts,
    DateTime? premiumExpiryDate,
  }) {
    return UserModel(
      id: id ?? this.id,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      fullName: fullName ?? this.fullName,
      email: email ?? this.email,
      age: age ?? this.age,
      gender: gender ?? this.gender,
      height: height ?? this.height,
      weight: weight ?? this.weight,
      targetAgency: targetAgency ?? this.targetAgency,
      fitnessGoal: fitnessGoal ?? this.fitnessGoal,
      isPremium: isPremium ?? this.isPremium,
      notificationPreferences:
          notificationPreferences ?? this.notificationPreferences,
      profileImageUrl: profileImageUrl ?? this.profileImageUrl,
      completedQuizzes: completedQuizzes ?? this.completedQuizzes,
      savedWorkouts: savedWorkouts ?? this.savedWorkouts,
      premiumExpiryDate: premiumExpiryDate ?? this.premiumExpiryDate,
    );
  }
}
