@echo off
echo Fixing flutter_paystack package...

set PAYSTACK_PATH=%LOCALAPPDATA%\Pub\Cache\git\flutter_paystack-a4a33c3dd0a12f46d655a2e63d11e9f20ba82d01

if exist "%PAYSTACK_PATH%" (
    echo Found flutter_paystack package at %PAYSTACK_PATH%
    
    echo Creating backup of original files...
    
    if not exist "%PAYSTACK_PATH%\lib\src\widgets\checkout\checkout_widget.dart.bak2" (
        copy "%PAYSTACK_PATH%\lib\src\widgets\checkout\checkout_widget.dart" "%PAYSTACK_PATH%\lib\src\widgets\checkout\checkout_widget.dart.bak2"
    )
    
    if not exist "%PAYSTACK_PATH%\lib\src\widgets\sucessful_widget.dart.bak2" (
        copy "%PAYSTACK_PATH%\lib\src\widgets\sucessful_widget.dart" "%PAYSTACK_PATH%\lib\src\widgets\sucessful_widget.dart.bak2"
    )
    
    if not exist "%PAYSTACK_PATH%\lib\src\widgets\buttons.dart.bak2" (
        copy "%PAYSTACK_PATH%\lib\src\widgets\buttons.dart" "%PAYSTACK_PATH%\lib\src\widgets\buttons.dart.bak2"
    )
    
    echo Applying precise fixes...
    
    echo Fixing checkout_widget.dart...
    powershell -Command "(Get-Content '%PAYSTACK_PATH%\lib\src\widgets\checkout\checkout_widget.dart.bak') -replace 'final accentColor = Theme.of\(context\).accentColor;', 'final accentColor = Theme.of(context).colorScheme.secondary;' | Set-Content '%PAYSTACK_PATH%\lib\src\widgets\checkout\checkout_widget.dart'"
    
    echo Fixing sucessful_widget.dart...
    powershell -Command "(Get-Content '%PAYSTACK_PATH%\lib\src\widgets\sucessful_widget.dart.bak') -replace 'final accentColor = Theme.of\(context\).accentColor;', 'final accentColor = Theme.of(context).colorScheme.secondary;' | Set-Content '%PAYSTACK_PATH%\lib\src\widgets\sucessful_widget.dart'"
    
    echo Fixing buttons.dart...
    powershell -Command "(Get-Content '%PAYSTACK_PATH%\lib\src\widgets\buttons.dart.bak') -replace '.copyWith\(accentColor: Colors.white\)', '.copyWith(primaryColor: Colors.white)' | Set-Content '%PAYSTACK_PATH%\lib\src\widgets\buttons.dart'"
    
    echo Fixes applied successfully!
) else (
    echo Could not find flutter_paystack package at %PAYSTACK_PATH%
    echo Please run 'flutter pub get' first to download the package.
)

echo Done.
