import 'package:fit_4_force/shared/models/base_model.dart';
import 'package:flutter/material.dart';
import 'package:fit_4_force/features/community/models/comment_model.dart';

/// Model representing a community post
class PostModel extends BaseModel {
  final String userId;
  final String userName;
  final String? userProfileImageUrl;
  final String title;
  final String content;
  final String agency;
  final List<String> tags;
  final int likesCount;
  final int commentsCount;
  final bool isLikedByCurrentUser;
  final List<CommentModel>? comments;
  final String? imageUrl;
  final IconData icon;
  final Color color;

  const PostModel({
    required super.id,
    required super.createdAt,
    super.updatedAt,
    required this.userId,
    required this.userName,
    this.userProfileImageUrl,
    required this.title,
    required this.content,
    required this.agency,
    required this.tags,
    required this.likesCount,
    required this.commentsCount,
    required this.isLikedByCurrentUser,
    this.comments,
    this.imageUrl,
    required this.icon,
    required this.color,
  });

  @override
  List<Object?> get props => [
        ...super.props,
        userId,
        userName,
        userProfileImageUrl,
        title,
        content,
        agency,
        tags,
        likesCount,
        commentsCount,
        isLikedByCurrentUser,
        comments,
        imageUrl,
        icon,
        color,
      ];

  @override
  Map<String, dynamic> toJson() {
    return {
      ...super.toJson(),
      'userId': userId,
      'userName': userName,
      'userProfileImageUrl': userProfileImageUrl,
      'title': title,
      'content': content,
      'agency': agency,
      'tags': tags,
      'likesCount': likesCount,
      'commentsCount': commentsCount,
      'isLikedByCurrentUser': isLikedByCurrentUser,
      'imageUrl': imageUrl,
    };
  }

  factory PostModel.fromJson(Map<String, dynamic> json) {
    return PostModel(
      id: json['id'] as String,
      createdAt: DateTime.parse(json['createdAt'] as String),
      updatedAt: json['updatedAt'] != null
          ? DateTime.parse(json['updatedAt'] as String)
          : null,
      userId: json['userId'] as String,
      userName: json['userName'] as String,
      userProfileImageUrl: json['userProfileImageUrl'] as String?,
      title: json['title'] as String,
      content: json['content'] as String,
      agency: json['agency'] as String,
      tags: List<String>.from(json['tags'] as List),
      likesCount: json['likesCount'] as int,
      commentsCount: json['commentsCount'] as int,
      isLikedByCurrentUser: json['isLikedByCurrentUser'] as bool,
      imageUrl: json['imageUrl'] as String?,
      comments: json['comments'] != null
          ? List<CommentModel>.from(
              (json['comments'] as List).map(
                (comment) => CommentModel.fromJson(comment),
              ),
            )
          : null,
      // These are UI-specific properties that aren't stored in the JSON
      icon: Icons.forum,
      color: Colors.purple,
    );
  }

  @override
  PostModel copyWith({
    String? id,
    DateTime? createdAt,
    DateTime? updatedAt,
    String? userId,
    String? userName,
    String? userProfileImageUrl,
    String? title,
    String? content,
    String? agency,
    List<String>? tags,
    int? likesCount,
    int? commentsCount,
    bool? isLikedByCurrentUser,
    List<CommentModel>? comments,
    String? imageUrl,
    IconData? icon,
    Color? color,
  }) {
    return PostModel(
      id: id ?? this.id,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      userId: userId ?? this.userId,
      userName: userName ?? this.userName,
      userProfileImageUrl: userProfileImageUrl ?? this.userProfileImageUrl,
      title: title ?? this.title,
      content: content ?? this.content,
      agency: agency ?? this.agency,
      tags: tags ?? this.tags,
      likesCount: likesCount ?? this.likesCount,
      commentsCount: commentsCount ?? this.commentsCount,
      isLikedByCurrentUser: isLikedByCurrentUser ?? this.isLikedByCurrentUser,
      comments: comments ?? this.comments,
      imageUrl: imageUrl ?? this.imageUrl,
      icon: icon ?? this.icon,
      color: color ?? this.color,
    );
  }
}
