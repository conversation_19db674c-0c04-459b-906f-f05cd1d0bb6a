import 'package:flutter/material.dart';
import 'package:fit_4_force/core/theme/app_theme.dart';
import 'package:fit_4_force/features/fitness/models/recovery_model.dart';
import 'package:fit_4_force/shared/models/user_model.dart';
import 'package:fit_4_force/shared/widgets/base_button.dart';

class RecoveryScreen extends StatefulWidget {
  final UserModel user;

  const RecoveryScreen({super.key, required this.user});

  @override
  State<RecoveryScreen> createState() => _RecoveryScreenState();
}

class _RecoveryScreenState extends State<RecoveryScreen>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;
  final TextEditingController _searchController = TextEditingController();
  String _selectedCategory = 'All';
  final List<String> _categories = [
    'All',
    'Stretching',
    'Foam Rolling',
    'Ice Bath',
    'Massage',
    'Sleep',
  ];

  // Mock data for recovery sessions
  final List<RecoverySessionModel> _recoverySessions = [
    RecoverySessionModel(
      id: '1',
      name: 'Post-Run Recovery',
      description:
          'A comprehensive recovery routine designed for runners to prevent injuries and reduce muscle soreness.',
      imageUrl: 'assets/images/content/recovery1.jpg',
      category: 'Stretching',
      durationMinutes: 20,
      exercises: [
        RecoveryExerciseModel(
          id: '1',
          name: 'Hamstring Stretch',
          description:
              'Stretches the hamstring muscles to reduce tightness and prevent injury.',
          imageUrl: 'assets/images/content/hamstring_stretch.jpg',
          durationSeconds: 60,
          sets: 2,
          repetitions: 1,
          instructions: [
            'Sit on the floor with one leg extended and the other bent.',
            'Reach toward your toes on the extended leg.',
            'Hold for 30 seconds, then switch legs.',
          ],
          targetAreas: ['Hamstrings', 'Lower Back'],
          equipment: 'None',
        ),
        RecoveryExerciseModel(
          id: '2',
          name: 'Quad Stretch',
          description:
              'Stretches the quadriceps muscles to reduce tightness and prevent injury.',
          imageUrl: 'assets/images/content/quad_stretch.jpg',
          durationSeconds: 60,
          sets: 2,
          repetitions: 1,
          instructions: [
            'Stand on one leg and grab your ankle with your hand.',
            'Pull your heel toward your buttocks.',
            'Hold for 30 seconds, then switch legs.',
          ],
          targetAreas: ['Quadriceps'],
          equipment: 'None',
        ),
      ],
      icon: Icons.self_improvement,
      color: Colors.blue,
      isPremium: false,
      targetAreas: ['Legs', 'Lower Back'],
      intensity: 'Light',
    ),
    RecoverySessionModel(
      id: '2',
      name: 'Foam Rolling Routine',
      description:
          'A foam rolling routine to release muscle tension and improve mobility.',
      imageUrl: 'assets/images/content/recovery2.jpg',
      category: 'Foam Rolling',
      durationMinutes: 15,
      exercises: [],
      icon: Icons.fitness_center,
      color: Colors.green,
      isPremium: true,
      targetAreas: ['Full Body'],
      intensity: 'Moderate',
    ),
    RecoverySessionModel(
      id: '3',
      name: 'Ice Bath Recovery',
      description:
          'An ice bath protocol to reduce inflammation and speed up recovery after intense training.',
      imageUrl: 'assets/images/content/recovery3.jpg',
      category: 'Ice Bath',
      durationMinutes: 10,
      exercises: [],
      icon: Icons.ac_unit,
      color: Colors.cyan,
      isPremium: true,
      targetAreas: ['Full Body'],
      intensity: 'Intense',
    ),
  ];

  // Mock data for recovery logs
  final List<RecoveryLogModel> _recoveryLogs = [
    RecoveryLogModel(
      id: '1',
      date: DateTime.now().subtract(const Duration(days: 1)),
      sessionId: '1',
      sessionName: 'Post-Run Recovery',
      durationMinutes: 20,
      perceivedEffectiveness: 4,
      notes:
          'Felt much better after this session. Reduced soreness in my legs.',
      painAreas: ['Calves', 'Hamstrings'],
      painLevel: 6,
      recoveryLevel: 7,
      energyLevel: 6,
      sleepQuality: 8,
      sleepHours: 7,
    ),
    RecoveryLogModel(
      id: '2',
      date: DateTime.now().subtract(const Duration(days: 3)),
      sessionId: '2',
      sessionName: 'Foam Rolling Routine',
      durationMinutes: 15,
      perceivedEffectiveness: 5,
      notes: 'The foam rolling really helped with my tight IT bands.',
      painAreas: ['IT Band', 'Quads'],
      painLevel: 7,
      recoveryLevel: 8,
      energyLevel: 7,
      sleepQuality: 7,
      sleepHours: 8,
    ),
  ];

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 3, vsync: this);
  }

  @override
  void dispose() {
    _tabController.dispose();
    _searchController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text(
          'Recovery Tracking',
          style: TextStyle(fontWeight: FontWeight.bold),
        ),
        actions: [
          IconButton(
            icon: const Icon(Icons.search),
            onPressed: () {
              _showSearchDialog();
            },
          ),
          IconButton(
            icon: const Icon(Icons.filter_list),
            onPressed: () {
              _showFilterDialog();
            },
          ),
        ],
        bottom: TabBar(
          controller: _tabController,
          labelColor: AppTheme.primaryColor,
          unselectedLabelColor: AppTheme.textSecondaryLight,
          indicatorColor: AppTheme.primaryColor,
          indicatorWeight: 3,
          tabs: const [
            Tab(text: 'RECOVERY SESSIONS'),
            Tab(text: 'MY LOGS'),
            Tab(text: 'INSIGHTS'),
          ],
        ),
      ),
      body: TabBarView(
        controller: _tabController,
        children: [
          _buildRecoverySessionsTab(),
          _buildMyLogsTab(),
          _buildInsightsTab(),
        ],
      ),
      floatingActionButton: FloatingActionButton(
        backgroundColor: AppTheme.primaryColor,
        child: const Icon(Icons.add),
        onPressed: () {
          _showLogRecoveryDialog();
        },
      ),
    );
  }

  Widget _buildRecoverySessionsTab() {
    final filteredSessions =
        _selectedCategory == 'All'
            ? _recoverySessions
            : _recoverySessions
                .where((session) => session.category == _selectedCategory)
                .toList();

    return filteredSessions.isEmpty
        ? _buildEmptyState(
          'No recovery sessions available',
          'Check back later for new recovery sessions!',
        )
        : ListView.builder(
          padding: const EdgeInsets.all(16),
          itemCount: filteredSessions.length,
          itemBuilder: (context, index) {
            return _buildRecoverySessionCard(filteredSessions[index]);
          },
        );
  }

  Widget _buildMyLogsTab() {
    return _recoveryLogs.isEmpty
        ? _buildEmptyState(
          'No recovery logs yet',
          'Log your recovery sessions to track your progress!',
        )
        : ListView.builder(
          padding: const EdgeInsets.all(16),
          itemCount: _recoveryLogs.length,
          itemBuilder: (context, index) {
            return _buildRecoveryLogCard(_recoveryLogs[index]);
          },
        );
  }

  Widget _buildInsightsTab() {
    if (_recoveryLogs.isEmpty) {
      return _buildEmptyState(
        'No insights available',
        'Log your recovery sessions to generate insights!',
      );
    }

    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Recovery Trends',
            style: Theme.of(
              context,
            ).textTheme.titleLarge?.copyWith(fontWeight: FontWeight.bold),
          ),
          const SizedBox(height: 16),
          _buildTrendsCard(),
          const SizedBox(height: 24),
          Text(
            'Pain Areas',
            style: Theme.of(
              context,
            ).textTheme.titleLarge?.copyWith(fontWeight: FontWeight.bold),
          ),
          const SizedBox(height: 16),
          _buildPainAreasCard(),
          const SizedBox(height: 24),
          Text(
            'Sleep Quality',
            style: Theme.of(
              context,
            ).textTheme.titleLarge?.copyWith(fontWeight: FontWeight.bold),
          ),
          const SizedBox(height: 16),
          _buildSleepQualityCard(),
        ],
      ),
    );
  }

  Widget _buildEmptyState(String title, String message) {
    return Center(
      child: Padding(
        padding: const EdgeInsets.all(24.0),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.self_improvement,
              size: 80,
              color: Colors.grey.withValues(alpha: 0.3 * 255),
            ),
            const SizedBox(height: 16),
            Text(
              title,
              style: const TextStyle(
                fontSize: 20,
                fontWeight: FontWeight.bold,
                color: Colors.black87,
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 8),
            Text(
              message,
              style: TextStyle(fontSize: 16, color: Colors.grey.shade600),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 24),
            BaseButton(
              text: 'Log Recovery',
              icon: Icons.add,
              backgroundColor: AppTheme.primaryColor,
              onPressed: () {
                _showLogRecoveryDialog();
              },
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildRecoverySessionCard(RecoverySessionModel session) {
    return Card(
      margin: const EdgeInsets.only(bottom: 16),
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
      child: InkWell(
        onTap: () => _navigateToSessionDetail(session),
        borderRadius: BorderRadius.circular(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Session image
            Container(
              height: 150,
              width: double.infinity,
              decoration: BoxDecoration(
                borderRadius: const BorderRadius.only(
                  topLeft: Radius.circular(16),
                  topRight: Radius.circular(16),
                ),
                image: DecorationImage(
                  image: AssetImage(session.imageUrl),
                  fit: BoxFit.cover,
                ),
              ),
              child: Stack(
                children: [
                  // Gradient overlay
                  Container(
                    decoration: BoxDecoration(
                      borderRadius: const BorderRadius.only(
                        topLeft: Radius.circular(16),
                        topRight: Radius.circular(16),
                      ),
                      gradient: LinearGradient(
                        begin: Alignment.topCenter,
                        end: Alignment.bottomCenter,
                        colors: [
                          Colors.transparent,
                          Colors.black.withValues(alpha: 0.7 * 255),
                        ],
                      ),
                    ),
                  ),
                  // Session name and category
                  Positioned(
                    bottom: 16,
                    left: 16,
                    right: 16,
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          session.name,
                          style: const TextStyle(
                            fontSize: 20,
                            fontWeight: FontWeight.bold,
                            color: Colors.white,
                            shadows: [
                              Shadow(
                                offset: Offset(0, 1),
                                blurRadius: 3.0,
                                color: Colors.black,
                              ),
                            ],
                          ),
                        ),
                        const SizedBox(height: 4),
                        Row(
                          children: [
                            Container(
                              padding: const EdgeInsets.symmetric(
                                horizontal: 8,
                                vertical: 2,
                              ),
                              decoration: BoxDecoration(
                                color: session.color.withValues(
                                  alpha: 0.8 * 255,
                                ),
                                borderRadius: BorderRadius.circular(12),
                              ),
                              child: Text(
                                session.category,
                                style: const TextStyle(
                                  fontSize: 12,
                                  color: Colors.white,
                                  fontWeight: FontWeight.w500,
                                ),
                              ),
                            ),
                            const SizedBox(width: 8),
                            Container(
                              padding: const EdgeInsets.symmetric(
                                horizontal: 8,
                                vertical: 2,
                              ),
                              decoration: BoxDecoration(
                                color: _getIntensityColor(
                                  session.intensity,
                                ).withValues(alpha: 0.8 * 255),
                                borderRadius: BorderRadius.circular(12),
                              ),
                              child: Text(
                                session.intensity,
                                style: const TextStyle(
                                  fontSize: 12,
                                  color: Colors.white,
                                  fontWeight: FontWeight.w500,
                                ),
                              ),
                            ),
                            if (session.isPremium) ...[
                              const SizedBox(width: 8),
                              Container(
                                padding: const EdgeInsets.symmetric(
                                  horizontal: 8,
                                  vertical: 2,
                                ),
                                decoration: BoxDecoration(
                                  color: Colors.amber.withValues(
                                    alpha: 0.8 * 255,
                                  ),
                                  borderRadius: BorderRadius.circular(12),
                                ),
                                child: const Text(
                                  'Premium',
                                  style: TextStyle(
                                    fontSize: 12,
                                    color: Colors.white,
                                    fontWeight: FontWeight.w500,
                                  ),
                                ),
                              ),
                            ],
                          ],
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ),
            // Session details
            Padding(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    session.description,
                    style: const TextStyle(fontSize: 14, color: Colors.black87),
                    maxLines: 2,
                    overflow: TextOverflow.ellipsis,
                  ),
                  const SizedBox(height: 16),
                  // Session info
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      _buildInfoItem(
                        Icons.timer,
                        '${session.durationMinutes} min',
                        Colors.blue,
                      ),
                      _buildInfoItem(
                        Icons.fitness_center,
                        '${session.exercises.length} exercises',
                        Colors.green,
                      ),
                      _buildInfoItem(
                        Icons.accessibility,
                        session.targetAreas.join(', '),
                        Colors.orange,
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildRecoveryLogCard(RecoveryLogModel log) {
    return Card(
      margin: const EdgeInsets.only(bottom: 16),
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Log header
            Row(
              children: [
                Container(
                  padding: const EdgeInsets.all(12),
                  decoration: BoxDecoration(
                    color: Colors.teal.withValues(alpha: 0.1 * 255),
                    shape: BoxShape.circle,
                  ),
                  child: const Icon(
                    Icons.self_improvement,
                    color: Colors.teal,
                    size: 24,
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        log.sessionName,
                        style: const TextStyle(
                          fontSize: 18,
                          fontWeight: FontWeight.bold,
                          color: Colors.black87,
                        ),
                      ),
                      const SizedBox(height: 4),
                      Text(
                        log.formattedDate,
                        style: TextStyle(
                          fontSize: 14,
                          color: Colors.grey.shade600,
                        ),
                      ),
                    ],
                  ),
                ),
                Container(
                  padding: const EdgeInsets.symmetric(
                    horizontal: 12,
                    vertical: 6,
                  ),
                  decoration: BoxDecoration(
                    color: _getEffectivenessColor(
                      log.perceivedEffectiveness,
                    ).withValues(alpha: 0.1 * 255),
                    borderRadius: BorderRadius.circular(16),
                  ),
                  child: Text(
                    '${log.perceivedEffectiveness}/5',
                    style: TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                      color: _getEffectivenessColor(log.perceivedEffectiveness),
                    ),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            // Log details
            if (log.notes.isNotEmpty) ...[
              Text(
                log.notes,
                style: const TextStyle(fontSize: 14, color: Colors.black87),
                maxLines: 2,
                overflow: TextOverflow.ellipsis,
              ),
              const SizedBox(height: 16),
            ],
            // Metrics
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                _buildMetricItem(
                  'Pain',
                  '${log.painLevel}/10',
                  _getPainLevelColor(log.painLevel),
                ),
                _buildMetricItem(
                  'Recovery',
                  '${log.recoveryLevel}/10',
                  _getRecoveryLevelColor(log.recoveryLevel),
                ),
                _buildMetricItem(
                  'Energy',
                  '${log.energyLevel}/10',
                  _getEnergyLevelColor(log.energyLevel),
                ),
                _buildMetricItem(
                  'Sleep',
                  '${log.sleepQuality}/10',
                  _getSleepQualityColor(log.sleepQuality),
                ),
              ],
            ),
            const SizedBox(height: 12),
            // Pain areas
            if (log.painAreas.isNotEmpty) ...[
              Wrap(
                spacing: 8,
                runSpacing: 8,
                children:
                    log.painAreas.map((area) {
                      return Container(
                        padding: const EdgeInsets.symmetric(
                          horizontal: 8,
                          vertical: 4,
                        ),
                        decoration: BoxDecoration(
                          color: Colors.red.withValues(alpha: 0.1 * 255),
                          borderRadius: BorderRadius.circular(12),
                        ),
                        child: Text(
                          area,
                          style: const TextStyle(
                            fontSize: 12,
                            color: Colors.red,
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                      );
                    }).toList(),
              ),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildInfoItem(IconData icon, String text, Color color) {
    return Column(
      children: [
        Container(
          padding: const EdgeInsets.all(8),
          decoration: BoxDecoration(
            color: color.withValues(alpha: 0.1 * 255),
            shape: BoxShape.circle,
          ),
          child: Icon(icon, color: color, size: 16),
        ),
        const SizedBox(height: 4),
        Text(
          text,
          style: TextStyle(fontSize: 12, color: Colors.grey.shade700),
          maxLines: 1,
          overflow: TextOverflow.ellipsis,
        ),
      ],
    );
  }

  Widget _buildMetricItem(String label, String value, Color color) {
    return Column(
      children: [
        Text(
          value,
          style: TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.bold,
            color: color,
          ),
        ),
        const SizedBox(height: 4),
        Text(
          label,
          style: TextStyle(fontSize: 12, color: Colors.grey.shade600),
        ),
      ],
    );
  }

  Widget _buildTrendsCard() {
    // Placeholder for trends chart
    return Container(
      height: 200,
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withValues(alpha: 0.1 * 255),
            blurRadius: 10,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Center(
        child: Text(
          'Recovery Trends Chart',
          style: TextStyle(color: AppTheme.textSecondaryLight),
        ),
      ),
    );
  }

  Widget _buildPainAreasCard() {
    // Placeholder for pain areas visualization
    return Container(
      height: 200,
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withValues(alpha: 0.1 * 255),
            blurRadius: 10,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Center(
        child: Text(
          'Pain Areas Visualization',
          style: TextStyle(color: AppTheme.textSecondaryLight),
        ),
      ),
    );
  }

  Widget _buildSleepQualityCard() {
    // Placeholder for sleep quality chart
    return Container(
      height: 200,
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withValues(alpha: 0.1 * 255),
            blurRadius: 10,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Center(
        child: Text(
          'Sleep Quality Chart',
          style: TextStyle(color: AppTheme.textSecondaryLight),
        ),
      ),
    );
  }

  void _showSearchDialog() {
    showDialog(
      context: context,
      builder: (context) {
        return AlertDialog(
          title: const Text('Search Recovery Sessions'),
          content: TextField(
            controller: _searchController,
            decoration: const InputDecoration(
              hintText: 'Enter keywords...',
              prefixIcon: Icon(Icons.search),
            ),
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.pop(context),
              child: const Text('CANCEL'),
            ),
            TextButton(
              onPressed: () {
                // Perform search
                Navigator.pop(context);
                // Update UI with search results
              },
              child: const Text('SEARCH'),
            ),
          ],
        );
      },
    );
  }

  void _showFilterDialog() {
    showDialog(
      context: context,
      builder: (context) {
        return StatefulBuilder(
          builder: (context, setState) {
            return AlertDialog(
              title: const Text('Filter Recovery Sessions'),
              content: Column(
                mainAxisSize: MainAxisSize.min,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  const Text(
                    'Category',
                    style: TextStyle(fontWeight: FontWeight.bold, fontSize: 16),
                  ),
                  const SizedBox(height: 8),
                  Wrap(
                    spacing: 8,
                    runSpacing: 8,
                    children:
                        _categories.map((category) {
                          return ChoiceChip(
                            label: Text(category),
                            selected: _selectedCategory == category,
                            onSelected: (selected) {
                              setState(() {
                                _selectedCategory = category;
                              });
                            },
                          );
                        }).toList(),
                  ),
                ],
              ),
              actions: [
                TextButton(
                  onPressed: () => Navigator.pop(context),
                  child: const Text('CANCEL'),
                ),
                TextButton(
                  onPressed: () {
                    // Apply filters
                    Navigator.pop(context);
                    // Update UI with filtered sessions
                    this.setState(() {});
                  },
                  child: const Text('APPLY'),
                ),
              ],
            );
          },
        );
      },
    );
  }

  void _showLogRecoveryDialog() {
    final sessionController = TextEditingController();
    final notesController = TextEditingController();
    int perceivedEffectiveness = 3;
    int painLevel = 5;
    int recoveryLevel = 5;
    int energyLevel = 5;
    int sleepQuality = 5;
    int sleepHours = 7;
    List<String> selectedPainAreas = [];
    final painAreas = [
      'Neck',
      'Shoulders',
      'Back',
      'Chest',
      'Arms',
      'Abs',
      'Hips',
      'Glutes',
      'Quads',
      'Hamstrings',
      'Calves',
      'Ankles',
      'Feet',
    ];

    showDialog(
      context: context,
      builder: (context) {
        return StatefulBuilder(
          builder: (context, setState) {
            return AlertDialog(
              title: const Text('Log Recovery Session'),
              content: SingleChildScrollView(
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    DropdownButtonFormField<String>(
                      decoration: const InputDecoration(
                        labelText: 'Recovery Session',
                      ),
                      items:
                          _recoverySessions.map((session) {
                            return DropdownMenuItem<String>(
                              value: session.id,
                              child: Text(session.name),
                            );
                          }).toList(),
                      onChanged: (value) {
                        sessionController.text = value ?? '';
                      },
                    ),
                    const SizedBox(height: 16),
                    TextField(
                      controller: notesController,
                      decoration: const InputDecoration(
                        labelText: 'Notes',
                        hintText: 'How did you feel after the session?',
                      ),
                      maxLines: 3,
                    ),
                    const SizedBox(height: 16),
                    const Text(
                      'Effectiveness (1-5)',
                      style: TextStyle(fontWeight: FontWeight.bold),
                    ),
                    Slider(
                      value: perceivedEffectiveness.toDouble(),
                      min: 1,
                      max: 5,
                      divisions: 4,
                      label: perceivedEffectiveness.toString(),
                      onChanged: (value) {
                        setState(() {
                          perceivedEffectiveness = value.toInt();
                        });
                      },
                    ),
                    const SizedBox(height: 16),
                    const Text(
                      'Pain Level (1-10)',
                      style: TextStyle(fontWeight: FontWeight.bold),
                    ),
                    Slider(
                      value: painLevel.toDouble(),
                      min: 1,
                      max: 10,
                      divisions: 9,
                      label: painLevel.toString(),
                      onChanged: (value) {
                        setState(() {
                          painLevel = value.toInt();
                        });
                      },
                    ),
                    const SizedBox(height: 16),
                    const Text(
                      'Pain Areas',
                      style: TextStyle(fontWeight: FontWeight.bold),
                    ),
                    Wrap(
                      spacing: 8,
                      runSpacing: 8,
                      children:
                          painAreas.map((area) {
                            return FilterChip(
                              label: Text(area),
                              selected: selectedPainAreas.contains(area),
                              onSelected: (selected) {
                                setState(() {
                                  if (selected) {
                                    selectedPainAreas.add(area);
                                  } else {
                                    selectedPainAreas.remove(area);
                                  }
                                });
                              },
                            );
                          }).toList(),
                    ),
                    const SizedBox(height: 16),
                    const Text(
                      'Recovery Level (1-10)',
                      style: TextStyle(fontWeight: FontWeight.bold),
                    ),
                    Slider(
                      value: recoveryLevel.toDouble(),
                      min: 1,
                      max: 10,
                      divisions: 9,
                      label: recoveryLevel.toString(),
                      onChanged: (value) {
                        setState(() {
                          recoveryLevel = value.toInt();
                        });
                      },
                    ),
                    const SizedBox(height: 16),
                    const Text(
                      'Energy Level (1-10)',
                      style: TextStyle(fontWeight: FontWeight.bold),
                    ),
                    Slider(
                      value: energyLevel.toDouble(),
                      min: 1,
                      max: 10,
                      divisions: 9,
                      label: energyLevel.toString(),
                      onChanged: (value) {
                        setState(() {
                          energyLevel = value.toInt();
                        });
                      },
                    ),
                    const SizedBox(height: 16),
                    const Text(
                      'Sleep Quality (1-10)',
                      style: TextStyle(fontWeight: FontWeight.bold),
                    ),
                    Slider(
                      value: sleepQuality.toDouble(),
                      min: 1,
                      max: 10,
                      divisions: 9,
                      label: sleepQuality.toString(),
                      onChanged: (value) {
                        setState(() {
                          sleepQuality = value.toInt();
                        });
                      },
                    ),
                    const SizedBox(height: 16),
                    const Text(
                      'Sleep Hours',
                      style: TextStyle(fontWeight: FontWeight.bold),
                    ),
                    Slider(
                      value: sleepHours.toDouble(),
                      min: 4,
                      max: 12,
                      divisions: 8,
                      label: sleepHours.toString(),
                      onChanged: (value) {
                        setState(() {
                          sleepHours = value.toInt();
                        });
                      },
                    ),
                  ],
                ),
              ),
              actions: [
                TextButton(
                  onPressed: () => Navigator.pop(context),
                  child: const Text('CANCEL'),
                ),
                TextButton(
                  onPressed: () {
                    // Create recovery log
                    if (sessionController.text.isNotEmpty) {
                      final session = _recoverySessions.firstWhere(
                        (s) => s.id == sessionController.text,
                      );

                      setState(() {
                        _recoveryLogs.insert(
                          0,
                          RecoveryLogModel(
                            id:
                                DateTime.now().millisecondsSinceEpoch
                                    .toString(),
                            date: DateTime.now(),
                            sessionId: session.id,
                            sessionName: session.name,
                            durationMinutes: session.durationMinutes,
                            perceivedEffectiveness: perceivedEffectiveness,
                            notes: notesController.text,
                            painAreas: selectedPainAreas,
                            painLevel: painLevel,
                            recoveryLevel: recoveryLevel,
                            energyLevel: energyLevel,
                            sleepQuality: sleepQuality,
                            sleepHours: sleepHours,
                          ),
                        );
                      });
                      Navigator.pop(context);
                      ScaffoldMessenger.of(context).showSnackBar(
                        const SnackBar(
                          content: Text(
                            'Recovery session logged successfully!',
                          ),
                        ),
                      );
                    }
                  },
                  child: const Text('LOG'),
                ),
              ],
            );
          },
        );
      },
    );
  }

  void _navigateToSessionDetail(RecoverySessionModel session) {
    // Navigate to session detail screen
    if (session.isPremium && !widget.user.isPremium) {
      _showPremiumDialog();
    } else {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('Viewing session: ${session.name}')),
      );
    }
  }

  void _showPremiumDialog() {
    showDialog(
      context: context,
      builder: (context) {
        return AlertDialog(
          title: const Text('Premium Content'),
          content: const Text(
            'This recovery session is only available to premium users. Upgrade to access all premium content!',
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.pop(context),
              child: const Text('CANCEL'),
            ),
            TextButton(
              onPressed: () {
                Navigator.pop(context);
                // Navigate to subscription screen
              },
              child: const Text('UPGRADE'),
            ),
          ],
        );
      },
    );
  }

  Color _getIntensityColor(String intensity) {
    switch (intensity) {
      case 'Light':
        return Colors.green;
      case 'Moderate':
        return Colors.orange;
      case 'Intense':
        return Colors.red;
      default:
        return Colors.grey;
    }
  }

  Color _getEffectivenessColor(int effectiveness) {
    if (effectiveness >= 4) return Colors.green;
    if (effectiveness >= 2) return Colors.orange;
    return Colors.red;
  }

  Color _getPainLevelColor(int level) {
    if (level <= 3) return Colors.green;
    if (level <= 6) return Colors.orange;
    return Colors.red;
  }

  Color _getRecoveryLevelColor(int level) {
    if (level >= 7) return Colors.green;
    if (level >= 4) return Colors.orange;
    return Colors.red;
  }

  Color _getEnergyLevelColor(int level) {
    if (level >= 7) return Colors.green;
    if (level >= 4) return Colors.orange;
    return Colors.red;
  }

  Color _getSleepQualityColor(int quality) {
    if (quality >= 7) return Colors.green;
    if (quality >= 4) return Colors.orange;
    return Colors.red;
  }
}
