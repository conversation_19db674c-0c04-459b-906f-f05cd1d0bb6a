#!/usr/bin/env python3
"""
Script to prepare legal documents for InfinityFree deployment
This script copies the necessary files and removes GitHub-specific files
"""

import os
import shutil
import sys
from pathlib import Path

def main():
    print("🌐 Preparing Fit4Force Legal Documents for InfinityFree Deployment")
    print("=" * 60)
    
    # Define paths
    docs_dir = Path("docs")
    output_dir = Path("infinityfree_upload")
    
    # Check if docs directory exists
    if not docs_dir.exists():
        print("❌ Error: 'docs' directory not found!")
        print("   Make sure you're running this script from the project root.")
        sys.exit(1)
    
    # Create output directory
    if output_dir.exists():
        print(f"🗑️  Removing existing {output_dir} directory...")
        shutil.rmtree(output_dir)
    
    output_dir.mkdir()
    print(f"📁 Created {output_dir} directory")
    
    # Files to copy (exclude GitHub-specific files)
    files_to_copy = [
        "index.html",
        "terms-of-service.html",
        "privacy-policy.html", 
        "cookie-policy.html",
        "disclaimer.html",
        "contact.html",
        "refund-policy.html",
        "data-retention-security.html",
        "community-policy.html",
        "styles.css"
    ]
    
    # Copy files
    copied_files = []
    for file_name in files_to_copy:
        source_file = docs_dir / file_name
        dest_file = output_dir / file_name
        
        if source_file.exists():
            shutil.copy2(source_file, dest_file)
            copied_files.append(file_name)
            print(f"✅ Copied: {file_name}")
        else:
            print(f"⚠️  Warning: {file_name} not found in docs directory")
    
    print("\n" + "=" * 60)
    print("📋 DEPLOYMENT SUMMARY")
    print("=" * 60)
    print(f"📁 Files prepared in: {output_dir}")
    print(f"📄 Total files copied: {len(copied_files)}")
    
    print("\n📝 Files ready for upload:")
    for file_name in copied_files:
        print(f"   • {file_name}")
    
    print("\n🚀 NEXT STEPS:")
    print("1. Upload all files from 'infinityfree_upload' folder to your InfinityFree /htdocs/ directory")
    print("2. Use FileZilla or InfinityFree File Manager")
    print("3. Update your Flutter app with your InfinityFree domain URL")
    print("4. Test all legal document links")
    
    print("\n📖 For detailed instructions, see: INFINITYFREE_DEPLOYMENT.md")
    print("🎉 Your legal documents are ready for InfinityFree deployment!")

if __name__ == "__main__":
    main()
