import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:fit_4_force/shared/models/subscription_model.dart';
import 'package:fit_4_force/shared/services/base_service.dart';
// Temporarily disabled due to compatibility issues
// import 'package:fit_4_force/shared/services/paystack_service.dart';
import 'package:fit_4_force/shared/services/mock_payment_service.dart';
import 'package:fit_4_force/core/di/service_locator.dart';
import 'package:flutter/material.dart';
import 'package:uuid/uuid.dart';

class SubscriptionService extends BaseService {
  // Use the MockPaymentService for payment processing
  // Temporarily using a mock service due to compatibility issues with flutter_paystack
  final MockPaymentService _paymentService =
      serviceLocator<MockPaymentService>();
  final BuildContext? _context;

  SubscriptionService({BuildContext? context}) : _context = context;

  // Generate a unique ID for the subscription
  String generateId() {
    return const Uuid().v4();
  }

  @override
  CollectionReference<Map<String, dynamic>> get collection =>
      firestore.collection('subscriptions');

  // Initialize Paystack
  Future<void> initPaystack() async {
    // PaystackService initializes itself in its constructor
  }

  // Get current subscription
  Future<SubscriptionModel?> getCurrentSubscription() async {
    try {
      if (!isAuthenticated) return null;

      final subscriptionQuery =
          await collection
              .where('userId', isEqualTo: currentUserId)
              .where('isActive', isEqualTo: true)
              .orderBy('expiryDate', descending: true)
              .limit(1)
              .get();

      if (subscriptionQuery.docs.isEmpty) return null;

      return SubscriptionModel.fromJson(subscriptionQuery.docs.first.data());
    } catch (e) {
      rethrow;
    }
  }

  // Check if user has active subscription
  Future<bool> hasActiveSubscription() async {
    try {
      final subscription = await getCurrentSubscription();
      if (subscription == null) return false;

      // Check if subscription is expired
      final now = DateTime.now();
      return subscription.expiryDate.isAfter(now);
    } catch (e) {
      return false;
    }
  }

  // Create a new subscription
  Future<SubscriptionModel> createSubscription({
    required String userId,
    required String transactionReference,
    required DateTime startDate,
    required DateTime expiryDate,
    required double amount,
  }) async {
    try {
      final subscription = SubscriptionModel(
        id: generateId(),
        createdAt: DateTime.now(),
        userId: userId,
        transactionReference: transactionReference,
        startDate: startDate,
        expiryDate: expiryDate,
        amount: amount,
        isActive: true,
        paymentMethod: 'Paystack',
        autoRenew: false,
      );

      await create(subscription.toJson());

      // Update user's premium status
      await firestore.collection('users').doc(userId).update({
        'isPremium': true,
        'premiumExpiryDate': expiryDate.toIso8601String(),
      });

      return subscription;
    } catch (e) {
      rethrow;
    }
  }

  // Cancel subscription
  Future<void> cancelSubscription(String subscriptionId) async {
    try {
      await update(subscriptionId, {
        'isActive': false,
        'updatedAt': DateTime.now().toIso8601String(),
      });

      // Update user's premium status
      if (!isAuthenticated) return;

      await firestore.collection('users').doc(currentUserId).update({
        'isPremium': false,
        'premiumExpiryDate': null,
      });
    } catch (e) {
      rethrow;
    }
  }

  // Renew subscription
  Future<SubscriptionModel> renewSubscription({
    required String transactionReference,
    required DateTime startDate,
    required DateTime expiryDate,
    required double amount,
  }) async {
    try {
      if (!isAuthenticated) {
        throw Exception('User not authenticated');
      }

      return await createSubscription(
        userId: currentUserId!,
        transactionReference: transactionReference,
        startDate: startDate,
        expiryDate: expiryDate,
        amount: amount,
      );
    } catch (e) {
      rethrow;
    }
  }

  // Process payment with MockPaymentService
  Future<Map<String, dynamic>> processPayment({
    required String email,
    required double amount,
    required String reference,
    required String fullName,
  }) async {
    try {
      if (_context == null) {
        throw Exception('Context is required for payment checkout');
      }

      // Use the MockPaymentService to process the payment
      final success = await _paymentService.processPayment(
        context: _context, // We've already checked that _context is not null
        email: email,
        fullName: fullName,
        amount: amount,
        onSuccess: (ref) {
          // Handle success callback
        },
        onError: (error) {
          // Handle error callback
        },
      );

      if (success) {
        // Payment successful
        return {
          'success': true,
          'reference': reference,
          'message': 'Payment successful',
        };
      } else {
        // Payment failed
        return {'success': false, 'message': 'Payment failed or was cancelled'};
      }
    } catch (e) {
      return {'success': false, 'message': e.toString()};
    }
  }

  // Get subscription history
  Future<List<SubscriptionModel>> getSubscriptionHistory() async {
    try {
      if (!isAuthenticated) return [];

      final subscriptionsQuery =
          await collection
              .where('userId', isEqualTo: currentUserId)
              .orderBy('createdAt', descending: true)
              .get();

      return subscriptionsQuery.docs
          .map((doc) => SubscriptionModel.fromJson(doc.data()))
          .toList();
    } catch (e) {
      rethrow;
    }
  }

  // Check if subscription is about to expire (within 3 days)
  Future<bool> isSubscriptionAboutToExpire() async {
    try {
      final subscription = await getCurrentSubscription();
      if (subscription == null) return false;

      final now = DateTime.now();
      final threeDaysFromNow = now.add(const Duration(days: 3));

      return subscription.expiryDate.isBefore(threeDaysFromNow) &&
          subscription.expiryDate.isAfter(now);
    } catch (e) {
      return false;
    }
  }
}
