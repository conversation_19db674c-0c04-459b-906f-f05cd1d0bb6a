import 'package:flutter/material.dart';
import 'package:fit_4_force/core/theme/app_theme.dart';
import 'package:fit_4_force/features/training/models/workout.dart';
import 'package:fit_4_force/features/training/services/training_plan_service.dart';
import 'package:fit_4_force/shared/services/auth_service.dart';
import 'package:intl/intl.dart';

class TrainingPlanScreen extends StatefulWidget {
  final TrainingPlan? plan;

  const TrainingPlanScreen({super.key, this.plan});

  @override
  State<TrainingPlanScreen> createState() => _TrainingPlanScreenState();
}

class _TrainingPlanScreenState extends State<TrainingPlanScreen>
    with SingleTickerProviderStateMixin {
  final TrainingPlanService _trainingService = TrainingPlanService();
  final AuthService _authService = AuthService();

  late TabController _tabController;
  bool _isLoading = true;
  TrainingPlan? _plan;

  // Group workouts by week
  final List<List<WorkoutDay>> _weeks = [];

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 4, vsync: this); // 4 weeks

    if (widget.plan != null) {
      _plan = widget.plan;
      _organizeWorkoutsByWeek();
      _isLoading = false;
    } else {
      _loadTrainingPlan();
    }
  }

  Future<void> _loadTrainingPlan() async {
    setState(() {
      _isLoading = true;
    });

    try {
      final userId = _authService.currentUserId;
      if (userId != null) {
        final plan = await _trainingService.getActiveTrainingPlan(userId);

        setState(() {
          _plan = plan;
          _organizeWorkoutsByWeek();
          _isLoading = false;
        });
      }
    } catch (e) {
      setState(() {
        _isLoading = false;
      });
    }
  }

  void _organizeWorkoutsByWeek() {
    if (_plan == null || _plan!.workouts.isEmpty) {
      return;
    }

    _weeks.clear();

    // Assuming 6 workouts per week (with 1 rest day)
    final workoutsPerWeek = 6;

    // Split workouts into weeks
    for (int i = 0; i < _plan!.workouts.length; i += workoutsPerWeek) {
      final end =
          (i + workoutsPerWeek <= _plan!.workouts.length)
              ? i + workoutsPerWeek
              : _plan!.workouts.length;

      _weeks.add(_plan!.workouts.sublist(i, end));
    }
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Your Training Plan'),
        backgroundColor: AppTheme.primaryColor,
        bottom:
            _isLoading || _plan == null
                ? null
                : TabBar(
                  controller: _tabController,
                  tabs: const [
                    Tab(text: 'Week 1'),
                    Tab(text: 'Week 2'),
                    Tab(text: 'Week 3'),
                    Tab(text: 'Week 4'),
                  ],
                ),
      ),
      body:
          _isLoading
              ? const Center(child: CircularProgressIndicator())
              : _plan == null
              ? _buildNoPlanView()
              : _buildPlanView(),
    );
  }

  Widget _buildNoPlanView() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          const Icon(Icons.fitness_center, size: 80, color: Colors.grey),
          const SizedBox(height: 16),
          Text(
            'No Active Training Plan',
            style: Theme.of(context).textTheme.titleLarge,
          ),
          const SizedBox(height: 8),
          Text(
            'Complete a fitness assessment to get started',
            style: Theme.of(context).textTheme.bodyMedium,
          ),
          const SizedBox(height: 24),
          ElevatedButton(
            onPressed: () {
              Navigator.pushNamed(context, '/fitness_assessment');
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: AppTheme.primaryColor,
              padding: const EdgeInsets.symmetric(horizontal: 32, vertical: 16),
            ),
            child: const Text('Start Assessment'),
          ),
        ],
      ),
    );
  }

  Widget _buildPlanView() {
    return Column(
      children: [
        // Progress indicator
        LinearProgressIndicator(
          value: _plan!.getCompletionPercentage() / 100,
          backgroundColor: Colors.grey[300],
          valueColor: AlwaysStoppedAnimation<Color>(AppTheme.primaryColor),
        ),

        // Plan details
        Padding(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                _plan!.title,
                style: Theme.of(
                  context,
                ).textTheme.titleLarge?.copyWith(fontWeight: FontWeight.bold),
              ),
              const SizedBox(height: 4),
              Text(
                _plan!.description,
                style: Theme.of(context).textTheme.bodyMedium,
              ),
              const SizedBox(height: 8),
              Row(
                children: [
                  Icon(Icons.calendar_today, size: 16, color: Colors.grey[600]),
                  const SizedBox(width: 4),
                  Text(
                    '${DateFormat('MMM d').format(_plan!.startDate)} - ${DateFormat('MMM d').format(_plan!.endDate)}',
                    style: TextStyle(color: Colors.grey[600]),
                  ),
                  const SizedBox(width: 16),
                  Icon(Icons.fitness_center, size: 16, color: Colors.grey[600]),
                  const SizedBox(width: 4),
                  Text(
                    _plan!.difficulty,
                    style: TextStyle(color: Colors.grey[600]),
                  ),
                ],
              ),
              const SizedBox(height: 8),
              Row(
                children: [
                  const Icon(Icons.check_circle, size: 16, color: Colors.green),
                  const SizedBox(width: 4),
                  Text(
                    '${_plan!.getCompletionPercentage().toStringAsFixed(0)}% Complete',
                    style: const TextStyle(
                      color: Colors.green,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),

        // Divider
        const Divider(height: 1),

        // Workouts by week
        Expanded(
          child: TabBarView(
            controller: _tabController,
            children:
                _weeks.map((weekWorkouts) {
                  return _buildWeekView(weekWorkouts);
                }).toList(),
          ),
        ),
      ],
    );
  }

  Widget _buildWeekView(List<WorkoutDay> workouts) {
    return ListView.builder(
      padding: const EdgeInsets.all(16),
      itemCount: workouts.length,
      itemBuilder: (context, index) {
        final workout = workouts[index];
        final isCompleted = _plan!.completedWorkouts[workout.id] ?? false;

        return Card(
          margin: const EdgeInsets.only(bottom: 16),
          elevation: 2,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(12),
          ),
          child: InkWell(
            onTap: () {
              Navigator.pushNamed(
                context,
                '/workout_detail',
                arguments: {
                  'workout': workout,
                  'planId': _plan!.id,
                  'isCompleted': isCompleted,
                },
              ).then((_) => _loadTrainingPlan());
            },
            borderRadius: BorderRadius.circular(12),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Workout header
                Container(
                  padding: const EdgeInsets.all(16),
                  decoration: BoxDecoration(
                    color: isCompleted ? Colors.green : AppTheme.primaryColor,
                    borderRadius: const BorderRadius.only(
                      topLeft: Radius.circular(12),
                      topRight: Radius.circular(12),
                    ),
                  ),
                  child: Row(
                    children: [
                      Icon(
                        isCompleted ? Icons.check_circle : Icons.fitness_center,
                        color: Colors.white,
                      ),
                      const SizedBox(width: 8),
                      Expanded(
                        child: Text(
                          workout.title,
                          style: const TextStyle(
                            color: Colors.white,
                            fontWeight: FontWeight.bold,
                            fontSize: 16,
                          ),
                        ),
                      ),
                      Container(
                        padding: const EdgeInsets.symmetric(
                          horizontal: 8,
                          vertical: 4,
                        ),
                        decoration: BoxDecoration(
                          color: Colors.white.withValues(
                            red: 255,
                            green: 255,
                            blue: 255,
                            alpha: 51, // 0.2 * 255 = 51
                          ),
                          borderRadius: BorderRadius.circular(16),
                        ),
                        child: Text(
                          '${workout.estimatedDurationMinutes} min',
                          style: const TextStyle(
                            color: Colors.white,
                            fontSize: 12,
                          ),
                        ),
                      ),
                    ],
                  ),
                ),

                // Workout details
                Padding(
                  padding: const EdgeInsets.all(16),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        workout.description,
                        style: Theme.of(context).textTheme.bodyMedium,
                      ),
                      const SizedBox(height: 16),
                      Row(
                        children: [
                          _buildWorkoutInfoChip(
                            Icons.category,
                            workout.focusArea,
                          ),
                          const SizedBox(width: 8),
                          _buildWorkoutInfoChip(
                            Icons.fitness_center,
                            workout.difficulty,
                          ),
                          const SizedBox(width: 8),
                          _buildWorkoutInfoChip(
                            Icons.fitness_center,
                            '${workout.exercises.length} exercises',
                          ),
                        ],
                      ),
                      const SizedBox(height: 16),
                      Row(
                        children: [
                          Expanded(
                            child: OutlinedButton(
                              onPressed: () {
                                Navigator.pushNamed(
                                  context,
                                  '/workout_detail',
                                  arguments: {
                                    'workout': workout,
                                    'planId': _plan!.id,
                                    'isCompleted': isCompleted,
                                  },
                                ).then((_) => _loadTrainingPlan());
                              },
                              style: OutlinedButton.styleFrom(
                                side: BorderSide(
                                  color:
                                      isCompleted
                                          ? Colors.green
                                          : AppTheme.primaryColor,
                                ),
                                shape: RoundedRectangleBorder(
                                  borderRadius: BorderRadius.circular(8),
                                ),
                              ),
                              child: Text(
                                'View Details',
                                style: TextStyle(
                                  color:
                                      isCompleted
                                          ? Colors.green
                                          : AppTheme.primaryColor,
                                ),
                              ),
                            ),
                          ),
                          const SizedBox(width: 8),
                          Expanded(
                            child: ElevatedButton(
                              onPressed:
                                  isCompleted
                                      ? null
                                      : () async {
                                        await _trainingService
                                            .markWorkoutCompleted(
                                              _plan!.id,
                                              workout.id,
                                            );
                                        _loadTrainingPlan();
                                      },
                              style: ElevatedButton.styleFrom(
                                backgroundColor:
                                    isCompleted ? Colors.grey : Colors.green,
                                shape: RoundedRectangleBorder(
                                  borderRadius: BorderRadius.circular(8),
                                ),
                              ),
                              child: Text(
                                isCompleted ? 'Completed' : 'Mark Complete',
                                style: const TextStyle(color: Colors.white),
                              ),
                            ),
                          ),
                        ],
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  Widget _buildWorkoutInfoChip(IconData icon, String label) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: Colors.grey[200],
        borderRadius: BorderRadius.circular(16),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(icon, size: 14, color: Colors.grey[700]),
          const SizedBox(width: 4),
          Text(label, style: TextStyle(fontSize: 12, color: Colors.grey[700])),
        ],
      ),
    );
  }
}
