import 'package:fit_4_force/core/services/api_service.dart';
import 'package:fit_4_force/core/services/connectivity_service.dart';
import 'package:fit_4_force/features/auth/services/user_api_service.dart';
import 'package:fit_4_force/features/community/services/community_api_service.dart';
import 'package:fit_4_force/features/fitness/services/fitness_api_service.dart';
import 'package:fit_4_force/features/quiz/services/quiz_api_service.dart';
import 'package:fit_4_force/features/subscription/services/subscription_api_service.dart';
import 'package:fit_4_force/shared/services/supabase_auth_service.dart';
import 'package:fit_4_force/shared/services/supabase_storage_service.dart';
import 'package:fit_4_force/shared/services/supabase_subscription_service.dart';
import 'package:get_it/get_it.dart';
import 'package:http/http.dart' as http;

/// Service locator instance
final GetIt serviceLocator = GetIt.instance;

/// Initialize service locator
Future<void> setupServiceLocator() async {
  // Core services
  serviceLocator.registerLazySingleton(() => http.Client());
  serviceLocator.registerLazySingleton(
    () => ApiService(client: serviceLocator()),
  );
  serviceLocator.registerLazySingleton(() => ConnectivityService());

  // API services
  serviceLocator.registerLazySingleton(
    () => UserApiService(apiService: serviceLocator()),
  );
  serviceLocator.registerLazySingleton(
    () => SubscriptionApiService(apiService: serviceLocator()),
  );
  serviceLocator.registerLazySingleton(
    () => QuizApiService(apiService: serviceLocator()),
  );
  serviceLocator.registerLazySingleton(
    () => FitnessApiService(apiService: serviceLocator()),
  );
  serviceLocator.registerLazySingleton(
    () => CommunityApiService(apiService: serviceLocator()),
  );

  // App services
  serviceLocator.registerLazySingleton(() => SupabaseAuthService());
  serviceLocator.registerLazySingleton(
    () => SupabaseSubscriptionService(authService: serviceLocator()),
  );
  serviceLocator.registerLazySingleton(() => SupabaseStorageService());
}
