import 'package:flutter/material.dart';
import 'package:fit_4_force/features/fitness/models/workout_model.dart';

/// Service for managing workouts
class WorkoutService {
  // Singleton instance
  static final WorkoutService _instance = WorkoutService._internal();

  factory WorkoutService() {
    return _instance;
  }

  WorkoutService._internal();

  // Mock data for workouts
  final List<WorkoutModel> _workouts = [
    WorkoutModel(
      id: '1',
      name: 'Full Body Workout',
      description: 'A complete workout targeting all major muscle groups',
      imageUrl: 'assets/images/workouts/full_body.jpg',
      category: 'Strength',
      duration: 45,
      calories: 350,
      exercises: [
        ExerciseModel(
          id: '1',
          name: 'Push-ups',
          description: 'Basic push-up exercise',
          imageUrl: 'assets/images/exercises/pushups.jpg',
          videoUrl: 'https://example.com/videos/pushups.mp4',
          duration: 60,
          sets: 3,
          reps: 15,
          restTime: 30,
        ),
        ExerciseModel(
          id: '2',
          name: 'Squats',
          description: 'Basic squat exercise',
          imageUrl: 'assets/images/exercises/squats.jpg',
          videoUrl: 'https://example.com/videos/squats.mp4',
          duration: 60,
          sets: 3,
          reps: 15,
          restTime: 30,
        ),
        ExerciseModel(
          id: '3',
          name: 'Lunges',
          description: 'Basic lunge exercise',
          imageUrl: 'assets/images/exercises/lunges.jpg',
          videoUrl: 'https://example.com/videos/lunges.mp4',
          duration: 60,
          sets: 3,
          reps: 10,
          restTime: 30,
        ),
      ],
      icon: Icons.fitness_center,
      color: Colors.green,
    ),
    WorkoutModel(
      id: '2',
      name: 'Upper Body Strength',
      description: 'Focus on building upper body strength',
      imageUrl: 'assets/images/workouts/upper_body.jpg',
      category: 'Strength',
      duration: 30,
      calories: 250,
      exercises: [
        ExerciseModel(
          id: '1',
          name: 'Push-ups',
          description: 'Basic push-up exercise',
          imageUrl: 'assets/images/exercises/pushups.jpg',
          videoUrl: 'https://example.com/videos/pushups.mp4',
          duration: 60,
          sets: 3,
          reps: 15,
          restTime: 30,
        ),
        ExerciseModel(
          id: '4',
          name: 'Pull-ups',
          description: 'Basic pull-up exercise',
          imageUrl: 'assets/images/exercises/pullups.jpg',
          videoUrl: 'https://example.com/videos/pullups.mp4',
          duration: 60,
          sets: 3,
          reps: 8,
          restTime: 45,
        ),
      ],
      icon: Icons.fitness_center,
      color: Colors.blue,
    ),
    WorkoutModel(
      id: '3',
      name: '5K Run',
      description: 'Outdoor running workout',
      imageUrl: 'assets/images/workouts/running.jpg',
      category: 'Cardio',
      duration: 25,
      calories: 300,
      exercises: [
        ExerciseModel(
          id: '5',
          name: 'Warm-up Jog',
          description: 'Light jogging to warm up',
          imageUrl: 'assets/images/exercises/jogging.jpg',
          videoUrl: 'https://example.com/videos/jogging.mp4',
          duration: 300,
          sets: 1,
          reps: 1,
          restTime: 0,
        ),
        ExerciseModel(
          id: '6',
          name: '5K Run',
          description: 'Steady pace running',
          imageUrl: 'assets/images/exercises/running.jpg',
          videoUrl: 'https://example.com/videos/running.mp4',
          duration: 1500,
          sets: 1,
          reps: 1,
          restTime: 0,
        ),
      ],
      icon: Icons.directions_run,
      color: Colors.green,
    ),
    WorkoutModel(
      id: '4',
      name: 'Core Workout',
      description: 'Focus on strengthening core muscles',
      imageUrl: 'assets/images/workouts/core.jpg',
      category: 'Strength',
      duration: 20,
      calories: 200,
      exercises: [
        ExerciseModel(
          id: '7',
          name: 'Crunches',
          description: 'Basic crunch exercise',
          imageUrl: 'assets/images/exercises/crunches.jpg',
          videoUrl: 'https://example.com/videos/crunches.mp4',
          duration: 60,
          sets: 3,
          reps: 20,
          restTime: 30,
        ),
        ExerciseModel(
          id: '8',
          name: 'Plank',
          description: 'Basic plank exercise',
          imageUrl: 'assets/images/exercises/plank.jpg',
          videoUrl: 'https://example.com/videos/plank.mp4',
          duration: 60,
          sets: 3,
          reps: 1,
          restTime: 30,
        ),
      ],
      icon: Icons.accessibility_new,
      color: Colors.orange,
    ),
  ];

  // Mock data for workout categories
  final List<WorkoutCategoryModel> _categories = [
    WorkoutCategoryModel(
      id: '1',
      name: 'Push-ups',
      description: 'Upper body strength exercises',
      icon: Icons.accessibility_new,
      color: Colors.blue,
      imageUrl: 'assets/images/categories/pushups.jpg',
      workoutCount: 5,
    ),
    WorkoutCategoryModel(
      id: '2',
      name: 'Running',
      description: 'Cardio exercises',
      icon: Icons.directions_run,
      color: Colors.green,
      imageUrl: 'assets/images/categories/running.jpg',
      workoutCount: 3,
    ),
    WorkoutCategoryModel(
      id: '3',
      name: 'Strength',
      description: 'Full body strength training',
      icon: Icons.fitness_center,
      color: Colors.orange,
      imageUrl: 'assets/images/categories/strength.jpg',
      workoutCount: 8,
    ),
    WorkoutCategoryModel(
      id: '4',
      name: 'Endurance',
      description: 'Endurance building exercises',
      icon: Icons.timer,
      color: Colors.purple,
      imageUrl: 'assets/images/categories/endurance.jpg',
      workoutCount: 4,
    ),
  ];

  // Mock data for workout plans
  final List<WorkoutPlanModel> _plans = [
    WorkoutPlanModel(
      id: '1',
      name: 'Basic Training',
      description: 'Prepare for military physical fitness test',
      imageUrl: 'assets/images/plans/basic_training.jpg',
      duration: '4 weeks',
      level: 'Beginner',
      color: Colors.blue,
      workouts: [],
    ),
    WorkoutPlanModel(
      id: '2',
      name: 'Advanced Strength',
      description: 'Build strength and endurance for military service',
      imageUrl: 'assets/images/plans/advanced_strength.jpg',
      duration: '6 weeks',
      level: 'Intermediate',
      color: Colors.orange,
      workouts: [],
    ),
    WorkoutPlanModel(
      id: '3',
      name: 'Elite Performance',
      description: 'Advanced training for special forces candidates',
      imageUrl: 'assets/images/plans/elite_performance.jpg',
      duration: '8 weeks',
      level: 'Advanced',
      color: Colors.red,
      workouts: [],
      isPremium: true,
    ),
    WorkoutPlanModel(
      id: '4',
      name: 'Tactical Fitness',
      description: 'Functional fitness for military operations',
      imageUrl: 'assets/images/plans/tactical_fitness.jpg',
      duration: '6 weeks',
      level: 'Intermediate',
      color: Colors.purple,
      workouts: [],
    ),
  ];

  // Mock data for workout history
  final List<WorkoutHistoryModel> _history = [
    WorkoutHistoryModel(
      id: '1',
      workoutId: '2',
      workoutName: 'Upper Body Strength',
      date: DateTime.now().subtract(const Duration(days: 1)),
      duration: 30,
      calories: 250,
      icon: Icons.fitness_center,
      color: Colors.blue,
    ),
    WorkoutHistoryModel(
      id: '2',
      workoutId: '3',
      workoutName: '5K Run',
      date: DateTime.now().subtract(const Duration(days: 2)),
      duration: 25,
      calories: 300,
      icon: Icons.directions_run,
      color: Colors.green,
    ),
    WorkoutHistoryModel(
      id: '3',
      workoutId: '4',
      workoutName: 'Core Workout',
      date: DateTime.now().subtract(const Duration(days: 3)),
      duration: 20,
      calories: 200,
      icon: Icons.accessibility_new,
      color: Colors.orange,
    ),
  ];

  // Get all workouts
  List<WorkoutModel> getAllWorkouts() {
    return List.from(_workouts);
  }

  // Get workout by ID
  WorkoutModel? getWorkoutById(String id) {
    try {
      return _workouts.firstWhere((workout) => workout.id == id);
    } catch (e) {
      return null;
    }
  }

  // Get workouts by category
  List<WorkoutModel> getWorkoutsByCategory(String category) {
    return _workouts.where((workout) => workout.category == category).toList();
  }

  // Search workouts
  List<WorkoutModel> searchWorkouts(String query) {
    final lowercaseQuery = query.toLowerCase();
    return _workouts.where((workout) {
      return workout.name.toLowerCase().contains(lowercaseQuery) ||
          workout.description.toLowerCase().contains(lowercaseQuery) ||
          workout.category.toLowerCase().contains(lowercaseQuery);
    }).toList();
  }

  // Get all categories
  List<WorkoutCategoryModel> getAllCategories() {
    return List.from(_categories);
  }

  // Get category by ID
  WorkoutCategoryModel? getCategoryById(String id) {
    try {
      return _categories.firstWhere((category) => category.id == id);
    } catch (e) {
      return null;
    }
  }

  // Get all plans
  List<WorkoutPlanModel> getAllPlans() {
    return List.from(_plans);
  }

  // Get plan by ID
  WorkoutPlanModel? getPlanById(String id) {
    try {
      return _plans.firstWhere((plan) => plan.id == id);
    } catch (e) {
      return null;
    }
  }

  // Get workout history
  List<WorkoutHistoryModel> getWorkoutHistory() {
    return List.from(_history);
  }

  // Add workout to history
  void addWorkoutToHistory(WorkoutHistoryModel workout) {
    _history.add(workout);
  }

  // Create custom workout
  WorkoutModel createCustomWorkout({
    required String name,
    required String description,
    required String category,
    required List<ExerciseModel> exercises,
    required IconData icon,
    required Color color,
  }) {
    final id = DateTime.now().millisecondsSinceEpoch.toString();
    
    // Calculate duration and calories
    int totalDuration = 0;
    for (var exercise in exercises) {
      totalDuration += (exercise.duration * exercise.sets) ~/ 60;
      totalDuration += (exercise.restTime * (exercise.sets - 1)) ~/ 60;
    }
    
    // Estimate calories (simplified calculation)
    final calories = totalDuration * 8;
    
    final workout = WorkoutModel(
      id: id,
      name: name,
      description: description,
      imageUrl: 'assets/images/workouts/custom.jpg',
      category: category,
      duration: totalDuration,
      calories: calories,
      exercises: exercises,
      icon: icon,
      color: color,
    );
    
    _workouts.add(workout);
    return workout;
  }
}
