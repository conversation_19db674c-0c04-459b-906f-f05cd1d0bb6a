import 'package:flutter/material.dart';

class NavigationService {
  // Singleton pattern
  static final NavigationService _instance = NavigationService._internal();

  factory NavigationService() {
    return _instance;
  }

  NavigationService._internal();

  final GlobalKey<NavigatorState> navigatorKey = GlobalKey<NavigatorState>();

  Future<dynamic>? navigateTo(String routeName, {Object? arguments}) {
    if (navigatorKey.currentState == null) return null;
    return navigatorKey.currentState!.pushNamed(
      routeName,
      arguments: arguments,
    );
  }

  Future<dynamic>? navigateToReplacement(
    String routeName, {
    Object? arguments,
  }) {
    if (navigatorKey.currentState == null) return null;
    return navigatorKey.currentState!.pushReplacementNamed(
      routeName,
      arguments: arguments,
    );
  }

  Future<dynamic>? navigateToAndRemoveUntil(
    String routeName, {
    Object? arguments,
  }) {
    if (navigatorKey.currentState == null) return null;
    return navigatorKey.currentState!.pushNamedAndRemoveUntil(
      routeName,
      (Route<dynamic> route) => false,
      arguments: arguments,
    );
  }

  bool goBack() {
    if (navigatorKey.currentState == null) return false;
    navigatorKey.currentState!.pop();
    return true;
  }

  bool goBackWithResult(dynamic result) {
    if (navigatorKey.currentState == null) return false;
    navigatorKey.currentState!.pop(result);
    return true;
  }
}
