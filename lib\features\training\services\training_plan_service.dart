import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:fit_4_force/features/training/models/fitness_assessment.dart';
import 'package:fit_4_force/features/training/models/workout.dart';
import 'package:logger/logger.dart';
import 'package:uuid/uuid.dart';

class TrainingPlanService {
  final FirebaseFirestore _firestore = FirebaseFirestore.instance;
  final Logger _logger = Logger();
  final Uuid _uuid = const Uuid();

  // Collection references
  CollectionReference get _assessmentsCollection =>
      _firestore.collection('fitness_assessments');

  CollectionReference get _exercisesCollection =>
      _firestore.collection('exercises');

  CollectionReference get _workoutsCollection =>
      _firestore.collection('workouts');

  CollectionReference get _trainingPlansCollection =>
      _firestore.collection('training_plans');

  CollectionReference get _agencyRequirementsCollection =>
      _firestore.collection('agency_requirements');

  // Save a fitness assessment
  Future<String> saveAssessment(FitnessAssessment assessment) async {
    try {
      final docRef = await _assessmentsCollection.add(assessment.toJson());
      return docRef.id;
    } catch (e) {
      _logger.e('Error saving fitness assessment: $e');
      rethrow;
    }
  }

  // Get the latest assessment for a user
  Future<FitnessAssessment?> getLatestAssessment(String userId) async {
    try {
      final querySnapshot =
          await _assessmentsCollection
              .where('userId', isEqualTo: userId)
              .orderBy('assessmentDate', descending: true)
              .limit(1)
              .get();

      if (querySnapshot.docs.isEmpty) {
        return null;
      }

      return FitnessAssessment.fromFirestore(querySnapshot.docs.first);
    } catch (e) {
      _logger.e('Error getting latest assessment: $e');
      return null;
    }
  }

  // Get agency-specific fitness requirements
  Future<Map<String, dynamic>> getAgencyRequirements(String agency) async {
    try {
      final docSnapshot = await _agencyRequirementsCollection.doc(agency).get();

      if (!docSnapshot.exists) {
        return _getDefaultRequirements();
      }

      return docSnapshot.data() as Map<String, dynamic>;
    } catch (e) {
      _logger.e('Error getting agency requirements: $e');
      return _getDefaultRequirements();
    }
  }

  // Get default requirements if agency-specific ones aren't available
  Map<String, dynamic> _getDefaultRequirements() {
    return {
      'minPushUps': 30,
      'minSitUps': 40,
      'maxRunTime': 12.0, // minutes for 2.4km
      'minHeight': 168, // cm
      'minWeight': 60, // kg
      'maxWeight': 80, // kg
    };
  }

  // Generate a personalized training plan
  Future<TrainingPlan> generateTrainingPlan(String userId) async {
    try {
      // Get user's latest assessment
      final assessment = await getLatestAssessment(userId);
      if (assessment == null) {
        return await _createDefaultPlan(userId, 'Nigerian Army');
      }

      // Get user's fitness level
      final fitnessLevel = assessment.getFitnessLevel();

      // Get exercises appropriate for the user's fitness level
      final exercises = await _getExercisesByLevel(fitnessLevel);

      // Create a 4-week plan with different focus areas
      final List<WorkoutDay> workouts = [];
      final startDate = DateTime.now();
      final endDate = startDate.add(const Duration(days: 28)); // 4 weeks

      // Create workouts for each day
      for (int week = 1; week <= 4; week++) {
        for (int day = 1; day <= 6; day++) {
          // Rest on day 7
          WorkoutDay workoutDay;

          switch (day) {
            case 1:
              workoutDay = await _createCardioWorkout(
                fitnessLevel,
                week,
                exercises,
              );
              break;
            case 2:
              workoutDay = await _createStrengthWorkout(
                fitnessLevel,
                week,
                'upper',
                exercises,
              );
              break;
            case 3:
              workoutDay = await _createCardioWorkout(
                fitnessLevel,
                week,
                exercises,
                isInterval: true,
              );
              break;
            case 4:
              workoutDay = await _createStrengthWorkout(
                fitnessLevel,
                week,
                'lower',
                exercises,
              );
              break;
            case 5:
              workoutDay = await _createEnduranceWorkout(
                fitnessLevel,
                week,
                exercises,
              );
              break;
            case 6:
              workoutDay = await _createAgencySpecificWorkout(
                fitnessLevel,
                week,
                assessment.militaryAgency,
                exercises,
              );
              break;
            default:
              workoutDay = WorkoutDay(
                id: _uuid.v4(),
                title: 'Rest Day',
                description: 'Take time to recover and let your body heal.',
                exercises: [],
                focusArea: 'Rest',
                difficulty: fitnessLevel,
                estimatedDurationMinutes: 0,
              );
          }

          workouts.add(workoutDay);
        }
      }

      // Create the training plan
      final plan = TrainingPlan(
        id: _uuid.v4(),
        userId: userId,
        title: '${assessment.militaryAgency} $fitnessLevel Training Plan',
        description:
            'A personalized 4-week training plan designed to improve your fitness for ${assessment.militaryAgency} requirements.',
        workouts: workouts,
        createdAt: DateTime.now(),
        startDate: startDate,
        endDate: endDate,
        difficulty: fitnessLevel,
        militaryAgency: assessment.militaryAgency,
        completedWorkouts: {for (var workout in workouts) workout.id: false},
      );

      // Save the plan to Firestore
      await _saveTrainingPlan(plan);

      return plan;
    } catch (e) {
      _logger.e('Error generating training plan: $e');
      return await _createDefaultPlan(userId, 'Nigerian Army');
    }
  }

  // Save a training plan to Firestore
  Future<void> _saveTrainingPlan(TrainingPlan plan) async {
    try {
      // First, save each workout day separately
      final workoutRefs = <String>[];
      for (final workout in plan.workouts) {
        final workoutRef = await _workoutsCollection.add({
          'title': workout.title,
          'description': workout.description,
          'focusArea': workout.focusArea,
          'difficulty': workout.difficulty,
          'estimatedDurationMinutes': workout.estimatedDurationMinutes,
          'imageUrl': workout.imageUrl,
          'videoUrl': workout.videoUrl,
          'requiresEquipment': workout.requiresEquipment,
          'exercises': workout.exercises.map((e) => e.id).toList(),
        });
        workoutRefs.add(workoutRef.id);
      }

      // Then save the plan with references to the workouts
      await _trainingPlansCollection.doc(plan.id).set({
        'userId': plan.userId,
        'title': plan.title,
        'description': plan.description,
        'workouts': workoutRefs,
        'createdAt': Timestamp.fromDate(plan.createdAt),
        'startDate': Timestamp.fromDate(plan.startDate),
        'endDate': Timestamp.fromDate(plan.endDate),
        'difficulty': plan.difficulty,
        'militaryAgency': plan.militaryAgency,
        'isCompleted': plan.isCompleted,
        'completedWorkouts': plan.completedWorkouts,
      });
    } catch (e) {
      _logger.e('Error saving training plan: $e');
      rethrow;
    }
  }

  // Get exercises by fitness level
  Future<List<Exercise>> _getExercisesByLevel(String fitnessLevel) async {
    try {
      final querySnapshot =
          await _exercisesCollection
              .where('difficulty', isEqualTo: fitnessLevel)
              .get();

      return querySnapshot.docs
          .map((doc) => Exercise.fromFirestore(doc))
          .toList();
    } catch (e) {
      _logger.e('Error getting exercises by level: $e');
      return [];
    }
  }

  // Create a cardio workout
  Future<WorkoutDay> _createCardioWorkout(
    String fitnessLevel,
    int week,
    List<Exercise> exercises, {
    bool isInterval = false,
  }) async {
    final title =
        isInterval ? 'Interval Cardio Training' : 'Steady-State Cardio';

    final description =
        isInterval
            ? 'High-intensity interval training to improve cardiovascular fitness and burn calories.'
            : 'Steady-state cardio to build endurance and improve recovery.';

    // Filter exercises for cardio
    final cardioExercises =
        exercises.where((e) => e.targetMuscles.contains('Cardio')).toList();

    // Adjust workout based on week (progressive overload)
    final intensity = 1.0 + (week * 0.1); // Increase intensity by 10% each week

    // Select exercises based on fitness level and week
    final selectedExercises = <Exercise>[];

    if (isInterval) {
      // For interval training
      final baseExercises = cardioExercises.take(4).toList();
      for (final exercise in baseExercises) {
        selectedExercises.add(
          Exercise(
            id: exercise.id,
            name: exercise.name,
            description: exercise.description,
            sets: exercise.sets + (week - 1),
            reps: (exercise.reps * intensity).round(),
            isTimeBased: exercise.isTimeBased,
            imageUrl: exercise.imageUrl,
            videoUrl: exercise.videoUrl,
            difficulty: exercise.difficulty,
            targetMuscles: exercise.targetMuscles,
            instructions: exercise.instructions,
          ),
        );
      }
    } else {
      // For steady-state cardio
      final baseExercises =
          cardioExercises.where((e) => e.isTimeBased).take(2).toList();

      for (final exercise in baseExercises) {
        selectedExercises.add(
          Exercise(
            id: exercise.id,
            name: exercise.name,
            description: exercise.description,
            sets: 1,
            reps: (exercise.reps * intensity).round(), // Increase duration
            isTimeBased: true,
            imageUrl: exercise.imageUrl,
            videoUrl: exercise.videoUrl,
            difficulty: exercise.difficulty,
            targetMuscles: exercise.targetMuscles,
            instructions: exercise.instructions,
          ),
        );
      }
    }

    return WorkoutDay(
      id: _uuid.v4(),
      title: 'Week $week: $title',
      description: description,
      exercises: selectedExercises,
      focusArea: 'Cardio',
      difficulty: fitnessLevel,
      estimatedDurationMinutes: isInterval ? 30 : 45,
      requiresEquipment: false,
    );
  }

  // Create a strength workout
  Future<WorkoutDay> _createStrengthWorkout(
    String fitnessLevel,
    int week,
    String bodyPart, // 'upper' or 'lower'
    List<Exercise> exercises,
  ) async {
    final title =
        bodyPart == 'upper' ? 'Upper Body Strength' : 'Lower Body Strength';

    final description =
        bodyPart == 'upper'
            ? 'Focus on building strength in your arms, chest, back, and shoulders.'
            : 'Focus on building strength in your legs, glutes, and core.';

    // Filter exercises for the body part
    final targetMuscles =
        bodyPart == 'upper'
            ? ['Chest', 'Back', 'Shoulders', 'Arms']
            : ['Legs', 'Glutes', 'Core'];

    final filteredExercises =
        exercises
            .where(
              (e) => e.targetMuscles.any(
                (muscle) => targetMuscles.contains(muscle),
              ),
            )
            .toList();

    // Adjust workout based on week (progressive overload)
    final intensity = 1.0 + (week * 0.1); // Increase intensity by 10% each week

    // Select exercises based on fitness level and week
    final selectedExercises = <Exercise>[];
    final exercisesToSelect = bodyPart == 'upper' ? 5 : 4;

    final baseExercises = filteredExercises.take(exercisesToSelect).toList();
    for (final exercise in baseExercises) {
      selectedExercises.add(
        Exercise(
          id: exercise.id,
          name: exercise.name,
          description: exercise.description,
          sets: exercise.sets + (week ~/ 2), // Increase sets every 2 weeks
          reps: (exercise.reps * intensity).round(),
          isTimeBased: exercise.isTimeBased,
          imageUrl: exercise.imageUrl,
          videoUrl: exercise.videoUrl,
          difficulty: exercise.difficulty,
          targetMuscles: exercise.targetMuscles,
          instructions: exercise.instructions,
        ),
      );
    }

    return WorkoutDay(
      id: _uuid.v4(),
      title: 'Week $week: $title',
      description: description,
      exercises: selectedExercises,
      focusArea: bodyPart == 'upper' ? 'Upper Body' : 'Lower Body',
      difficulty: fitnessLevel,
      estimatedDurationMinutes: 45,
      requiresEquipment: true,
    );
  }

  // Create an endurance workout
  Future<WorkoutDay> _createEnduranceWorkout(
    String fitnessLevel,
    int week,
    List<Exercise> exercises,
  ) async {
    final title = 'Endurance Training';
    final description =
        'Build stamina and endurance with this full-body workout.';

    // Filter exercises for endurance
    final enduranceExercises =
        exercises
            .where(
              (e) => e.targetMuscles.contains('Endurance') || e.isTimeBased,
            )
            .toList();

    // Adjust workout based on week (progressive overload)
    final intensity = 1.0 + (week * 0.1); // Increase intensity by 10% each week

    // Select exercises based on fitness level and week
    final selectedExercises = <Exercise>[];

    final baseExercises = enduranceExercises.take(6).toList();
    for (final exercise in baseExercises) {
      selectedExercises.add(
        Exercise(
          id: exercise.id,
          name: exercise.name,
          description: exercise.description,
          sets: exercise.sets,
          reps: (exercise.reps * intensity).round(),
          isTimeBased: exercise.isTimeBased,
          imageUrl: exercise.imageUrl,
          videoUrl: exercise.videoUrl,
          difficulty: exercise.difficulty,
          targetMuscles: exercise.targetMuscles,
          instructions: exercise.instructions,
        ),
      );
    }

    return WorkoutDay(
      id: _uuid.v4(),
      title: 'Week $week: $title',
      description: description,
      exercises: selectedExercises,
      focusArea: 'Endurance',
      difficulty: fitnessLevel,
      estimatedDurationMinutes: 50,
      requiresEquipment: false,
    );
  }

  // Create an agency-specific workout
  Future<WorkoutDay> _createAgencySpecificWorkout(
    String fitnessLevel,
    int week,
    String agency,
    List<Exercise> exercises,
  ) async {
    // Get agency requirements - will be used in future implementation
    // Commenting out to avoid unused variable warning
    // final requirements = await getAgencyRequirements(agency);

    final title = '$agency Specific Training';
    final description =
        'Focused training to meet the specific requirements of the $agency.';

    // Determine focus areas based on agency
    List<String> focusAreas;
    switch (agency) {
      case 'Nigerian Army':
        focusAreas = ['Strength', 'Endurance', 'Core'];
        break;
      case 'Nigerian Navy':
        focusAreas = ['Swimming', 'Endurance', 'Upper Body'];
        break;
      case 'Nigerian Air Force':
        focusAreas = ['Cardio', 'Agility', 'Core'];
        break;
      case 'Nigerian Police':
        focusAreas = ['Strength', 'Agility', 'Endurance'];
        break;
      default:
        focusAreas = ['Strength', 'Endurance', 'Core'];
    }

    // Filter exercises for the focus areas
    final filteredExercises =
        exercises
            .where(
              (e) =>
                  e.targetMuscles.any((muscle) => focusAreas.contains(muscle)),
            )
            .toList();

    // Adjust workout based on week (progressive overload)
    final intensity = 1.0 + (week * 0.1); // Increase intensity by 10% each week

    // Select exercises based on fitness level and week
    final selectedExercises = <Exercise>[];

    final baseExercises = filteredExercises.take(7).toList();
    for (final exercise in baseExercises) {
      selectedExercises.add(
        Exercise(
          id: exercise.id,
          name: exercise.name,
          description: exercise.description,
          sets: exercise.sets + (week - 1),
          reps: (exercise.reps * intensity).round(),
          isTimeBased: exercise.isTimeBased,
          imageUrl: exercise.imageUrl,
          videoUrl: exercise.videoUrl,
          difficulty: exercise.difficulty,
          targetMuscles: exercise.targetMuscles,
          instructions: exercise.instructions,
        ),
      );
    }

    return WorkoutDay(
      id: _uuid.v4(),
      title: 'Week $week: $title',
      description: description,
      exercises: selectedExercises,
      focusArea: agency,
      difficulty: fitnessLevel,
      estimatedDurationMinutes: 60,
      requiresEquipment: true,
    );
  }

  // Create a default training plan if assessment is not available
  Future<TrainingPlan> _createDefaultPlan(String userId, String agency) async {
    try {
      // Get beginner level exercises
      final exercises = await _getExercisesByLevel('Beginner');

      // Create a basic 4-week plan
      final List<WorkoutDay> workouts = [];
      final startDate = DateTime.now();
      final endDate = startDate.add(const Duration(days: 28)); // 4 weeks

      // Create workouts for each day
      for (int week = 1; week <= 4; week++) {
        for (int day = 1; day <= 6; day++) {
          // Rest on day 7
          WorkoutDay workoutDay;

          switch (day) {
            case 1:
              workoutDay = await _createCardioWorkout(
                'Beginner',
                week,
                exercises,
              );
              break;
            case 2:
              workoutDay = await _createStrengthWorkout(
                'Beginner',
                week,
                'upper',
                exercises,
              );
              break;
            case 3:
              workoutDay = await _createCardioWorkout(
                'Beginner',
                week,
                exercises,
                isInterval: true,
              );
              break;
            case 4:
              workoutDay = await _createStrengthWorkout(
                'Beginner',
                week,
                'lower',
                exercises,
              );
              break;
            case 5:
              workoutDay = await _createEnduranceWorkout(
                'Beginner',
                week,
                exercises,
              );
              break;
            case 6:
              workoutDay = await _createAgencySpecificWorkout(
                'Beginner',
                week,
                agency,
                exercises,
              );
              break;
            default:
              workoutDay = WorkoutDay(
                id: _uuid.v4(),
                title: 'Rest Day',
                description: 'Take time to recover and let your body heal.',
                exercises: [],
                focusArea: 'Rest',
                difficulty: 'Beginner',
                estimatedDurationMinutes: 0,
              );
          }

          workouts.add(workoutDay);
        }
      }

      // Create the training plan
      final plan = TrainingPlan(
        id: _uuid.v4(),
        userId: userId,
        title: '$agency Beginner Training Plan',
        description:
            'A standard 4-week training plan designed for beginners preparing for $agency requirements.',
        workouts: workouts,
        createdAt: DateTime.now(),
        startDate: startDate,
        endDate: endDate,
        difficulty: 'Beginner',
        militaryAgency: agency,
        completedWorkouts: {for (var workout in workouts) workout.id: false},
      );

      // Save the plan to Firestore
      await _saveTrainingPlan(plan);

      return plan;
    } catch (e) {
      _logger.e('Error creating default plan: $e');

      // Create a very basic plan if everything else fails
      return TrainingPlan(
        id: _uuid.v4(),
        userId: userId,
        title: 'Basic Training Plan',
        description: 'A basic training plan to get you started.',
        workouts: [],
        createdAt: DateTime.now(),
        startDate: DateTime.now(),
        endDate: DateTime.now().add(const Duration(days: 28)),
        difficulty: 'Beginner',
        militaryAgency: agency,
        completedWorkouts: {},
      );
    }
  }

  // Mark a workout as completed
  Future<bool> markWorkoutCompleted(String planId, String workoutId) async {
    try {
      // Get the current plan
      final docSnapshot = await _trainingPlansCollection.doc(planId).get();
      if (!docSnapshot.exists) {
        return false;
      }

      final data = docSnapshot.data() as Map<String, dynamic>;
      final completedWorkouts = Map<String, bool>.from(
        data['completedWorkouts'],
      );

      // Update the completed status
      completedWorkouts[workoutId] = true;

      // Check if all workouts are completed
      final allCompleted = completedWorkouts.values.every((v) => v);

      // Update the plan
      await _trainingPlansCollection.doc(planId).update({
        'completedWorkouts': completedWorkouts,
        'isCompleted': allCompleted,
      });

      return true;
    } catch (e) {
      _logger.e('Error marking workout as completed: $e');
      return false;
    }
  }

  // Get a user's active training plan
  Future<TrainingPlan?> getActiveTrainingPlan(String userId) async {
    try {
      final querySnapshot =
          await _trainingPlansCollection
              .where('userId', isEqualTo: userId)
              .where('isCompleted', isEqualTo: false)
              .orderBy('createdAt', descending: true)
              .limit(1)
              .get();

      if (querySnapshot.docs.isEmpty) {
        return null;
      }

      final planDoc = querySnapshot.docs.first;
      final planData = planDoc.data() as Map<String, dynamic>;

      // Get the workouts
      final workoutRefs = List<String>.from(planData['workouts']);
      final workouts = <WorkoutDay>[];

      for (final workoutId in workoutRefs) {
        final workoutDoc = await _workoutsCollection.doc(workoutId).get();
        if (workoutDoc.exists) {
          final workoutData = workoutDoc.data() as Map<String, dynamic>;

          // Get the exercises for this workout
          final exerciseRefs = List<String>.from(workoutData['exercises']);
          final exercises = <Exercise>[];

          for (final exerciseId in exerciseRefs) {
            final exerciseDoc =
                await _exercisesCollection.doc(exerciseId).get();
            if (exerciseDoc.exists) {
              exercises.add(Exercise.fromFirestore(exerciseDoc));
            }
          }

          // Create the workout
          workouts.add(
            WorkoutDay(
              id: workoutDoc.id,
              title: workoutData['title'],
              description: workoutData['description'],
              exercises: exercises,
              focusArea: workoutData['focusArea'],
              difficulty: workoutData['difficulty'],
              estimatedDurationMinutes: workoutData['estimatedDurationMinutes'],
              imageUrl: workoutData['imageUrl'],
              videoUrl: workoutData['videoUrl'],
              requiresEquipment: workoutData['requiresEquipment'] ?? false,
            ),
          );
        }
      }

      // Create the training plan
      return TrainingPlan.fromFirestore(planDoc, workouts);
    } catch (e) {
      _logger.e('Error getting active training plan: $e');
      return null;
    }
  }
}
