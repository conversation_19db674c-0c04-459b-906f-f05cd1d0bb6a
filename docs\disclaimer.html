<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Disclaimer - Fit4Force</title>
    <link rel="stylesheet" href="styles.css">
    <link rel="icon" type="image/x-icon" href="favicon.ico">
</head>
<body>
    <header>
        <div class="container">
            <div class="logo">
                <h1>🏋️ Fit4Force</h1>
                <p>Disclaimer</p>
            </div>
        </div>
    </header>

    <main>
        <div class="container">
            <a href="index.html" class="back-link">Back to Legal Documents</a>
            
            <div class="document-header">
                <h1>Disclaimer</h1>
                <div class="document-meta">
                    <p>Effective Date: June 3, 2025</p>
                    <p>Last Updated: June 3, 2025</p>
                </div>
            </div>

            <div class="document-content">
                <p>Fit4Force is a fitness and educational mobile application intended to assist military and paramilitary aspirants in preparing mentally, physically, and academically for recruitment processes.</p>

                <p><strong>By using Fit4Force, you acknowledge and agree to the following:</strong></p>

                <h2>1. No Professional Advice</h2>
                <ul>
                    <li>Fit4Force does not provide medical, legal, or psychological advice.</li>
                    <li>All fitness and health-related content is for informational purposes only and should not substitute consultation with a qualified professional.</li>
                </ul>

                <h2>2. Physical Activity Risks</h2>
                <ul>
                    <li>The fitness workouts, training challenges, and routines provided in this app carry inherent risks.</li>
                    <li>Users should consult a physician before beginning any exercise or training program, especially if they have pre-existing medical conditions.</li>
                    <li>You voluntarily assume all risks associated with physical activities performed using the app.</li>
                </ul>

                <h2>3. AI Content and Limitations</h2>
                <ul>
                    <li>AI-powered suggestions (e.g., quiz recommendations, mock test analysis, career insights) are generated using algorithms and do not replace human judgment.</li>
                    <li>Accuracy or relevance is not guaranteed.</li>
                </ul>

                <h2>4. Educational Content Accuracy</h2>
                <p>While we strive to provide accurate and up-to-date study materials, mock tests, and prep content, Fit4Force does not guarantee success in any official recruitment exam.</p>

                <h2>5. Third-Party Content & Links</h2>
                <p>The app may include third-party content or external links. Fit4Force is not responsible for their accuracy, availability, or reliability.</p>

                <h2>6. Limitation of Liability</h2>
                <p>Nehemiah Technologies and its team shall not be liable for any injuries, losses, or damages incurred from use of this app, including but not limited to physical injuries, emotional distress, or exam failures.</p>

                <h2>7. Contact Information</h2>
                <p>If you have any questions about this disclaimer, please contact us at:</p>
                <p>Email: <EMAIL></p>

                <div style="margin-top: 3rem; padding-top: 2rem; border-top: 1px solid var(--border-color); text-align: center; color: var(--text-secondary);">
                    <p><strong>By using the Fit4Force app, you acknowledge that you have read, understood, and agree to this disclaimer.</strong></p>
                </div>
            </div>
        </div>
    </main>

    <footer>
        <div class="container">
            <p>&copy; 2025 Nehemiah Technologies. All rights reserved.</p>
            <p>Last updated: <span id="lastUpdated"></span></p>
        </div>
    </footer>

    <script>
        document.getElementById('lastUpdated').textContent = new Date().toLocaleDateString();
    </script>
</body>
</html>
