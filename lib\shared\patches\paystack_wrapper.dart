import 'package:flutter/material.dart';
import 'package:fit_4_force/shared/extensions/context_extensions.dart';
import 'package:fit_4_force/shared/patches/paystack_pin_widget_patch.dart';

/// A wrapper for the Flutter Paystack package
/// 
/// This wrapper provides custom implementations of the Flutter Paystack widgets
/// to fix compatibility issues with the latest Flutter version
class PaystackWrapper {
  /// Initialize the Paystack plugin
  static Future<void> initialize({required String publicKey}) async {
    // In a real implementation, this would initialize the Paystack plugin
    // For now, we'll just print a message
    debugPrint('Initializing Paystack with public key: $publicKey');
  }
  
  /// Charge a card
  static Future<Map<String, dynamic>> chargeCard({
    required BuildContext context,
    required int amount,
    required String email,
    String? reference,
  }) async {
    // Show a dialog to simulate the payment process
    await showDialog(
      context: context,
      barrierDismissible: false,
      builder: (BuildContext context) => AlertDialog(
        title: Text('Payment', style: context.textTheme().titleLarge),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Text(
              'Processing payment of ₦${amount / 100}',
              style: context.textTheme().bodyMedium,
            ),
            const SizedBox(height: 20),
            const CircularProgressIndicator(),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancel'),
          ),
        ],
      ),
    );
    
    // Return a success response
    return {
      'status': 'success',
      'reference': reference ?? 'mock_reference_${DateTime.now().millisecondsSinceEpoch}',
      'message': 'Payment successful',
    };
  }
  
  /// Show the PIN dialog
  static Future<void> showPinDialog(BuildContext context) async {
    await showDialog(
      context: context,
      barrierDismissible: false,
      builder: (BuildContext context) => Dialog(
        child: Padding(
          padding: const EdgeInsets.all(20.0),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Text(
                'Enter PIN',
                style: context.textTheme().titleLarge,
              ),
              const SizedBox(height: 20.0),
              // Use our custom PinWidget with the required count parameter
              const PinWidget(count: 0),
              const SizedBox(height: 20.0),
              ElevatedButton(
                onPressed: () {
                  Navigator.of(context).pop();
                },
                child: const Text('Cancel'),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
