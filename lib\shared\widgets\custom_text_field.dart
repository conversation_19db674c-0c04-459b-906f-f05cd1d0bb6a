import 'package:flutter/material.dart';
import 'package:fit_4_force/core/theme/app_theme.dart';

/// A custom text field widget with consistent styling
class CustomTextField extends StatelessWidget {
  /// The label text for the field
  final String label;
  
  /// Optional hint text
  final String? hint;
  
  /// Optional controller for the text field
  final TextEditingController? controller;
  
  /// Optional validator function
  final String? Function(String?)? validator;
  
  /// Whether to obscure the text (for passwords)
  final bool obscureText;
  
  /// Keyboard type for the field
  final TextInputType keyboardType;
  
  /// Optional prefix widget
  final Widget? prefix;
  
  /// Optional suffix widget
  final Widget? suffix;
  
  /// Maximum number of lines
  final int? maxLines;
  
  /// Minimum number of lines
  final int? minLines;
  
  /// Callback when text changes
  final void Function(String)? onChanged;
  
  /// Callback when field is submitted
  final void Function(String)? onSubmitted;

  /// Constructor
  const CustomTextField({
    super.key,
    required this.label,
    this.hint,
    this.controller,
    this.validator,
    this.obscureText = false,
    this.keyboardType = TextInputType.text,
    this.prefix,
    this.suffix,
    this.maxLines = 1,
    this.minLines,
    this.onChanged,
    this.onSubmitted,
  });

  @override
  Widget build(BuildContext context) {
    return TextFormField(
      controller: controller,
      validator: validator,
      obscureText: obscureText,
      keyboardType: keyboardType,
      maxLines: obscureText ? 1 : maxLines,
      minLines: minLines,
      onChanged: onChanged,
      onFieldSubmitted: onSubmitted,
      decoration: InputDecoration(
        labelText: label,
        hintText: hint,
        prefixIcon: prefix,
        suffixIcon: suffix,
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12),
          borderSide: const BorderSide(color: Colors.grey),
        ),
        focusedBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12),
          borderSide: BorderSide(color: AppTheme.primaryColor, width: 2),
        ),
        enabledBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12),
          borderSide: const BorderSide(color: Colors.grey),
        ),
        errorBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12),
          borderSide: const BorderSide(color: Colors.red),
        ),
        focusedErrorBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12),
          borderSide: const BorderSide(color: Colors.red, width: 2),
        ),
        filled: true,
        fillColor: Colors.white,
        contentPadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 16),
      ),
      style: const TextStyle(fontSize: 16),
    );
  }
}
