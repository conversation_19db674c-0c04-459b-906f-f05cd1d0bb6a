import 'package:flutter/foundation.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import 'package:fit_4_force/core/config/supabase_config.dart';
import 'package:logger/logger.dart';

enum UserRole { user, admin, superAdmin }

enum AdminPermission {
  uploadContent,
  manageUsers,
  moderateContent,
  viewAnalytics,
  systemSettings,
}

class AdminAuthService {
  static final AdminAuthService _instance = AdminAuthService._internal();
  factory AdminAuthService() => _instance;
  AdminAuthService._internal();

  final Logger _logger = Logger();
  SupabaseClient get _client => SupabaseConfig.client;

  /// Check if current user is an admin
  Future<bool> isCurrentUserAdmin() async {
    try {
      final user = _client.auth.currentUser;
      if (user == null) return false;

      return await isUserAdmin(user.id);
    } catch (e) {
      _logger.e('❌ Error checking admin status: $e');
      return false;
    }
  }

  /// Check if a specific user is an admin
  Future<bool> isUserAdmin(String userId) async {
    try {
      final response =
          await _client
              .from('user_roles')
              .select('role')
              .eq('user_id', userId)
              .maybeSingle();

      if (response == null) return false;

      final role = response['role'] as String?;
      return role == 'admin' || role == 'super_admin';
    } catch (e) {
      _logger.e('❌ Error checking user admin status: $e');
      return false;
    }
  }

  /// Get user role
  Future<UserRole> getUserRole(String userId) async {
    try {
      final response =
          await _client
              .from('user_roles')
              .select('role')
              .eq('user_id', userId)
              .maybeSingle();

      if (response == null) return UserRole.user;

      final roleString = response['role'] as String?;
      switch (roleString) {
        case 'admin':
          return UserRole.admin;
        case 'super_admin':
          return UserRole.superAdmin;
        default:
          return UserRole.user;
      }
    } catch (e) {
      _logger.e('❌ Error getting user role: $e');
      return UserRole.user;
    }
  }

  /// Check if user has specific admin permission
  Future<bool> hasPermission(String userId, AdminPermission permission) async {
    try {
      final userRole = await getUserRole(userId);

      // Super admins have all permissions
      if (userRole == UserRole.superAdmin) return true;

      // Regular users have no admin permissions
      if (userRole == UserRole.user) return false;

      // Check specific permissions for regular admins
      final response =
          await _client
              .from('admin_permissions')
              .select('permissions')
              .eq('user_id', userId)
              .maybeSingle();

      if (response == null) {
        // Default admin permissions
        return _getDefaultAdminPermissions().contains(permission);
      }

      final permissions = List<String>.from(response['permissions'] ?? []);
      return permissions.contains(permission.name);
    } catch (e) {
      _logger.e('❌ Error checking permission: $e');
      return false;
    }
  }

  /// Get default permissions for regular admins
  List<AdminPermission> _getDefaultAdminPermissions() {
    return [AdminPermission.uploadContent, AdminPermission.moderateContent];
  }

  /// Promote user to admin (super admin only)
  Future<bool> promoteToAdmin(
    String userId,
    List<AdminPermission> permissions,
  ) async {
    try {
      final currentUser = _client.auth.currentUser;
      if (currentUser == null) return false;

      // Check if current user is super admin
      final currentUserRole = await getUserRole(currentUser.id);
      if (currentUserRole != UserRole.superAdmin) {
        _logger.w('⚠️ Only super admins can promote users');
        return false;
      }

      // Add user role
      await _client.from('user_roles').upsert({
        'user_id': userId,
        'role': 'admin',
        'assigned_by': currentUser.id,
        'assigned_at': DateTime.now().toIso8601String(),
      });

      // Add permissions
      await _client.from('admin_permissions').upsert({
        'user_id': userId,
        'permissions': permissions.map((p) => p.name).toList(),
        'assigned_by': currentUser.id,
        'assigned_at': DateTime.now().toIso8601String(),
      });

      _logger.i('✅ User $userId promoted to admin');
      return true;
    } catch (e) {
      _logger.e('❌ Error promoting user to admin: $e');
      return false;
    }
  }

  /// Remove admin privileges
  Future<bool> removeAdminPrivileges(String userId) async {
    try {
      final currentUser = _client.auth.currentUser;
      if (currentUser == null) return false;

      // Check if current user is super admin
      final currentUserRole = await getUserRole(currentUser.id);
      if (currentUserRole != UserRole.superAdmin) {
        _logger.w('⚠️ Only super admins can remove admin privileges');
        return false;
      }

      // Remove role and permissions
      await _client.from('user_roles').delete().eq('user_id', userId);
      await _client.from('admin_permissions').delete().eq('user_id', userId);

      _logger.i('✅ Admin privileges removed for user $userId');
      return true;
    } catch (e) {
      _logger.e('❌ Error removing admin privileges: $e');
      return false;
    }
  }

  /// Get all admins
  Future<List<Map<String, dynamic>>> getAllAdmins() async {
    try {
      final response = await _client
          .from('user_roles')
          .select('''
            user_id,
            role,
            assigned_at,
            users!inner(
              id,
              full_name,
              email,
              target_agency
            )
          ''')
          .inFilter('role', ['admin', 'super_admin']);

      return List<Map<String, dynamic>>.from(response);
    } catch (e) {
      _logger.e('❌ Error getting all admins: $e');
      return [];
    }
  }

  /// Verify admin access with additional security checks
  Future<bool> verifyAdminAccess({
    required String userId,
    required AdminPermission requiredPermission,
    String? ipAddress,
    String? userAgent,
  }) async {
    try {
      // Basic permission check
      final hasPermission = await this.hasPermission(
        userId,
        requiredPermission,
      );
      if (!hasPermission) return false;

      // Log admin access attempt
      await _logAdminAccess(
        userId: userId,
        permission: requiredPermission,
        ipAddress: ipAddress,
        userAgent: userAgent,
        success: true,
      );

      return true;
    } catch (e) {
      // Log failed access attempt
      await _logAdminAccess(
        userId: userId,
        permission: requiredPermission,
        ipAddress: ipAddress,
        userAgent: userAgent,
        success: false,
        error: e.toString(),
      );

      _logger.e('❌ Error verifying admin access: $e');
      return false;
    }
  }

  /// Log admin access attempts for security auditing
  Future<void> _logAdminAccess({
    required String userId,
    required AdminPermission permission,
    String? ipAddress,
    String? userAgent,
    required bool success,
    String? error,
  }) async {
    try {
      await _client.from('admin_access_logs').insert({
        'user_id': userId,
        'permission': permission.name,
        'ip_address': ipAddress,
        'user_agent': userAgent,
        'success': success,
        'error': error,
        'timestamp': DateTime.now().toIso8601String(),
      });
    } catch (e) {
      _logger.e('❌ Error logging admin access: $e');
    }
  }

  /// Get admin access logs (super admin only)
  Future<List<Map<String, dynamic>>> getAdminAccessLogs({
    String? userId,
    DateTime? startDate,
    DateTime? endDate,
    int limit = 100,
  }) async {
    try {
      final currentUser = _client.auth.currentUser;
      if (currentUser == null) return [];

      final currentUserRole = await getUserRole(currentUser.id);
      if (currentUserRole != UserRole.superAdmin) {
        _logger.w('⚠️ Only super admins can view access logs');
        return [];
      }

      var queryBuilder = _client.from('admin_access_logs').select('''
            *,
            users!inner(
              full_name,
              email
            )
          ''');

      if (userId != null) {
        queryBuilder = queryBuilder.eq('user_id', userId);
      }

      if (startDate != null) {
        queryBuilder = queryBuilder.gte(
          'timestamp',
          startDate.toIso8601String(),
        );
      }

      if (endDate != null) {
        queryBuilder = queryBuilder.lte('timestamp', endDate.toIso8601String());
      }

      final response = await queryBuilder
          .order('timestamp', ascending: false)
          .limit(limit);
      return List<Map<String, dynamic>>.from(response);
    } catch (e) {
      _logger.e('❌ Error getting admin access logs: $e');
      return [];
    }
  }

  /// Initialize default super admin (development only)
  Future<void> initializeDefaultSuperAdmin(String email) async {
    if (!kDebugMode) {
      _logger.w('⚠️ Super admin initialization only available in debug mode');
      return;
    }

    try {
      // Find user by email
      final response =
          await _client
              .from('users')
              .select('id')
              .eq('email', email)
              .maybeSingle();

      if (response == null) {
        _logger.w('⚠️ User with email $email not found');
        return;
      }

      final userId = response['id'] as String;

      // Set as super admin
      await _client.from('user_roles').upsert({
        'user_id': userId,
        'role': 'super_admin',
        'assigned_by': userId, // Self-assigned for initial setup
        'assigned_at': DateTime.now().toIso8601String(),
      });

      _logger.i('✅ Super admin initialized for $email');
    } catch (e) {
      _logger.e('❌ Error initializing super admin: $e');
    }
  }
}
