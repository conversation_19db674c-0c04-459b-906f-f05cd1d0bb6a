import 'dart:async';
import 'package:flutter/material.dart';
import 'package:fit_4_force/core/theme/app_theme.dart';
import 'package:fit_4_force/shared/models/user_model.dart';
import 'package:fit_4_force/features/prep/widgets/timer_painter.dart';

class PomodoroTimerScreen extends StatefulWidget {
  final UserModel user;

  const PomodoroTimerScreen({super.key, required this.user});

  @override
  State<PomodoroTimerScreen> createState() => _PomodoroTimerScreenState();
}

class _PomodoroTimerScreenState extends State<PomodoroTimerScreen>
    with TickerProviderStateMixin {
  // Timer settings
  static const int _defaultFocusTime = 25 * 60; // 25 minutes in seconds
  static const int _defaultBreakTime = 5 * 60; // 5 minutes in seconds
  static const int _defaultLongBreakTime = 15 * 60; // 15 minutes in seconds
  static const int _sessionsBeforeLongBreak = 4;

  // Timer state
  late int _currentTime;
  late int _totalTime;
  late Timer _timer;
  bool _isRunning = false;
  bool _isBreak = false;
  int _completedSessions = 0;
  String _currentSubject = "General Study";

  // Animation controller for the progress indicator
  late AnimationController _animationController;

  // List of subjects for the dropdown
  final List<String> _subjects = [
    "General Study",
    "Physical Training",
    "Mathematics",
    "English",
    "Current Affairs",
    "Agency Specific",
  ];

  // Session history
  final List<Map<String, dynamic>> _sessionHistory = [];

  @override
  void initState() {
    super.initState();
    print("PomodoroTimerScreen initialized");
    _currentTime = _defaultFocusTime;
    _totalTime = _defaultFocusTime;

    _animationController = AnimationController(
      vsync: this,
      duration: Duration(seconds: _totalTime),
    );
  }

  @override
  void dispose() {
    if (_isRunning) {
      _timer.cancel();
    }
    _animationController.dispose();
    super.dispose();
  }

  void _startTimer() {
    setState(() {
      _isRunning = true;
    });

    _timer = Timer.periodic(const Duration(seconds: 1), (timer) {
      setState(() {
        if (_currentTime > 0) {
          _currentTime--;
          _animationController.value = 1 - (_currentTime / _totalTime);
        } else {
          _timer.cancel();
          _isRunning = false;
          _handleTimerComplete();
        }
      });
    });
  }

  void _pauseTimer() {
    _timer.cancel();
    setState(() {
      _isRunning = false;
    });
  }

  void _resetTimer() {
    _timer.cancel();
    setState(() {
      _isRunning = false;
      _currentTime =
          _isBreak
              ? (_completedSessions % _sessionsBeforeLongBreak == 0
                  ? _defaultLongBreakTime
                  : _defaultBreakTime)
              : _defaultFocusTime;
      _totalTime = _currentTime;
      _animationController.duration = Duration(seconds: _totalTime);
      _animationController.reset();
    });
  }

  void _handleTimerComplete() {
    // Play sound or vibration here

    if (_isBreak) {
      // Break is over, start a new focus session
      setState(() {
        _isBreak = false;
        _currentTime = _defaultFocusTime;
        _totalTime = _defaultFocusTime;
        _animationController.duration = Duration(seconds: _totalTime);
        _animationController.reset();
      });

      _showNotification("Focus Time", "Break is over! Time to focus again.");
    } else {
      // Focus session is over, start a break
      _completedSessions++;

      // Record the completed session
      _sessionHistory.add({
        'subject': _currentSubject,
        'duration': _defaultFocusTime,
        'timestamp': DateTime.now().toString(),
      });

      setState(() {
        _isBreak = true;

        // Determine if it's time for a long break
        if (_completedSessions % _sessionsBeforeLongBreak == 0) {
          _currentTime = _defaultLongBreakTime;
          _totalTime = _defaultLongBreakTime;
          _showNotification("Long Break", "Great job! Take a longer break.");
        } else {
          _currentTime = _defaultBreakTime;
          _totalTime = _defaultBreakTime;
          _showNotification("Break Time", "Good work! Take a short break.");
        }

        _animationController.duration = Duration(seconds: _totalTime);
        _animationController.reset();
      });
    }
  }

  void _showNotification(String title, String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        duration: const Duration(seconds: 3),
        behavior: SnackBarBehavior.floating,
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(10)),
      ),
    );
  }

  String _formatTime(int seconds) {
    int minutes = seconds ~/ 60;
    int remainingSeconds = seconds % 60;
    return '${minutes.toString().padLeft(2, '0')}:${remainingSeconds.toString().padLeft(2, '0')}';
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text(
          'Study Timer',
          style: TextStyle(fontWeight: FontWeight.bold, fontSize: 20),
        ),
        actions: [
          IconButton(
            icon: const Icon(Icons.history),
            onPressed: _showSessionHistory,
          ),
          IconButton(
            icon: const Icon(Icons.settings),
            onPressed: _showTimerSettings,
          ),
        ],
      ),
      body: SingleChildScrollView(
        child: Padding(
          padding: const EdgeInsets.all(20.0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.center,
            children: [
              _buildTimerHeader(),
              const SizedBox(height: 40),
              _buildTimerCircle(),
              const SizedBox(height: 40),
              _buildTimerControls(),
              const SizedBox(height: 30),
              _buildSubjectSelector(),
              const SizedBox(height: 30),
              _buildSessionStats(),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildTimerHeader() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [
            _isBreak ? Colors.green : AppTheme.primaryColor,
            _isBreak ? Colors.green.shade700 : AppTheme.primaryDarkColor,
          ],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: (_isBreak ? Colors.green : AppTheme.primaryColor).withValues(
              alpha: 0.3 * 255,
            ),
            blurRadius: 10,
            offset: const Offset(0, 5),
          ),
        ],
      ),
      child: Row(
        children: [
          Icon(
            _isBreak ? Icons.coffee : Icons.timer,
            color: Colors.white,
            size: 30,
          ),
          const SizedBox(width: 16),
          Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                _isBreak ? "Break Time" : "Focus Time",
                style: const TextStyle(
                  color: Colors.white,
                  fontSize: 20,
                  fontWeight: FontWeight.bold,
                  letterSpacing: 0.5,
                  shadows: [
                    Shadow(
                      color: Colors.black26,
                      blurRadius: 2,
                      offset: Offset(0, 1),
                    ),
                  ],
                ),
              ),
              const SizedBox(height: 4),
              Text(
                _isBreak ? "Relax and recharge" : "Stay focused on your task",
                style: const TextStyle(
                  color: Colors.white,
                  fontSize: 14,
                  shadows: [
                    Shadow(
                      color: Colors.black26,
                      blurRadius: 2,
                      offset: Offset(0, 1),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildTimerCircle() {
    return Stack(
      alignment: Alignment.center,
      children: [
        SizedBox(
          width: 250,
          height: 250,
          child: AnimatedBuilder(
            animation: _animationController,
            builder: (context, child) {
              return CustomPaint(
                painter: TimerPainter(
                  animation: _animationController,
                  backgroundColor: Colors.grey.shade300,
                  color: _isBreak ? Colors.green : AppTheme.primaryColor,
                ),
                child: child,
              );
            },
            child: Container(
              width: 250,
              height: 250,
              padding: const EdgeInsets.all(40),
              decoration: BoxDecoration(
                shape: BoxShape.circle,
                color: Colors.white,
                boxShadow: [
                  BoxShadow(
                    color: Colors.grey.withValues(alpha: 0.1 * 255),
                    blurRadius: 10,
                    spreadRadius: 1,
                  ),
                ],
              ),
              child: Center(
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Text(
                      _formatTime(_currentTime),
                      style: TextStyle(
                        fontSize: 48,
                        fontWeight: FontWeight.bold,
                        color:
                            _isBreak
                                ? Colors.green.shade700
                                : AppTheme.primaryDarkColor,
                      ),
                    ),
                    const SizedBox(height: 8),
                    Text(
                      _isBreak ? "Break" : "Focus",
                      style: TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.w500,
                        color: _isBreak ? Colors.green : AppTheme.primaryColor,
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildTimerControls() {
    return Row(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        // Reset button
        ElevatedButton(
          onPressed: _isRunning ? _resetTimer : null,
          style: ElevatedButton.styleFrom(
            backgroundColor: Colors.grey.shade200,
            foregroundColor: Colors.black87,
            shape: const CircleBorder(),
            padding: const EdgeInsets.all(16),
          ),
          child: const Icon(Icons.refresh, size: 30),
        ),
        const SizedBox(width: 20),
        // Start/Pause button
        ElevatedButton(
          onPressed: () {
            if (_isRunning) {
              _pauseTimer();
            } else {
              _startTimer();
            }
          },
          style: ElevatedButton.styleFrom(
            backgroundColor: _isBreak ? Colors.green : AppTheme.primaryColor,
            foregroundColor: Colors.white,
            shape: const CircleBorder(),
            padding: const EdgeInsets.all(24),
          ),
          child: Icon(_isRunning ? Icons.pause : Icons.play_arrow, size: 40),
        ),
        const SizedBox(width: 20),
        // Skip button
        ElevatedButton(
          onPressed: () {
            if (_isRunning) {
              _timer.cancel();
            }
            _handleTimerComplete();
          },
          style: ElevatedButton.styleFrom(
            backgroundColor: Colors.grey.shade200,
            foregroundColor: Colors.black87,
            shape: const CircleBorder(),
            padding: const EdgeInsets.all(16),
          ),
          child: const Icon(Icons.skip_next, size: 30),
        ),
      ],
    );
  }

  Widget _buildSubjectSelector() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withValues(alpha: 0.1 * 255),
            blurRadius: 10,
            offset: const Offset(0, 5),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            "What are you studying?",
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.bold,
              color: Colors.black87,
            ),
          ),
          const SizedBox(height: 12),
          DropdownButtonFormField<String>(
            value: _currentSubject,
            decoration: InputDecoration(
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(12),
                borderSide: BorderSide(
                  color: Colors.grey.withValues(alpha: 0.3 * 255),
                ),
              ),
              contentPadding: const EdgeInsets.symmetric(
                horizontal: 16,
                vertical: 12,
              ),
            ),
            items:
                _subjects.map((subject) {
                  return DropdownMenuItem<String>(
                    value: subject,
                    child: Text(subject),
                  );
                }).toList(),
            onChanged: (value) {
              if (value != null) {
                setState(() {
                  _currentSubject = value;
                });
              }
            },
          ),
        ],
      ),
    );
  }

  Widget _buildSessionStats() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withValues(alpha: 0.1 * 255),
            blurRadius: 10,
            offset: const Offset(0, 5),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            "Today's Progress",
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.bold,
              color: Colors.black87,
            ),
          ),
          const SizedBox(height: 16),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceAround,
            children: [
              _buildStatItem(
                icon: Icons.timer,
                value: _completedSessions.toString(),
                label: "Sessions",
                color: AppTheme.primaryColor,
              ),
              _buildStatItem(
                icon: Icons.access_time,
                value: "${(_completedSessions * 25).toString()} min",
                label: "Focus Time",
                color: Colors.orange,
              ),
              _buildStatItem(
                icon: Icons.trending_up,
                value: _calculateStreak().toString(),
                label: "Streak",
                color: Colors.green,
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildStatItem({
    required IconData icon,
    required String value,
    required String label,
    required Color color,
  }) {
    return Column(
      children: [
        Container(
          padding: const EdgeInsets.all(12),
          decoration: BoxDecoration(
            color: color.withValues(alpha: 0.1 * 255),
            shape: BoxShape.circle,
          ),
          child: Icon(icon, color: color, size: 24),
        ),
        const SizedBox(height: 8),
        Text(
          value,
          style: TextStyle(
            fontSize: 18,
            fontWeight: FontWeight.bold,
            color: color,
          ),
        ),
        Text(
          label,
          style: TextStyle(fontSize: 12, color: Colors.grey.shade600),
        ),
      ],
    );
  }

  int _calculateStreak() {
    // In a real app, this would calculate the actual streak from stored data
    return 3; // Placeholder value
  }

  void _showSessionHistory() {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
      ),
      builder: (context) {
        return Container(
          padding: const EdgeInsets.all(20),
          height: MediaQuery.of(context).size.height * 0.7,
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              const Text(
                "Session History",
                style: TextStyle(fontSize: 20, fontWeight: FontWeight.bold),
              ),
              const SizedBox(height: 20),
              Expanded(
                child:
                    _sessionHistory.isEmpty
                        ? const Center(
                          child: Text(
                            "No sessions completed yet",
                            style: TextStyle(color: Colors.grey),
                          ),
                        )
                        : ListView.builder(
                          itemCount: _sessionHistory.length,
                          itemBuilder: (context, index) {
                            final session =
                                _sessionHistory[_sessionHistory.length -
                                    1 -
                                    index];
                            return Card(
                              margin: const EdgeInsets.only(bottom: 12),
                              child: ListTile(
                                leading: CircleAvatar(
                                  backgroundColor: AppTheme.primaryColor
                                      .withValues(alpha: 0.1 * 255),
                                  child: const Icon(
                                    Icons.timer,
                                    color: AppTheme.primaryColor,
                                  ),
                                ),
                                title: Text(session['subject']),
                                subtitle: Text(
                                  "Duration: ${session['duration'] ~/ 60} minutes",
                                ),
                                trailing: Text(
                                  DateTime.parse(
                                    session['timestamp'],
                                  ).toString().substring(11, 16),
                                  style: TextStyle(color: Colors.grey.shade600),
                                ),
                              ),
                            );
                          },
                        ),
              ),
            ],
          ),
        );
      },
    );
  }

  void _showTimerSettings() {
    // This would show a dialog to customize timer settings
    showDialog(
      context: context,
      builder: (context) {
        return AlertDialog(
          title: const Text("Timer Settings"),
          content: const Text("Customization options coming soon!"),
          actions: [
            TextButton(
              onPressed: () => Navigator.pop(context),
              child: const Text("OK"),
            ),
          ],
        );
      },
    );
  }
}
