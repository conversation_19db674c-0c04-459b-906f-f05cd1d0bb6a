import 'package:flutter/material.dart';
import 'package:fit_4_force/core/services/notification_service.dart';
import 'package:fit_4_force/core/theme/app_theme.dart';

class NotificationPreferencesWidget extends StatefulWidget {
  final String userId;
  final Function(NotificationPreferences)? onPreferencesChanged;

  const NotificationPreferencesWidget({
    super.key,
    required this.userId,
    this.onPreferencesChanged,
  });

  @override
  State<NotificationPreferencesWidget> createState() => _NotificationPreferencesWidgetState();
}

class _NotificationPreferencesWidgetState extends State<NotificationPreferencesWidget> {
  final NotificationService _notificationService = NotificationService();
  NotificationPreferences _preferences = const NotificationPreferences();
  bool _isLoading = true;
  bool _isSaving = false;

  @override
  void initState() {
    super.initState();
    _loadPreferences();
  }

  Future<void> _loadPreferences() async {
    try {
      final preferences = await _notificationService.getUserNotificationPreferences(widget.userId);
      setState(() {
        _preferences = preferences;
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _isLoading = false;
      });
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Error loading preferences: $e')),
        );
      }
    }
  }

  Future<void> _savePreferences() async {
    setState(() {
      _isSaving = true;
    });

    try {
      final success = await _notificationService.updateNotificationPreferences(
        widget.userId,
        _preferences,
      );

      if (success) {
        widget.onPreferencesChanged?.call(_preferences);
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('Notification preferences saved!'),
              backgroundColor: Colors.green,
            ),
          );
        }
      } else {
        throw Exception('Failed to save preferences');
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Error saving preferences: $e')),
        );
      }
    } finally {
      setState(() {
        _isSaving = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    if (_isLoading) {
      return const Center(
        child: CircularProgressIndicator(),
      );
    }

    return Card(
      margin: const EdgeInsets.all(16),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                const Icon(Icons.notifications, color: AppTheme.primaryColor),
                const SizedBox(width: 8),
                Text(
                  'Notification Preferences',
                  style: Theme.of(context).textTheme.titleLarge?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            
            // Master toggle for push notifications
            _buildPreferenceItem(
              title: 'Push Notifications',
              subtitle: 'Enable all push notifications',
              value: _preferences.pushNotifications,
              onChanged: (value) {
                setState(() {
                  _preferences = NotificationPreferences(
                    pushNotifications: value,
                    emailNotifications: _preferences.emailNotifications,
                    communityUpdates: _preferences.communityUpdates,
                    workoutReminders: _preferences.workoutReminders,
                    studyGroupNotifications: _preferences.studyGroupNotifications,
                    achievementNotifications: _preferences.achievementNotifications,
                    premiumNotifications: _preferences.premiumNotifications,
                  );
                });
              },
              icon: Icons.notifications_active,
              isMainToggle: true,
            ),
            
            const Divider(),
            
            // Email notifications
            _buildPreferenceItem(
              title: 'Email Notifications',
              subtitle: 'Receive notifications via email',
              value: _preferences.emailNotifications,
              onChanged: (value) {
                setState(() {
                  _preferences = NotificationPreferences(
                    pushNotifications: _preferences.pushNotifications,
                    emailNotifications: value,
                    communityUpdates: _preferences.communityUpdates,
                    workoutReminders: _preferences.workoutReminders,
                    studyGroupNotifications: _preferences.studyGroupNotifications,
                    achievementNotifications: _preferences.achievementNotifications,
                    premiumNotifications: _preferences.premiumNotifications,
                  );
                });
              },
              icon: Icons.email,
            ),
            
            const SizedBox(height: 16),
            Text(
              'Notification Categories',
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.bold,
                color: Colors.grey[700],
              ),
            ),
            const SizedBox(height: 8),
            
            // Community updates
            _buildPreferenceItem(
              title: 'Community Updates',
              subtitle: 'New posts, comments, and replies',
              value: _preferences.communityUpdates && _preferences.pushNotifications,
              onChanged: _preferences.pushNotifications ? (value) {
                setState(() {
                  _preferences = NotificationPreferences(
                    pushNotifications: _preferences.pushNotifications,
                    emailNotifications: _preferences.emailNotifications,
                    communityUpdates: value,
                    workoutReminders: _preferences.workoutReminders,
                    studyGroupNotifications: _preferences.studyGroupNotifications,
                    achievementNotifications: _preferences.achievementNotifications,
                    premiumNotifications: _preferences.premiumNotifications,
                  );
                });
              } : null,
              icon: Icons.forum,
            ),
            
            // Workout reminders
            _buildPreferenceItem(
              title: 'Workout Reminders',
              subtitle: 'Scheduled training sessions and fitness goals',
              value: _preferences.workoutReminders && _preferences.pushNotifications,
              onChanged: _preferences.pushNotifications ? (value) {
                setState(() {
                  _preferences = NotificationPreferences(
                    pushNotifications: _preferences.pushNotifications,
                    emailNotifications: _preferences.emailNotifications,
                    communityUpdates: _preferences.communityUpdates,
                    workoutReminders: value,
                    studyGroupNotifications: _preferences.studyGroupNotifications,
                    achievementNotifications: _preferences.achievementNotifications,
                    premiumNotifications: _preferences.premiumNotifications,
                  );
                });
              } : null,
              icon: Icons.fitness_center,
            ),
            
            // Study group notifications
            _buildPreferenceItem(
              title: 'Study Group Activities',
              subtitle: 'Messages and announcements in study groups',
              value: _preferences.studyGroupNotifications && _preferences.pushNotifications,
              onChanged: _preferences.pushNotifications ? (value) {
                setState(() {
                  _preferences = NotificationPreferences(
                    pushNotifications: _preferences.pushNotifications,
                    emailNotifications: _preferences.emailNotifications,
                    communityUpdates: _preferences.communityUpdates,
                    workoutReminders: _preferences.workoutReminders,
                    studyGroupNotifications: value,
                    achievementNotifications: _preferences.achievementNotifications,
                    premiumNotifications: _preferences.premiumNotifications,
                  );
                });
              } : null,
              icon: Icons.group,
            ),
            
            // Achievement notifications
            _buildPreferenceItem(
              title: 'Achievements & Badges',
              subtitle: 'Milestone celebrations and rewards',
              value: _preferences.achievementNotifications && _preferences.pushNotifications,
              onChanged: _preferences.pushNotifications ? (value) {
                setState(() {
                  _preferences = NotificationPreferences(
                    pushNotifications: _preferences.pushNotifications,
                    emailNotifications: _preferences.emailNotifications,
                    communityUpdates: _preferences.communityUpdates,
                    workoutReminders: _preferences.workoutReminders,
                    studyGroupNotifications: _preferences.studyGroupNotifications,
                    achievementNotifications: value,
                    premiumNotifications: _preferences.premiumNotifications,
                  );
                });
              } : null,
              icon: Icons.emoji_events,
            ),
            
            // Premium notifications
            _buildPreferenceItem(
              title: 'Premium Updates',
              subtitle: 'Subscription renewals and premium features',
              value: _preferences.premiumNotifications && _preferences.pushNotifications,
              onChanged: _preferences.pushNotifications ? (value) {
                setState(() {
                  _preferences = NotificationPreferences(
                    pushNotifications: _preferences.pushNotifications,
                    emailNotifications: _preferences.emailNotifications,
                    communityUpdates: _preferences.communityUpdates,
                    workoutReminders: _preferences.workoutReminders,
                    studyGroupNotifications: _preferences.studyGroupNotifications,
                    achievementNotifications: _preferences.achievementNotifications,
                    premiumNotifications: value,
                  );
                });
              } : null,
              icon: Icons.workspace_premium,
            ),
            
            const SizedBox(height: 24),
            
            // Save button
            SizedBox(
              width: double.infinity,
              child: ElevatedButton.icon(
                onPressed: _isSaving ? null : _savePreferences,
                icon: _isSaving 
                    ? const SizedBox(
                        width: 16,
                        height: 16,
                        child: CircularProgressIndicator(strokeWidth: 2),
                      )
                    : const Icon(Icons.save),
                label: Text(_isSaving ? 'Saving...' : 'Save Preferences'),
                style: ElevatedButton.styleFrom(
                  backgroundColor: AppTheme.primaryColor,
                  foregroundColor: Colors.white,
                  padding: const EdgeInsets.symmetric(vertical: 16),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildPreferenceItem({
    required String title,
    required String subtitle,
    required bool value,
    required Function(bool)? onChanged,
    required IconData icon,
    bool isMainToggle = false,
  }) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 8),
      child: Row(
        children: [
          Icon(
            icon,
            color: isMainToggle ? AppTheme.primaryColor : Colors.grey[600],
            size: isMainToggle ? 24 : 20,
          ),
          const SizedBox(width: 16),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  title,
                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                    fontWeight: isMainToggle ? FontWeight.bold : FontWeight.w500,
                    color: onChanged == null ? Colors.grey : null,
                  ),
                ),
                Text(
                  subtitle,
                  style: Theme.of(context).textTheme.bodySmall?.copyWith(
                    color: onChanged == null ? Colors.grey : Colors.grey[600],
                  ),
                ),
              ],
            ),
          ),
          Switch(
            value: value,
            onChanged: onChanged,
            activeColor: AppTheme.primaryColor,
          ),
        ],
      ),
    );
  }
}
