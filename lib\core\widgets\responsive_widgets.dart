import 'package:flutter/material.dart';
import '../utils/responsive_utils.dart';

/// Responsive text widget that adapts font size based on screen size
class ResponsiveText extends StatelessWidget {
  final String text;
  final double? mobileFontSize;
  final double? tabletFontSize;
  final double? desktopFontSize;
  final FontWeight? fontWeight;
  final Color? color;
  final TextAlign? textAlign;
  final int? maxLines;
  final TextOverflow? overflow;
  final TextStyle? style;

  const ResponsiveText(
    this.text, {
    super.key,
    this.mobileFontSize,
    this.tabletFontSize,
    this.desktopFontSize,
    this.fontWeight,
    this.color,
    this.textAlign,
    this.maxLines,
    this.overflow,
    this.style,
  });

  @override
  Widget build(BuildContext context) {
    final fontSize = ResponsiveUtils.getResponsiveFontSize(
      context,
      mobile: mobileFontSize ?? 14.0,
      tablet: tabletFontSize ?? 16.0,
      desktop: desktopFontSize ?? 18.0,
    );

    return Text(
      text,
      style: (style ?? const TextStyle()).copyWith(
        fontSize: fontSize,
        fontWeight: fontWeight,
        color: color,
      ),
      textAlign: textAlign,
      maxLines: maxLines,
      overflow: overflow,
    );
  }
}

/// Responsive container that adapts its size based on screen size
class ResponsiveContainer extends StatelessWidget {
  final Widget child;
  final double? mobileWidth;
  final double? tabletWidth;
  final double? desktopWidth;
  final double? mobileHeight;
  final double? tabletHeight;
  final double? desktopHeight;
  final EdgeInsets? mobilePadding;
  final EdgeInsets? tabletPadding;
  final EdgeInsets? desktopPadding;
  final EdgeInsets? mobileMargin;
  final EdgeInsets? tabletMargin;
  final EdgeInsets? desktopMargin;
  final Decoration? decoration;
  final AlignmentGeometry? alignment;

  const ResponsiveContainer({
    super.key,
    required this.child,
    this.mobileWidth,
    this.tabletWidth,
    this.desktopWidth,
    this.mobileHeight,
    this.tabletHeight,
    this.desktopHeight,
    this.mobilePadding,
    this.tabletPadding,
    this.desktopPadding,
    this.mobileMargin,
    this.tabletMargin,
    this.desktopMargin,
    this.decoration,
    this.alignment,
  });

  @override
  Widget build(BuildContext context) {
    final width = ResponsiveUtils.getResponsiveWidth(
      context,
      mobile: mobileWidth ?? double.infinity,
      tablet: tabletWidth ?? double.infinity,
      desktop: desktopWidth ?? double.infinity,
    );

    final height = ResponsiveUtils.getResponsiveHeight(
      context,
      mobile: mobileHeight ?? double.infinity,
      tablet: tabletHeight ?? double.infinity,
      desktop: desktopHeight ?? double.infinity,
    );

    final padding = ResponsiveUtils.getResponsivePadding(
      context,
      mobile: mobilePadding ?? EdgeInsets.zero,
      tablet: tabletPadding ?? EdgeInsets.zero,
      desktop: desktopPadding ?? EdgeInsets.zero,
    );

    final margin = ResponsiveUtils.getResponsivePadding(
      context,
      mobile: mobileMargin ?? EdgeInsets.zero,
      tablet: tabletMargin ?? EdgeInsets.zero,
      desktop: desktopMargin ?? EdgeInsets.zero,
    );

    return Container(
      width: width == double.infinity ? null : width,
      height: height == double.infinity ? null : height,
      padding: padding,
      margin: margin,
      decoration: decoration,
      alignment: alignment,
      child: child,
    );
  }
}

/// Responsive grid view that adapts column count based on screen size
class ResponsiveGridView extends StatelessWidget {
  final List<Widget> children;
  final int? mobileColumns;
  final int? tabletColumns;
  final int? desktopColumns;
  final double? childAspectRatio;
  final double? crossAxisSpacing;
  final double? mainAxisSpacing;
  final EdgeInsets? padding;
  final bool shrinkWrap;
  final ScrollPhysics? physics;

  const ResponsiveGridView({
    super.key,
    required this.children,
    this.mobileColumns,
    this.tabletColumns,
    this.desktopColumns,
    this.childAspectRatio,
    this.crossAxisSpacing,
    this.mainAxisSpacing,
    this.padding,
    this.shrinkWrap = false,
    this.physics,
  });

  @override
  Widget build(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;
    final screenHeight = MediaQuery.of(context).size.height;
    final isSmallScreen = screenWidth < 400;
    final isVerySmallScreen = screenWidth < 360;

    // Enhanced column calculation for better mobile support
    int columns;
    if (ResponsiveUtils.isMobile(context)) {
      if (isVerySmallScreen) {
        columns = 1; // Single column for very small screens
      } else if (isSmallScreen) {
        columns = mobileColumns ?? 2;
      } else {
        columns = mobileColumns ?? 2;
      }
    } else if (ResponsiveUtils.isTablet(context)) {
      columns = tabletColumns ?? 3;
    } else {
      columns = desktopColumns ?? 4;
    }

    // Enhanced aspect ratio calculation
    double aspectRatio = childAspectRatio ?? 1.0;
    if (ResponsiveUtils.isMobile(context)) {
      // Adjust aspect ratio for mobile to prevent overlapping
      if (isVerySmallScreen) {
        aspectRatio = 1.2; // Taller cards for very small screens
      } else {
        aspectRatio = 1.1; // Slightly taller for mobile
      }
    }

    // Enhanced spacing calculation
    final spacing = ResponsiveUtils.getResponsiveSpacing(context);
    final crossSpacing = crossAxisSpacing ?? spacing;
    final mainSpacing = mainAxisSpacing ?? spacing;

    // Enhanced padding calculation
    final responsivePadding =
        padding ??
        EdgeInsets.symmetric(
          horizontal: ResponsiveUtils.isMobile(context) ? 12.0 : 16.0,
          vertical: ResponsiveUtils.isMobile(context) ? 8.0 : 12.0,
        );

    return GridView.count(
      crossAxisCount: columns,
      childAspectRatio: aspectRatio,
      crossAxisSpacing: crossSpacing,
      mainAxisSpacing: mainSpacing,
      padding: responsivePadding,
      shrinkWrap: shrinkWrap,
      physics: physics,
      children: children,
    );
  }
}

/// Responsive button that adapts its size based on screen size
class ResponsiveButton extends StatelessWidget {
  final String text;
  final VoidCallback? onPressed;
  final double? mobileWidth;
  final double? tabletWidth;
  final double? desktopWidth;
  final double? mobileHeight;
  final double? tabletHeight;
  final double? desktopHeight;
  final double? mobileFontSize;
  final double? tabletFontSize;
  final double? desktopFontSize;
  final Color? backgroundColor;
  final Color? textColor;
  final BorderRadius? borderRadius;
  final Widget? icon;

  const ResponsiveButton({
    super.key,
    required this.text,
    this.onPressed,
    this.mobileWidth,
    this.tabletWidth,
    this.desktopWidth,
    this.mobileHeight,
    this.tabletHeight,
    this.desktopHeight,
    this.mobileFontSize,
    this.tabletFontSize,
    this.desktopFontSize,
    this.backgroundColor,
    this.textColor,
    this.borderRadius,
    this.icon,
  });

  @override
  Widget build(BuildContext context) {
    final width = ResponsiveUtils.getResponsiveWidth(
      context,
      mobile: mobileWidth ?? double.infinity,
      tablet: tabletWidth ?? double.infinity,
      desktop: desktopWidth ?? double.infinity,
    );

    final height = ResponsiveUtils.getResponsiveHeight(
      context,
      mobile: mobileHeight ?? 48.0,
      tablet: tabletHeight ?? 52.0,
      desktop: desktopHeight ?? 56.0,
    );

    final fontSize = ResponsiveUtils.getAccessibleFontSize(
      context,
      mobile: mobileFontSize ?? 14.0,
      tablet: tabletFontSize ?? 16.0,
      desktop: desktopFontSize ?? 18.0,
    );

    final borderRadius = ResponsiveUtils.getResponsiveBorderRadius(context);

    return SizedBox(
      width: width == double.infinity ? null : width,
      height: height,
      child: ElevatedButton(
        onPressed: onPressed,
        style: ElevatedButton.styleFrom(
          backgroundColor: backgroundColor,
          foregroundColor: textColor,
          elevation: ResponsiveUtils.getResponsiveElevation(context),
          shape: RoundedRectangleBorder(
            borderRadius:
                this.borderRadius ?? BorderRadius.circular(borderRadius),
          ),
        ),
        child:
            icon != null
                ? Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    icon!,
                    const SizedBox(width: 8),
                    Text(text, style: TextStyle(fontSize: fontSize)),
                  ],
                )
                : Text(text, style: TextStyle(fontSize: fontSize)),
      ),
    );
  }
}

/// Responsive card with adaptive layout
class ResponsiveCard extends StatelessWidget {
  final Widget child;
  final EdgeInsets? padding;
  final EdgeInsets? margin;
  final Color? color;
  final double? elevation;
  final BorderRadius? borderRadius;

  const ResponsiveCard({
    super.key,
    required this.child,
    this.padding,
    this.margin,
    this.color,
    this.elevation,
    this.borderRadius,
  });

  @override
  Widget build(BuildContext context) {
    final responsivePadding = ResponsiveUtils.getResponsivePadding(context);
    final responsiveElevation = ResponsiveUtils.getResponsiveElevation(context);
    final responsiveBorderRadius = ResponsiveUtils.getResponsiveBorderRadius(
      context,
    );

    return Container(
      margin: margin ?? responsivePadding,
      child: Card(
        color: color,
        elevation: elevation ?? responsiveElevation,
        shape: RoundedRectangleBorder(
          borderRadius:
              borderRadius ?? BorderRadius.circular(responsiveBorderRadius),
        ),
        child: Padding(padding: padding ?? responsivePadding, child: child),
      ),
    );
  }
}

/// Multi-pane layout for tablets and desktop
class ResponsiveMultiPaneLayout extends StatelessWidget {
  final Widget primaryPane;
  final Widget? secondaryPane;
  final double? primaryFlex;
  final double? secondaryFlex;
  final Axis direction;

  const ResponsiveMultiPaneLayout({
    super.key,
    required this.primaryPane,
    this.secondaryPane,
    this.primaryFlex = 2,
    this.secondaryFlex = 1,
    this.direction = Axis.horizontal,
  });

  @override
  Widget build(BuildContext context) {
    final supportsMultiPane = ResponsiveUtils.supportsMultiPane(context);
    final layoutType = ResponsiveUtils.getLayoutType(context);

    if (!supportsMultiPane || secondaryPane == null) {
      return primaryPane;
    }

    // For desktop and large tablets, use multi-pane layout
    if (layoutType == LayoutType.desktop ||
        layoutType == LayoutType.tabletLandscape) {
      return Row(
        children: [
          Expanded(flex: primaryFlex!.toInt(), child: primaryPane),
          const VerticalDivider(width: 1),
          Expanded(flex: secondaryFlex!.toInt(), child: secondaryPane!),
        ],
      );
    }

    // For tablet portrait, stack vertically
    if (layoutType == LayoutType.tabletPortrait) {
      return Column(
        children: [
          Expanded(flex: primaryFlex!.toInt(), child: primaryPane),
          const Divider(height: 1),
          Expanded(flex: secondaryFlex!.toInt(), child: secondaryPane!),
        ],
      );
    }

    return primaryPane;
  }
}

/// Responsive image that adapts to screen size
class ResponsiveImage extends StatelessWidget {
  final String imageUrl;
  final String? placeholder;
  final BoxFit? fit;
  final double? mobileHeight;
  final double? tabletHeight;
  final double? desktopHeight;

  const ResponsiveImage({
    super.key,
    required this.imageUrl,
    this.placeholder,
    this.fit,
    this.mobileHeight,
    this.tabletHeight,
    this.desktopHeight,
  });

  @override
  Widget build(BuildContext context) {
    final height = ResponsiveUtils.getResponsiveHeight(
      context,
      mobile: mobileHeight ?? 200.0,
      tablet: tabletHeight ?? 300.0,
      desktop: desktopHeight ?? 400.0,
    );

    return Container(
      height: height,
      width: double.infinity,
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(
          ResponsiveUtils.getResponsiveBorderRadius(context),
        ),
      ),
      clipBehavior: Clip.antiAlias,
      child:
          imageUrl.startsWith('http')
              ? Image.network(
                imageUrl,
                fit: fit ?? BoxFit.cover,
                errorBuilder: (context, error, stackTrace) {
                  return _buildPlaceholder(context);
                },
              )
              : Image.asset(
                imageUrl,
                fit: fit ?? BoxFit.cover,
                errorBuilder: (context, error, stackTrace) {
                  return _buildPlaceholder(context);
                },
              ),
    );
  }

  Widget _buildPlaceholder(BuildContext context) {
    return Container(
      color: Colors.grey[300],
      child: Center(
        child: Icon(
          Icons.image,
          size: ResponsiveUtils.getResponsiveFontSize(
            context,
            mobile: 48,
            tablet: 64,
            desktop: 80,
          ),
          color: Colors.grey[600],
        ),
      ),
    );
  }
}
