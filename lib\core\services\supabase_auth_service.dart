import 'package:supabase_flutter/supabase_flutter.dart';
import 'package:fit_4_force/core/config/supabase_config.dart';
import 'package:fit_4_force/shared/models/user_model.dart';
import 'package:logger/logger.dart';

class SupabaseAuthService {
  static final _logger = Logger();
  static final SupabaseClient _client = SupabaseConfig.client;

  // Get current user
  static User? get currentUser => _client.auth.currentUser;
  static Session? get currentSession => _client.auth.currentSession;
  static bool get isAuthenticated => currentUser != null;

  // Auth state stream
  static Stream<AuthState> get authStateChanges =>
      _client.auth.onAuthStateChange;

  /// Sign up with email and password
  static Future<AuthResponse> signUp({
    required String email,
    required String password,
    required String fullName,
    required String targetAgency,
    Map<String, dynamic>? additionalData,
  }) async {
    try {
      _logger.i('🔐 Attempting to sign up user: $email');

      final response = await _client.auth.signUp(
        email: email,
        password: password,
        data: {
          'full_name': fullName,
          'target_agency': targetAgency,
          ...?additionalData,
        },
      );

      if (response.user != null) {
        _logger.i('✅ User signed up successfully: ${response.user!.id}');

        // Create user profile in our custom users table
        await _createUserProfile(
          userId: response.user!.id,
          email: email,
          fullName: fullName,
          targetAgency: targetAgency,
          additionalData: additionalData,
        );
      }

      return response;
    } catch (e) {
      _logger.e('❌ Sign up failed: $e');
      rethrow;
    }
  }

  /// Sign in with email and password
  static Future<AuthResponse> signIn({
    required String email,
    required String password,
  }) async {
    try {
      _logger.i('🔐 Attempting to sign in user: $email');

      final response = await _client.auth.signInWithPassword(
        email: email,
        password: password,
      );

      if (response.user != null) {
        _logger.i('✅ User signed in successfully: ${response.user!.id}');

        // Update last active timestamp
        await _updateLastActive(response.user!.id);
      }

      return response;
    } catch (e) {
      _logger.e('❌ Sign in failed: $e');
      rethrow;
    }
  }

  /// Sign out
  static Future<void> signOut() async {
    try {
      _logger.i('🔐 Signing out user');
      await _client.auth.signOut();
      _logger.i('✅ User signed out successfully');
    } catch (e) {
      _logger.e('❌ Sign out failed: $e');
      rethrow;
    }
  }

  /// Reset password
  static Future<void> resetPassword({required String email}) async {
    try {
      _logger.i('🔐 Sending password reset email to: $email');

      await _client.auth.resetPasswordForEmail(
        email,
        redirectTo:
            'https://your-app.com/reset-password', // Update with your app's deep link
      );

      _logger.i('✅ Password reset email sent successfully');
    } catch (e) {
      _logger.e('❌ Password reset failed: $e');
      rethrow;
    }
  }

  /// Update password
  static Future<UserResponse> updatePassword({
    required String newPassword,
  }) async {
    try {
      _logger.i('🔐 Updating user password');

      final response = await _client.auth.updateUser(
        UserAttributes(password: newPassword),
      );

      _logger.i('✅ Password updated successfully');
      return response;
    } catch (e) {
      _logger.e('❌ Password update failed: $e');
      rethrow;
    }
  }

  /// Update user email
  static Future<UserResponse> updateEmail({required String newEmail}) async {
    try {
      _logger.i('🔐 Updating user email to: $newEmail');

      final response = await _client.auth.updateUser(
        UserAttributes(email: newEmail),
      );

      _logger.i('✅ Email updated successfully');
      return response;
    } catch (e) {
      _logger.e('❌ Email update failed: $e');
      rethrow;
    }
  }

  /// Get user profile from our custom users table
  static Future<UserModel?> getUserProfile({String? userId}) async {
    try {
      final uid = userId ?? currentUser?.id;
      if (uid == null) return null;

      _logger.i('📱 Fetching user profile for: $uid');

      final response =
          await _client
              .from(SupabaseConfig.usersTable)
              .select()
              .eq('id', uid)
              .maybeSingle();

      if (response == null) {
        _logger.w('⚠️ User profile not found for: $uid');
        return null;
      }

      final userModel = UserModel.fromJson(response);
      _logger.i('✅ User profile fetched successfully');
      return userModel;
    } catch (e) {
      _logger.e('❌ Failed to fetch user profile: $e');
      return null;
    }
  }

  /// Update user profile
  static Future<void> updateUserProfile({
    required String userId,
    required Map<String, dynamic> updates,
  }) async {
    try {
      _logger.i('📱 Updating user profile for: $userId');

      await _client
          .from(SupabaseConfig.usersTable)
          .update({...updates, 'updated_at': DateTime.now().toIso8601String()})
          .eq('id', userId);

      _logger.i('✅ User profile updated successfully');
    } catch (e) {
      _logger.e('❌ Failed to update user profile: $e');
      rethrow;
    }
  }

  /// Delete user account
  static Future<void> deleteAccount() async {
    try {
      final user = currentUser;
      if (user == null) throw Exception('No authenticated user');

      _logger.i('🗑️ Deleting user account: ${user.id}');

      // First, delete user data from our custom tables
      await _client.from(SupabaseConfig.usersTable).delete().eq('id', user.id);

      // Then delete the auth user (this requires admin privileges)
      // In production, you might want to handle this via a server function
      await _client.auth.admin.deleteUser(user.id);

      _logger.i('✅ User account deleted successfully');
    } catch (e) {
      _logger.e('❌ Failed to delete user account: $e');
      rethrow;
    }
  }

  /// Check if email is already registered
  static Future<bool> isEmailRegistered(String email) async {
    try {
      final response =
          await _client
              .from(SupabaseConfig.usersTable)
              .select('id')
              .eq('email', email)
              .maybeSingle();

      return response != null;
    } catch (e) {
      _logger.e('❌ Failed to check email registration: $e');
      return false;
    }
  }

  /// Refresh session
  static Future<AuthResponse> refreshSession() async {
    try {
      _logger.i('🔄 Refreshing user session');

      final response = await _client.auth.refreshSession();

      _logger.i('✅ Session refreshed successfully');
      return response;
    } catch (e) {
      _logger.e('❌ Session refresh failed: $e');
      rethrow;
    }
  }

  // Private helper methods

  /// Create user profile in custom users table
  static Future<void> _createUserProfile({
    required String userId,
    required String email,
    required String fullName,
    required String targetAgency,
    Map<String, dynamic>? additionalData,
  }) async {
    try {
      // Create user profile
      await _client.from(SupabaseConfig.usersTable).insert({
        'id': userId,
        'email': email,
        'full_name': fullName,
        'target_agency': targetAgency,
        'created_at': DateTime.now().toIso8601String(),
        'updated_at': DateTime.now().toIso8601String(),
        'last_active_at': DateTime.now().toIso8601String(),
        ...?additionalData,
      });

      // Get agency ID from agency code
      final agencyResponse =
          await _client
              .from('agencies')
              .select('id')
              .eq('code', targetAgency)
              .maybeSingle();

      if (agencyResponse != null) {
        // Create user preferences with target agency
        await _client.from('user_preferences').insert({
          'user_id': userId,
          'target_agency_id': agencyResponse['id'],
          'is_premium': false,
          'notification_preferences': {
            'email': true,
            'push': true,
            'sms': false,
          },
          'study_preferences': {
            'difficulty': 'intermediate',
            'content_types': ['pdf', 'quiz', 'video'],
          },
          'created_at': DateTime.now().toIso8601String(),
          'updated_at': DateTime.now().toIso8601String(),
        });
      }

      _logger.i('✅ User profile and preferences created successfully');
    } catch (e) {
      _logger.e('❌ Failed to create user profile: $e');
      rethrow;
    }
  }

  /// Update user's last active timestamp
  static Future<void> _updateLastActive(String userId) async {
    try {
      await _client
          .from(SupabaseConfig.usersTable)
          .update({'last_active_at': DateTime.now().toIso8601String()})
          .eq('id', userId);
    } catch (e) {
      _logger.w('⚠️ Failed to update last active: $e');
      // Don't rethrow as this is not critical
    }
  }

  /// Convert Supabase auth error to user-friendly message
  static String getErrorMessage(dynamic error) {
    if (error is AuthException) {
      switch (error.message.toLowerCase()) {
        case 'invalid login credentials':
          return 'Invalid email or password. Please check your credentials.';
        case 'email not confirmed':
          return 'Please check your email and click the confirmation link.';
        case 'user already registered':
          return 'An account with this email already exists.';
        case 'password should be at least 6 characters':
          return 'Password must be at least 6 characters long.';
        case 'invalid email':
          return 'Please enter a valid email address.';
        default:
          return error.message;
      }
    }
    return error.toString();
  }
}
