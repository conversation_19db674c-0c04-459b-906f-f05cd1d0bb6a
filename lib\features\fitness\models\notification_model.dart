import 'package:flutter/material.dart';

/// Model representing a notification
class NotificationModel {
  final String id;
  final String title;
  final String message;
  final DateTime timestamp;
  final NotificationType type;
  final String? relatedId; // ID of related item (workout, plan, etc.)
  final bool isRead;

  const NotificationModel({
    required this.id,
    required this.title,
    required this.message,
    required this.timestamp,
    required this.type,
    this.relatedId,
    this.isRead = false,
  });

  // Create a copy of the notification with modified properties
  NotificationModel copyWith({
    String? id,
    String? title,
    String? message,
    DateTime? timestamp,
    NotificationType? type,
    String? relatedId,
    bool? isRead,
  }) {
    return NotificationModel(
      id: id ?? this.id,
      title: title ?? this.title,
      message: message ?? this.message,
      timestamp: timestamp ?? this.timestamp,
      type: type ?? this.type,
      relatedId: relatedId ?? this.relatedId,
      isRead: isRead ?? this.isRead,
    );
  }

  // Convert notification to map
  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'title': title,
      'message': message,
      'timestamp': timestamp.toIso8601String(),
      'type': type.toString(),
      'relatedId': relatedId,
      'isRead': isRead,
    };
  }

  // Create notification from map
  factory NotificationModel.fromMap(Map<String, dynamic> map) {
    return NotificationModel(
      id: map['id'],
      title: map['title'],
      message: map['message'],
      timestamp: DateTime.parse(map['timestamp']),
      type: NotificationType.values.firstWhere(
        (e) => e.toString() == map['type'],
        orElse: () => NotificationType.general,
      ),
      relatedId: map['relatedId'],
      isRead: map['isRead'] ?? false,
    );
  }

  // Get icon based on notification type
  IconData get icon {
    if (type == NotificationType.workout) {
      return Icons.fitness_center;
    } else if (type == NotificationType.achievement) {
      return Icons.emoji_events;
    } else if (type == NotificationType.reminder) {
      return Icons.alarm;
    } else if (type == NotificationType.plan) {
      return Icons.calendar_today;
    } else {
      return Icons.notifications; // Default for general type
    }
  }

  // Get color based on notification type
  Color get color {
    if (type == NotificationType.workout) {
      return Colors.green;
    } else if (type == NotificationType.achievement) {
      return Colors.amber;
    } else if (type == NotificationType.reminder) {
      return Colors.blue;
    } else if (type == NotificationType.plan) {
      return Colors.purple;
    } else {
      return Colors.grey; // Default for general type
    }
  }

  // Get formatted time string
  String get formattedTime {
    final now = DateTime.now();
    final difference = now.difference(timestamp);

    if (difference.inMinutes < 1) {
      return 'Just now';
    } else if (difference.inHours < 1) {
      return '${difference.inMinutes} min ago';
    } else if (difference.inDays < 1) {
      return '${difference.inHours} hours ago';
    } else if (difference.inDays < 7) {
      return '${difference.inDays} days ago';
    } else {
      return '${timestamp.day}/${timestamp.month}/${timestamp.year}';
    }
  }
}

/// Enum representing notification types
enum NotificationType { general, workout, achievement, reminder, plan }
