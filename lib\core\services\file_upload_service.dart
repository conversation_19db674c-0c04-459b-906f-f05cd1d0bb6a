import 'dart:io';
import 'package:file_picker/file_picker.dart';
import 'package:image_picker/image_picker.dart';
import 'package:mime/mime.dart';
import 'package:path/path.dart' as path;
import 'package:supabase_flutter/supabase_flutter.dart';
import 'package:logger/logger.dart';

/// Service for handling file uploads to Supabase Storage
class FileUploadService {
  static final FileUploadService _instance = FileUploadService._internal();
  factory FileUploadService() => _instance;
  FileUploadService._internal();

  final SupabaseClient _client = Supabase.instance.client;
  final Logger _logger = Logger();
  final ImagePicker _imagePicker = ImagePicker();

  /// Upload profile image
  Future<String?> uploadProfileImage({
    required String userId,
    required File imageFile,
    Function(double)? onProgress,
  }) async {
    try {
      final fileName = '${DateTime.now().millisecondsSinceEpoch}.jpg';
      final filePath = '$userId/$fileName';

      _logger.i('📤 Uploading profile image: $filePath');

      final response = await _client.storage
          .from('profile-images')
          .upload(filePath, imageFile);

      if (response.isNotEmpty) {
        final publicUrl = _client.storage
            .from('profile-images')
            .getPublicUrl(filePath);

        _logger.i('✅ Profile image uploaded successfully: $publicUrl');
        return publicUrl;
      }

      return null;
    } catch (e) {
      _logger.e('❌ Error uploading profile image: $e');
      return null;
    }
  }

  /// Upload study material file
  Future<String?> uploadStudyMaterial({
    required String agencyCode,
    required String sectionName,
    required File file,
    Function(double)? onProgress,
  }) async {
    try {
      final fileName = path.basename(file.path);
      final sanitizedSection = sectionName.toLowerCase().replaceAll(' ', '_');
      final filePath = '$agencyCode/$sanitizedSection/$fileName';

      _logger.i('📤 Uploading study material: $filePath');

      final response = await _client.storage
          .from('study-materials')
          .upload(filePath, file);

      if (response.isNotEmpty) {
        _logger.i('✅ Study material uploaded successfully: $filePath');
        return filePath; // Return path, not public URL (private bucket)
      }

      return null;
    } catch (e) {
      _logger.e('❌ Error uploading study material: $e');
      return null;
    }
  }

  /// Upload workout image
  Future<String?> uploadWorkoutImage({
    required File imageFile,
    String? customName,
    Function(double)? onProgress,
  }) async {
    try {
      final fileName = customName ?? '${DateTime.now().millisecondsSinceEpoch}.jpg';
      final filePath = 'workouts/$fileName';

      _logger.i('📤 Uploading workout image: $filePath');

      final response = await _client.storage
          .from('workout-images')
          .upload(filePath, imageFile);

      if (response.isNotEmpty) {
        final publicUrl = _client.storage
            .from('workout-images')
            .getPublicUrl(filePath);

        _logger.i('✅ Workout image uploaded successfully: $publicUrl');
        return publicUrl;
      }

      return null;
    } catch (e) {
      _logger.e('❌ Error uploading workout image: $e');
      return null;
    }
  }

  /// Upload post image
  Future<String?> uploadPostImage({
    required String userId,
    required File imageFile,
    Function(double)? onProgress,
  }) async {
    try {
      final fileName = '${DateTime.now().millisecondsSinceEpoch}.jpg';
      final filePath = '$userId/posts/$fileName';

      _logger.i('📤 Uploading post image: $filePath');

      final response = await _client.storage
          .from('post-images')
          .upload(filePath, imageFile);

      if (response.isNotEmpty) {
        // Generate signed URL for private access
        final signedUrl = await _client.storage
            .from('post-images')
            .createSignedUrl(filePath, 3600); // 1 hour expiry

        _logger.i('✅ Post image uploaded successfully');
        return signedUrl;
      }

      return null;
    } catch (e) {
      _logger.e('❌ Error uploading post image: $e');
      return null;
    }
  }

  /// Pick image from gallery or camera
  Future<File?> pickImage({
    ImageSource source = ImageSource.gallery,
    int imageQuality = 85,
    double? maxWidth,
    double? maxHeight,
  }) async {
    try {
      final XFile? pickedFile = await _imagePicker.pickImage(
        source: source,
        imageQuality: imageQuality,
        maxWidth: maxWidth,
        maxHeight: maxHeight,
      );

      if (pickedFile != null) {
        return File(pickedFile.path);
      }

      return null;
    } catch (e) {
      _logger.e('❌ Error picking image: $e');
      return null;
    }
  }

  /// Pick file using file picker
  Future<File?> pickFile({
    List<String>? allowedExtensions,
    FileType type = FileType.any,
  }) async {
    try {
      final result = await FilePicker.platform.pickFiles(
        type: type,
        allowedExtensions: allowedExtensions,
        allowMultiple: false,
      );

      if (result != null && result.files.isNotEmpty) {
        final file = result.files.first;
        if (file.path != null) {
          return File(file.path!);
        }
      }

      return null;
    } catch (e) {
      _logger.e('❌ Error picking file: $e');
      return null;
    }
  }

  /// Get signed URL for private file access
  Future<String?> getSignedUrl({
    required String bucket,
    required String filePath,
    int expiresIn = 3600, // 1 hour default
  }) async {
    try {
      final signedUrl = await _client.storage
          .from(bucket)
          .createSignedUrl(filePath, expiresIn);

      return signedUrl;
    } catch (e) {
      _logger.e('❌ Error creating signed URL: $e');
      return null;
    }
  }

  /// Delete file from storage
  Future<bool> deleteFile({
    required String bucket,
    required String filePath,
  }) async {
    try {
      await _client.storage
          .from(bucket)
          .remove([filePath]);

      _logger.i('✅ File deleted successfully: $filePath');
      return true;
    } catch (e) {
      _logger.e('❌ Error deleting file: $e');
      return false;
    }
  }

  /// Get file info
  Map<String, dynamic> getFileInfo(File file) {
    final fileName = path.basename(file.path);
    final fileExtension = path.extension(file.path);
    final mimeType = lookupMimeType(file.path);
    final fileSize = file.lengthSync();

    return {
      'name': fileName,
      'extension': fileExtension,
      'mimeType': mimeType,
      'size': fileSize,
      'sizeFormatted': _formatFileSize(fileSize),
    };
  }

  /// Format file size for display
  String _formatFileSize(int bytes) {
    if (bytes < 1024) return '$bytes B';
    if (bytes < 1024 * 1024) return '${(bytes / 1024).toStringAsFixed(1)} KB';
    if (bytes < 1024 * 1024 * 1024) return '${(bytes / (1024 * 1024)).toStringAsFixed(1)} MB';
    return '${(bytes / (1024 * 1024 * 1024)).toStringAsFixed(1)} GB';
  }

  /// Validate file type
  bool isValidFileType(File file, List<String> allowedTypes) {
    final mimeType = lookupMimeType(file.path);
    return mimeType != null && allowedTypes.contains(mimeType);
  }

  /// Validate file size
  bool isValidFileSize(File file, int maxSizeInBytes) {
    return file.lengthSync() <= maxSizeInBytes;
  }

  /// Compress image if needed
  Future<File?> compressImage(File imageFile, {int quality = 85}) async {
    try {
      // For now, return the original file
      // You can implement image compression using packages like flutter_image_compress
      return imageFile;
    } catch (e) {
      _logger.e('❌ Error compressing image: $e');
      return null;
    }
  }
}
