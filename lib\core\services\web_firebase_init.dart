import 'package:flutter/foundation.dart' show kIsWeb;

/// Initializes Supabase for web platform
class WebSupabaseInit {
  /// Initialize Supabase for web
  static Future<void> initialize() async {
    if (kIsWeb) {
      // Supabase is already initialized in the main.dart file
      // This is just a placeholder for web-specific initialization if needed
      print('Web platform detected');
    }
  }
}
