@echo off
echo Fixing flutter_paystack package...

set PAYSTACK_PATH=%LOCALAPPDATA%\Pub\Cache\git\flutter_paystack-a4a33c3dd0a12f46d655a2e63d11e9f20ba82d01

if exist "%PAYSTACK_PATH%" (
    echo Found flutter_paystack package at %PAYSTACK_PATH%
    
    echo Creating backup of original files...
    
    if not exist "%PAYSTACK_PATH%\lib\src\widgets\pin_widget.dart.bak" (
        copy "%PAYSTACK_PATH%\lib\src\widgets\pin_widget.dart" "%PAYSTACK_PATH%\lib\src\widgets\pin_widget.dart.bak"
    )
    
    if not exist "%PAYSTACK_PATH%\lib\src\widgets\checkout\checkout_widget.dart.bak" (
        copy "%PAYSTACK_PATH%\lib\src\widgets\checkout\checkout_widget.dart" "%PAYSTACK_PATH%\lib\src\widgets\checkout\checkout_widget.dart.bak"
    )
    
    if not exist "%PAYSTACK_PATH%\lib\src\widgets\checkout\bank_checkout.dart.bak" (
        copy "%PAYSTACK_PATH%\lib\src\widgets\checkout\bank_checkout.dart" "%PAYSTACK_PATH%\lib\src\widgets\checkout\bank_checkout.dart.bak"
    )
    
    if not exist "%PAYSTACK_PATH%\lib\src\widgets\custom_dialog.dart.bak" (
        copy "%PAYSTACK_PATH%\lib\src\widgets\custom_dialog.dart" "%PAYSTACK_PATH%\lib\src\widgets\custom_dialog.dart.bak"
    )
    
    if not exist "%PAYSTACK_PATH%\lib\src\widgets\sucessful_widget.dart.bak" (
        copy "%PAYSTACK_PATH%\lib\src\widgets\sucessful_widget.dart" "%PAYSTACK_PATH%\lib\src\widgets\sucessful_widget.dart.bak"
    )
    
    if not exist "%PAYSTACK_PATH%\lib\src\widgets\otp_widget.dart.bak" (
        copy "%PAYSTACK_PATH%\lib\src\widgets\otp_widget.dart" "%PAYSTACK_PATH%\lib\src\widgets\otp_widget.dart.bak"
    )
    
    if not exist "%PAYSTACK_PATH%\lib\src\widgets\buttons.dart.bak" (
        copy "%PAYSTACK_PATH%\lib\src\widgets\buttons.dart" "%PAYSTACK_PATH%\lib\src\widgets\buttons.dart.bak"
    )
    
    if not exist "%PAYSTACK_PATH%\lib\src\widgets\input\pin_field.dart.bak" (
        copy "%PAYSTACK_PATH%\lib\src\widgets\input\pin_field.dart" "%PAYSTACK_PATH%\lib\src\widgets\input\pin_field.dart.bak"
    )
    
    if not exist "%PAYSTACK_PATH%\lib\src\widgets\input\base_field.dart.bak" (
        copy "%PAYSTACK_PATH%\lib\src\widgets\input\base_field.dart" "%PAYSTACK_PATH%\lib\src\widgets\input\base_field.dart.bak"
    )
    
    echo Applying fixes...
    
    echo Fixing pin_widget.dart...
    powershell -Command "(Get-Content '%PAYSTACK_PATH%\lib\src\widgets\pin_widget.dart') -replace 'headline6', 'titleLarge' -replace 'accentColor', 'colorScheme.secondary' | Set-Content '%PAYSTACK_PATH%\lib\src\widgets\pin_widget.dart'"
    
    echo Fixing checkout_widget.dart...
    powershell -Command "(Get-Content '%PAYSTACK_PATH%\lib\src\widgets\checkout\checkout_widget.dart') -replace 'accentColor', 'colorScheme.secondary' -replace 'bodyText1', 'bodyLarge' -replace 'vsync: this,', '' | Set-Content '%PAYSTACK_PATH%\lib\src\widgets\checkout\checkout_widget.dart'"
    
    echo Fixing bank_checkout.dart...
    powershell -Command "(Get-Content '%PAYSTACK_PATH%\lib\src\widgets\checkout\bank_checkout.dart') -replace 'accentColor', 'colorScheme.secondary' | Set-Content '%PAYSTACK_PATH%\lib\src\widgets\checkout\bank_checkout.dart'"
    
    echo Fixing custom_dialog.dart...
    powershell -Command "(Get-Content '%PAYSTACK_PATH%\lib\src\widgets\custom_dialog.dart') -replace 'headline6', 'titleLarge' -replace 'subtitle1', 'titleMedium' | Set-Content '%PAYSTACK_PATH%\lib\src\widgets\custom_dialog.dart'"
    
    echo Fixing sucessful_widget.dart...
    powershell -Command "(Get-Content '%PAYSTACK_PATH%\lib\src\widgets\sucessful_widget.dart') -replace 'accentColor', 'colorScheme.secondary' | Set-Content '%PAYSTACK_PATH%\lib\src\widgets\sucessful_widget.dart'"
    
    echo Fixing otp_widget.dart...
    powershell -Command "(Get-Content '%PAYSTACK_PATH%\lib\src\widgets\otp_widget.dart') -replace 'accentColor', 'colorScheme.secondary' | Set-Content '%PAYSTACK_PATH%\lib\src\widgets\otp_widget.dart'"
    
    echo Fixing buttons.dart...
    powershell -Command "(Get-Content '%PAYSTACK_PATH%\lib\src\widgets\buttons.dart') -replace 'accentColor', 'colorScheme.secondary' -replace '.copyWith\(accentColor: Colors.white\)', '.copyWith\(colorScheme: ColorScheme.dark\(secondary: Colors.white\)\)' | Set-Content '%PAYSTACK_PATH%\lib\src\widgets\buttons.dart'"
    
    echo Fixing pin_field.dart...
    powershell -Command "(Get-Content '%PAYSTACK_PATH%\lib\src\widgets\input\pin_field.dart') -replace 'accentColor', 'colorScheme.secondary' | Set-Content '%PAYSTACK_PATH%\lib\src\widgets\input\pin_field.dart'"
    
    echo Fixing base_field.dart...
    powershell -Command "(Get-Content '%PAYSTACK_PATH%\lib\src\widgets\input\base_field.dart') -replace 'accentColor', 'colorScheme.secondary' | Set-Content '%PAYSTACK_PATH%\lib\src\widgets\input\base_field.dart'"
    
    echo Fixes applied successfully!
) else (
    echo Could not find flutter_paystack package at %PAYSTACK_PATH%
    echo Please run 'flutter pub get' first to download the package.
)

echo Done.
