import 'package:flutter/material.dart';
import 'package:fit_4_force/shared/extensions/context_extensions.dart';

/// This file contains patches for the flutter_paystack package to make it compatible
/// with the latest Flutter version.

/// A patched version of the PinWidget from flutter_paystack
class PatchedPinWidget extends StatelessWidget {
  final int count;

  const PatchedPinWidget({super.key, required this.count});

  @override
  Widget build(BuildContext context) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.center,
      children: List.generate(
        4, // Fixed pin length
        (i) => Container(
          margin: const EdgeInsets.symmetric(horizontal: 5.0),
          height: 20.0,
          width: 20.0,
          decoration: BoxDecoration(
            color: i < count ? context.colorScheme().primary : null,
            border: Border.all(
              color: context.textTheme().titleMedium?.color ?? Colors.black,
            ),
            borderRadius: BorderRadius.circular(10.0),
          ),
        ),
      ),
    );
  }
}

/// Apply the patches to the flutter_paystack package
void applyPaystackPatches() {
  // This function is called in the main.dart file to apply patches to the flutter_paystack package

  // Important: Instead of using the flutter_paystack package directly, we should use our
  // custom PaystackWrapper class from lib/shared/patches/paystack_wrapper.dart

  // The wrapper provides compatible implementations of the Flutter Paystack widgets
  // and functions, avoiding the compatibility issues with the latest Flutter version

  debugPrint('Paystack patches applied');
}
