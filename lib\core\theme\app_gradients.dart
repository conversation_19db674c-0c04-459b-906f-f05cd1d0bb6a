import 'package:flutter/material.dart';
import 'package:fit_4_force/core/theme/app_theme.dart';

/// A utility class for consistent gradient styles
class AppGradients {
  // Prevent instantiation
  AppGradients._();

  /// Primary gradient - blue to purple
  static LinearGradient get primary => LinearGradient(
    colors: [AppTheme.primaryColor, AppTheme.secondaryColor],
    begin: Alignment.topLeft,
    end: Alignment.bottomRight,
  );

  /// Secondary gradient - purple to indigo
  static LinearGradient get secondary => LinearGradient(
    colors: [AppTheme.secondaryColor, AppTheme.accentColor],
    begin: Alignment.topLeft,
    end: Alignment.bottomRight,
  );

  /// Accent gradient - indigo to blue
  static LinearGradient get accent => LinearGradient(
    colors: [AppTheme.accentColor, AppTheme.primaryColor],
    begin: Alignment.topLeft,
    end: Alignment.bottomRight,
  );

  /// Success gradient - green to teal
  static LinearGradient get success => LinearGradient(
    colors: [
      AppTheme.successColor,
      const Color(0xFF009688), // Teal
    ],
    begin: Alignment.topLeft,
    end: Alignment.bottomRight,
  );

  /// Error gradient - red to orange
  static LinearGradient get error => LinearGradient(
    colors: [
      AppTheme.errorColor,
      const Color(0xFFFF9800), // Orange
    ],
    begin: Alignment.topLeft,
    end: Alignment.bottomRight,
  );

  /// Warning gradient - amber to orange
  static LinearGradient get warning => LinearGradient(
    colors: [
      AppTheme.warningColor,
      const Color(0xFFFF9800), // Orange
    ],
    begin: Alignment.topLeft,
    end: Alignment.bottomRight,
  );

  /// Info gradient - blue to light blue
  static LinearGradient get info => LinearGradient(
    colors: [
      AppTheme.infoColor,
      const Color(0xFF03A9F4), // Light Blue
    ],
    begin: Alignment.topLeft,
    end: Alignment.bottomRight,
  );

  /// Premium gradient - purple to gold
  static LinearGradient get premium => LinearGradient(
    colors: [
      AppTheme.premiumColor,
      const Color(0xFFFFD700), // Gold
    ],
    begin: Alignment.topLeft,
    end: Alignment.bottomRight,
  );

  /// Light gradient - white to light gray
  static LinearGradient get light => LinearGradient(
    colors: [
      Colors.white,
      const Color(0xFFF5F5F5), // Light Gray
    ],
    begin: Alignment.topLeft,
    end: Alignment.bottomRight,
  );

  /// Dark gradient - dark gray to black
  static LinearGradient get dark => LinearGradient(
    colors: [
      const Color(0xFF424242), // Dark Gray
      Colors.black,
    ],
    begin: Alignment.topLeft,
    end: Alignment.bottomRight,
  );

  /// Blue gradient - light blue to dark blue
  static LinearGradient get blue => LinearGradient(
    colors: [
      const Color(0xFF64B5F6), // Light Blue
      const Color(0xFF1565C0), // Dark Blue
    ],
    begin: Alignment.topLeft,
    end: Alignment.bottomRight,
  );

  /// Green gradient - light green to dark green
  static LinearGradient get green => LinearGradient(
    colors: [
      const Color(0xFF81C784), // Light Green
      const Color(0xFF2E7D32), // Dark Green
    ],
    begin: Alignment.topLeft,
    end: Alignment.bottomRight,
  );

  /// Purple gradient - light purple to dark purple
  static LinearGradient get purple => LinearGradient(
    colors: [
      const Color(0xFFBA68C8), // Light Purple
      const Color(0xFF6A1B9A), // Dark Purple
    ],
    begin: Alignment.topLeft,
    end: Alignment.bottomRight,
  );

  /// Orange gradient - light orange to dark orange
  static LinearGradient get orange => LinearGradient(
    colors: [
      const Color(0xFFFFB74D), // Light Orange
      const Color(0xFFE65100), // Dark Orange
    ],
    begin: Alignment.topLeft,
    end: Alignment.bottomRight,
  );

  /// Red gradient - light red to dark red
  static LinearGradient get red => LinearGradient(
    colors: [
      const Color(0xFFE57373), // Light Red
      const Color(0xFFB71C1C), // Dark Red
    ],
    begin: Alignment.topLeft,
    end: Alignment.bottomRight,
  );

  /// Custom gradient with opacity
  static LinearGradient custom({
    required List<Color> colors,
    AlignmentGeometry begin = Alignment.topLeft,
    AlignmentGeometry end = Alignment.bottomRight,
    double opacity = 1.0,
  }) {
    final adjustedColors =
        colors.map((color) {
          return color.withValues(alpha: (opacity * 255).round().toDouble());
        }).toList();

    return LinearGradient(colors: adjustedColors, begin: begin, end: end);
  }

  /// Get a gradient for a specific agency
  static LinearGradient forAgency(String agency) {
    final color = AppTheme.agencyColors[agency] ?? AppTheme.primaryColor;
    final lighterColor = Color.lerp(color, Colors.white, 0.3) ?? color;

    return LinearGradient(
      colors: [lighterColor, color],
      begin: Alignment.topLeft,
      end: Alignment.bottomRight,
    );
  }
}
