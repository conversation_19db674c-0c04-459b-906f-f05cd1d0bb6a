import 'package:flutter/material.dart';

/// Provider for Supabase services (mock implementation)
class SupabaseProvider extends InheritedWidget {
  /// Mock client instance (not used in this implementation)
  final dynamic client;

  /// Constructor
  const SupabaseProvider({
    super.key,
    required super.child,
    required this.client,
  });

  /// Get the SupabaseProvider instance
  static SupabaseProvider of(BuildContext context) {
    final SupabaseProvider? result =
        context.dependOnInheritedWidgetOfExactType<SupabaseProvider>();
    assert(result != null, 'No SupabaseProvider found in context');
    return result!;
  }

  @override
  bool updateShouldNotify(SupabaseProvider oldWidget) {
    return false; // The client instance doesn't change
  }
}
