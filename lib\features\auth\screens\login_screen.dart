import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:fit_4_force/core/theme/app_theme.dart';
import 'package:fit_4_force/core/utils/responsive_utils.dart';
import 'package:fit_4_force/core/widgets/responsive_widgets.dart';
import 'package:fit_4_force/features/auth/bloc/auth_bloc.dart';
import 'package:fit_4_force/shared/widgets/base_widget.dart';

class LoginScreen extends StatefulWidget {
  const LoginScreen({super.key});

  @override
  State<LoginScreen> createState() => _LoginScreenState();
}

class _LoginScreenState extends State<LoginScreen> {
  final _formKey = GlobalKey<FormState>();
  final _emailController = TextEditingController();
  final _passwordController = TextEditingController();
  bool _obscurePassword = true;
  bool _isLoading = false;

  @override
  void dispose() {
    _emailController.dispose();
    _passwordController.dispose();
    super.dispose();
  }

  void _handleLogin() {
    if (_formKey.currentState!.validate()) {
      setState(() {
        _isLoading = true;
      });

      context.read<AuthBloc>().add(
        SignInEvent(
          email: _emailController.text.trim(),
          password: _passwordController.text,
        ),
      );

      // Reset loading state after a delay
      Future.delayed(const Duration(seconds: 2), () {
        if (mounted) {
          setState(() {
            _isLoading = false;
          });
        }
      });
    }
  }

  void _handleForgotPassword() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('Password reset instructions sent to your email'),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: BlocConsumer<AuthBloc, AuthState>(
        listener: (context, state) {
          if (state is AuthError) {
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(content: Text(state.message)),
            );
          } else if (state is Authenticated) {
            Navigator.of(context).pushReplacementNamed('/home');
          }
        },
        builder: (context, state) {
          final isDesktop = ResponsiveUtils.isDesktop(context);
          final padding = ResponsiveUtils.getResponsivePadding(context);
          final spacing = ResponsiveUtils.getResponsiveSpacing(context);

          return SafeArea(
            child: Center(
              child: SingleChildScrollView(
                padding: padding,
                child: ConstrainedBox(
                  constraints: BoxConstraints(
                    maxWidth: isDesktop ? 400 : double.infinity,
                  ),
                  child: Form(
                    key: _formKey,
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      crossAxisAlignment: CrossAxisAlignment.stretch,
                      children: [
                        // Logo and Title
                        Icon(
                          Icons.fitness_center,
                          size: ResponsiveUtils.getResponsiveFontSize(
                            context,
                            mobile: 60,
                            tablet: 70,
                            desktop: 80,
                          ),
                          color: AppTheme.primaryColor,
                        ),
                        SizedBox(height: spacing),
                        ResponsiveText(
                          'Welcome Back',
                          mobileFontSize: 24.0,
                          tabletFontSize: 28.0,
                          desktopFontSize: 32.0,
                          fontWeight: FontWeight.bold,
                          textAlign: TextAlign.center,
                        ),
                        SizedBox(height: spacing / 3),
                        ResponsiveText(
                          'Sign in to continue your fitness journey',
                          mobileFontSize: 14.0,
                          tabletFontSize: 16.0,
                          desktopFontSize: 18.0,
                          color: Colors.grey[600],
                          textAlign: TextAlign.center,
                        ),
                        SizedBox(height: spacing * 1.5),

                        // Email Field
                        BaseTextField(
                          label: 'Email',
                          hint: 'Enter your email',
                          controller: _emailController,
                          keyboardType: TextInputType.emailAddress,
                          prefix: const Icon(Icons.email_outlined),
                          validator: (value) {
                            if (value == null || value.isEmpty) {
                              return 'Please enter your email';
                            }
                            if (!value.contains('@')) {
                              return 'Please enter a valid email';
                            }
                            return null;
                          },
                        ),
                        SizedBox(height: spacing),

                        // Password Field
                        BaseTextField(
                          label: 'Password',
                          hint: 'Enter your password',
                          controller: _passwordController,
                          obscureText: _obscurePassword,
                          prefix: const Icon(Icons.lock_outline),
                          suffix: IconButton(
                            icon: Icon(
                              _obscurePassword
                                  ? Icons.visibility_off
                                  : Icons.visibility,
                            ),
                            onPressed: () {
                              setState(() {
                                _obscurePassword = !_obscurePassword;
                              });
                            },
                          ),
                          validator: (value) {
                            if (value == null || value.isEmpty) {
                              return 'Please enter your password';
                            }
                            if (value.length < 6) {
                              return 'Password must be at least 6 characters';
                            }
                            return null;
                          },
                        ),
                        SizedBox(height: spacing / 2),

                        // Forgot Password
                        Align(
                          alignment: Alignment.centerRight,
                          child: TextButton(
                            onPressed: _handleForgotPassword,
                            child: ResponsiveText(
                              'Forgot Password?',
                              mobileFontSize: 14.0,
                              tabletFontSize: 15.0,
                              desktopFontSize: 16.0,
                              color: AppTheme.primaryColor,
                            ),
                          ),
                        ),
                        SizedBox(height: spacing),

                        // Login Button
                        ResponsiveButton(
                          text: 'Sign In',
                          backgroundColor: AppTheme.primaryColor,
                          textColor: AppTheme.textOnPrimary,
                          onPressed: (_isLoading || state is AuthLoading) ? null : _handleLogin,
                          mobileHeight: 48.0,
                          tabletHeight: 52.0,
                          desktopHeight: 56.0,
                        ),
                        SizedBox(height: spacing),

                        // Sign Up Link
                        Row(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            ResponsiveText(
                              "Don't have an account?",
                              mobileFontSize: 14.0,
                              tabletFontSize: 15.0,
                              desktopFontSize: 16.0,
                            ),
                            TextButton(
                              onPressed: () {
                                Navigator.of(context).pushNamed('/signup');
                              },
                              child: ResponsiveText(
                                'Sign Up',
                                mobileFontSize: 14.0,
                                tabletFontSize: 15.0,
                                desktopFontSize: 16.0,
                                color: AppTheme.primaryColor,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                          ],
                        ),
                      ],
                    ),
                  ),
                ),
              ),
            ),
          );
        },
      ),
    );
  }
}
