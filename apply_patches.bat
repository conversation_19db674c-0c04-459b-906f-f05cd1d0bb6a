@echo off
echo Applying patches to flutter_paystack package...

set PAYSTACK_PATH=%LOCALAPPDATA%\Pub\Cache\hosted\pub.dev\flutter_paystack-1.0.7
set PATCH_PATH=%~dp0patches\flutter_paystack

if exist "%PAYSTACK_PATH%" (
    echo Found flutter_paystack package at %PAYSTACK_PATH%
    
    echo Backing up original file...
    if not exist "%PAYSTACK_PATH%\lib\src\widgets\pin_widget.dart.bak" (
        copy "%PAYSTACK_PATH%\lib\src\widgets\pin_widget.dart" "%PAYSTACK_PATH%\lib\src\widgets\pin_widget.dart.bak"
    )
    
    echo Applying patch...
    copy "%PATCH_PATH%\pin_widget.dart" "%PAYSTACK_PATH%\lib\src\widgets\pin_widget.dart"
    
    echo Patch applied successfully!
) else (
    echo Could not find flutter_paystack package at %PAYSTACK_PATH%
    echo Please run 'flutter pub get' first to download the package.
)

echo Done.
