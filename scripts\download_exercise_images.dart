import 'dart:io';
import 'package:http/http.dart' as http;
import 'package:path/path.dart' as path;

/// This script downloads exercise images from a public domain source
/// and saves them to the assets/images/exercises directory.
/// 
/// To run this script:
/// 1. Add http and path dependencies to pubspec.yaml
/// 2. Run: dart scripts/download_exercise_images.dart
///
/// Note: This is a simple script for demonstration purposes.
/// In a production app, you would want to use properly licensed images.

void main() async {
  // Create the directory if it doesn't exist
  final directory = Directory('assets/images/exercises');
  if (!await directory.exists()) {
    await directory.create(recursive: true);
  }

  // List of exercise images to download
  // These are placeholder URLs - replace with actual image URLs
  final exerciseImages = {
    'jumping_jacks.png': 'https://example.com/images/jumping_jacks.png',
    'incline_pushups.png': 'https://example.com/images/incline_pushups.png',
    'knee_pushups.png': 'https://example.com/images/knee_pushups.png',
    'pushups.png': 'https://example.com/images/pushups.png',
    'wide_pushups.png': 'https://example.com/images/wide_pushups.png',
  };

  // Download each image
  for (final entry in exerciseImages.entries) {
    final fileName = entry.key;
    final url = entry.value;
    final filePath = path.join(directory.path, fileName);
    
    try {
      print('Downloading $fileName...');
      final response = await http.get(Uri.parse(url));
      
      if (response.statusCode == 200) {
        final file = File(filePath);
        await file.writeAsBytes(response.bodyBytes);
        print('Downloaded $fileName successfully');
      } else {
        print('Failed to download $fileName: ${response.statusCode}');
      }
    } catch (e) {
      print('Error downloading $fileName: $e');
    }
  }

  print('\nDownload complete!');
  print('Place your exercise images in the assets/images/exercises directory.');
  print('Make sure to update pubspec.yaml to include these assets:');
  print('''
  flutter:
    assets:
      - assets/images/exercises/
  ''');
}
