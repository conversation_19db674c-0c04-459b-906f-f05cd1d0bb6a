import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:fit_4_force/core/config/app_config.dart';
import 'package:fit_4_force/core/config/app_routes.dart';
import 'package:fit_4_force/core/theme/app_theme.dart';
import 'package:fit_4_force/core/utils/responsive_utils.dart';
import 'package:fit_4_force/core/widgets/responsive_widgets.dart';
import 'package:fit_4_force/core/utils/legal_documents_helper.dart';
import 'package:fit_4_force/features/auth/bloc/auth_bloc.dart';
import 'package:package_info_plus/package_info_plus.dart';

class SettingsScreen extends StatefulWidget {
  const SettingsScreen({super.key});

  @override
  State<SettingsScreen> createState() => _SettingsScreenState();
}

class _SettingsScreenState extends State<SettingsScreen> {
  bool _notificationsEnabled = true;
  final bool _darkModeEnabled = false;
  String _appVersion = '';

  @override
  void initState() {
    super.initState();
    _loadAppInfo();
    _loadUserSettings();
  }

  Future<void> _loadAppInfo() async {
    try {
      final packageInfo = await PackageInfo.fromPlatform();
      setState(() {
        _appVersion = packageInfo.version;
      });
    } catch (e) {
      setState(() {
        _appVersion = AppConfig.appVersion;
      });
    }
  }

  void _loadUserSettings() {
    final authState = context.read<AuthBloc>().state;
    if (authState is Authenticated) {
      final user = authState.user;
      setState(() {
        _notificationsEnabled = user.notificationPreferences['general'] ?? true;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    final isDesktop = ResponsiveUtils.isDesktop(context);
    final padding = ResponsiveUtils.getResponsivePadding(context);

    return Scaffold(
      appBar: AppBar(title: const Text('Settings')),
      body: BlocBuilder<AuthBloc, AuthState>(
        builder: (context, state) {
          if (state is Authenticated) {
            final user = state.user;

            return SingleChildScrollView(
              padding: padding,
              child: Center(
                child: ConstrainedBox(
                  constraints: BoxConstraints(
                    maxWidth: isDesktop ? 600 : double.infinity,
                  ),
                  child: Column(
                    children: [
                      // Account settings
                      _buildSectionHeader('Account'),
                      ResponsiveCard(
                        child: Column(
                          children: [
                            ListTile(
                              leading: const Icon(Icons.person),
                              title: const Text('Edit Profile'),
                              onTap: () {
                                Navigator.of(
                                  context,
                                ).pushNamed(AppRoutes.profile);
                              },
                            ),
                            const Divider(),
                            ListTile(
                              leading: const Icon(Icons.email),
                              title: const Text('Change Email'),
                              onTap: () {
                                // Simplified - just show a message
                                ScaffoldMessenger.of(context).showSnackBar(
                                  const SnackBar(
                                    content: Text('Feature coming soon!'),
                                  ),
                                );
                              },
                            ),
                          ],
                        ),
                      ),

                      // Notification settings
                      _buildSectionHeader('Notifications'),
                      ResponsiveCard(
                        child: SwitchListTile(
                          title: const Text('Push Notifications'),
                          subtitle: const Text(
                            'Receive notifications for updates and reminders',
                          ),
                          value: _notificationsEnabled,
                          onChanged: (value) {
                            setState(() {
                              _notificationsEnabled = value;
                            });
                          },
                        ),
                      ),

                      // Subscription settings
                      _buildSectionHeader('Subscription'),
                      ResponsiveCard(
                        child: ListTile(
                          leading: Icon(
                            Icons.workspace_premium,
                            color:
                                user.isPremium ? AppTheme.premiumColor : null,
                          ),
                          title: Text(
                            user.isPremium
                                ? 'Manage Premium'
                                : 'Upgrade to Premium',
                            style: TextStyle(
                              color:
                                  user.isPremium
                                      ? AppTheme.premiumDarkColor
                                      : null,
                              fontWeight:
                                  user.isPremium ? FontWeight.bold : null,
                            ),
                          ),
                          subtitle: Text(
                            user.isPremium
                                ? 'Your subscription is active'
                                : 'Unlock all premium features',
                          ),
                          onTap: () {
                            Navigator.of(context).pushNamed(AppRoutes.premium);
                          },
                        ),
                      ),

                      // App information
                      _buildSectionHeader('About'),
                      ResponsiveCard(
                        child: Column(
                          children: [
                            ListTile(
                              leading: const Icon(Icons.info_outline),
                              title: const Text('App Version'),
                              subtitle: Text(_appVersion),
                            ),
                            const Divider(),
                            ListTile(
                              leading: const Icon(Icons.description_outlined),
                              title: const Text('Terms of Service'),
                              onTap: () async {
                                final success =
                                    await LegalDocumentsHelper.launchTermsOfService();
                                if (!success && mounted) {
                                  ScaffoldMessenger.of(context).showSnackBar(
                                    const SnackBar(
                                      content: Text(
                                        'Unable to open Terms of Service',
                                      ),
                                    ),
                                  );
                                }
                              },
                            ),
                            const Divider(),
                            ListTile(
                              leading: const Icon(Icons.privacy_tip_outlined),
                              title: const Text('Privacy Policy'),
                              onTap: () async {
                                final success =
                                    await LegalDocumentsHelper.launchPrivacyPolicy();
                                if (!success && mounted) {
                                  ScaffoldMessenger.of(context).showSnackBar(
                                    const SnackBar(
                                      content: Text(
                                        'Unable to open Privacy Policy',
                                      ),
                                    ),
                                  );
                                }
                              },
                            ),
                            const Divider(),
                            ListTile(
                              leading: const Icon(Icons.warning_outlined),
                              title: const Text('Disclaimer'),
                              onTap: () async {
                                final success =
                                    await LegalDocumentsHelper.launchDisclaimer();
                                if (!success && mounted) {
                                  ScaffoldMessenger.of(context).showSnackBar(
                                    const SnackBar(
                                      content: Text(
                                        'Unable to open Disclaimer',
                                      ),
                                    ),
                                  );
                                }
                              },
                            ),
                            const Divider(),
                            ListTile(
                              leading: const Icon(Icons.group_outlined),
                              title: const Text('Community Guidelines'),
                              onTap: () async {
                                final success =
                                    await LegalDocumentsHelper.launchCommunityPolicy();
                                if (!success && mounted) {
                                  ScaffoldMessenger.of(context).showSnackBar(
                                    const SnackBar(
                                      content: Text(
                                        'Unable to open Community Guidelines',
                                      ),
                                    ),
                                  );
                                }
                              },
                            ),
                            const Divider(),
                            ListTile(
                              leading: const Icon(Icons.money_off_outlined),
                              title: const Text('Refund Policy'),
                              onTap: () async {
                                final success =
                                    await LegalDocumentsHelper.launchRefundPolicy();
                                if (!success && mounted) {
                                  ScaffoldMessenger.of(context).showSnackBar(
                                    const SnackBar(
                                      content: Text(
                                        'Unable to open Refund Policy',
                                      ),
                                    ),
                                  );
                                }
                              },
                            ),
                          ],
                        ),
                      ),

                      const SizedBox(height: 24),
                      // Sign out button
                      ResponsiveButton(
                        text: 'Sign Out',
                        backgroundColor: Colors.red,
                        textColor: Colors.white,
                        onPressed: () {
                          context.read<AuthBloc>().add(SignOutEvent());
                        },
                        mobileHeight: 48.0,
                        tabletHeight: 52.0,
                        desktopHeight: 56.0,
                      ),
                      const SizedBox(height: 24),
                    ],
                  ),
                ),
              ),
            );
          }

          return const Center(child: CircularProgressIndicator());
        },
      ),
    );
  }

  Widget _buildSectionHeader(String title) {
    return Padding(
      padding: const EdgeInsets.fromLTRB(16, 24, 16, 8),
      child: Align(
        alignment: Alignment.centerLeft,
        child: ResponsiveText(
          title,
          mobileFontSize: 14.0,
          tabletFontSize: 15.0,
          desktopFontSize: 16.0,
          fontWeight: FontWeight.bold,
          color: Colors.grey[600],
        ),
      ),
    );
  }
}
