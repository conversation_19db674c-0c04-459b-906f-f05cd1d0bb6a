import 'package:fit_4_force/core/services/api_service.dart';

/// API service for quiz-related endpoints
class QuizApiService {
  /// Base API service
  final ApiService _apiService;

  /// Constructor
  QuizApiService({ApiService? apiService})
      : _apiService = apiService ?? ApiService();

  /// Get quizzes by agency
  Future<List<dynamic>> getQuizzesByAgency(
    String agency,
    String token, {
    int page = 1,
    int limit = 10,
  }) async {
    final response = await _apiService.get(
      '/quizzes',
      queryParams: {
        'agency': agency,
        'page': page,
        'limit': limit,
      },
      token: token,
    );

    return response['data'];
  }

  /// Get quiz by ID
  Future<Map<String, dynamic>> getQuizById(
    String quizId,
    String token,
  ) async {
    final response = await _apiService.get(
      '/quizzes/$quizId',
      token: token,
    );

    return response;
  }

  /// Submit quiz answers
  Future<Map<String, dynamic>> submitQuizAnswers(
    String quizId,
    List<Map<String, dynamic>> answers,
    String token,
  ) async {
    final response = await _apiService.post(
      '/quizzes/$quizId/submit',
      body: {
        'answers': answers,
      },
      token: token,
    );

    return response;
  }

  /// Get user quiz history
  Future<List<dynamic>> getUserQuizHistory(
    String userId,
    String token, {
    int page = 1,
    int limit = 10,
  }) async {
    final response = await _apiService.get(
      '/quizzes/history/$userId',
      queryParams: {
        'page': page,
        'limit': limit,
      },
      token: token,
    );

    return response['data'];
  }

  /// Get quiz result
  Future<Map<String, dynamic>> getQuizResult(
    String resultId,
    String token,
  ) async {
    final response = await _apiService.get(
      '/quizzes/results/$resultId',
      token: token,
    );

    return response;
  }

  /// Generate AI quiz
  Future<Map<String, dynamic>> generateAiQuiz(
    String agency,
    String difficulty,
    int questionCount,
    String token,
  ) async {
    final response = await _apiService.post(
      '/quizzes/generate',
      body: {
        'agency': agency,
        'difficulty': difficulty,
        'questionCount': questionCount,
      },
      token: token,
    );

    return response;
  }
}
