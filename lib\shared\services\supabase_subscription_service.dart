import 'package:fit_4_force/core/config/app_config.dart';
import 'package:fit_4_force/shared/models/subscription_model.dart';
import 'package:fit_4_force/shared/services/supabase_auth_service.dart';
import 'package:fit_4_force/shared/services/supabase_base_service.dart';
import 'package:flutter/material.dart';
import 'package:logger/logger.dart';
import 'package:uuid/uuid.dart';

/// Service for handling subscriptions with Supabase
class SupabaseSubscriptionService extends SupabaseBaseService {
  final Logger _logger = Logger();
  final SupabaseAuthService _authService;

  /// Constructor
  SupabaseSubscriptionService({required SupabaseAuthService authService})
    : _authService = authService;

  @override
  String get tableName => 'subscriptions';

  /// Get current user ID
  String? get currentUserId => _authService.currentUserId;

  /// Check if user is authenticated
  bool get isAuthenticated => _authService.isAuthenticated;

  /// Generate a unique ID for the subscription
  String generateId() {
    return const Uuid().v4();
  }

  /// Get current subscription
  Future<SubscriptionModel?> getCurrentSubscription() async {
    try {
      if (!isAuthenticated) return null;

      // Mock implementation
      // Create a mock subscription for testing
      final mockSubscription = SubscriptionModel(
        id: 'mock-subscription-id',
        createdAt: DateTime.now().subtract(const Duration(days: 5)),
        userId: currentUserId!,
        transactionReference: 'mock-transaction-ref',
        startDate: DateTime.now().subtract(const Duration(days: 5)),
        expiryDate: DateTime.now().add(const Duration(days: 25)),
        amount: 2500.0,
        isActive: true,
        paymentMethod: 'Paystack',
        autoRenew: false,
      );

      _logger.i('Mock subscription retrieved for user: ${currentUserId!}');
      return mockSubscription;
    } catch (e) {
      _logger.e('Error getting current subscription: $e');
      rethrow;
    }
  }

  /// Check if user has active subscription
  Future<bool> hasActiveSubscription() async {
    try {
      final subscription = await getCurrentSubscription();
      if (subscription == null) return false;

      // Check if subscription is expired
      final now = DateTime.now();
      return subscription.expiryDate.isAfter(now);
    } catch (e) {
      _logger.e('Error checking active subscription: $e');
      return false;
    }
  }

  /// Create a new subscription
  Future<SubscriptionModel> createSubscription({
    required String userId,
    required String transactionReference,
    required DateTime startDate,
    required DateTime expiryDate,
    required double amount,
  }) async {
    try {
      final subscription = SubscriptionModel(
        id: generateId(),
        createdAt: DateTime.now(),
        userId: userId,
        transactionReference: transactionReference,
        startDate: startDate,
        expiryDate: expiryDate,
        amount: amount,
        isActive: true,
        paymentMethod: 'Paystack',
        autoRenew: false,
      );

      await create(subscription.toJson());

      // Update user's premium status
      await _authService.updatePremiumStatus(userId, true, expiryDate);

      return subscription;
    } catch (e) {
      _logger.e('Error creating subscription: $e');
      rethrow;
    }
  }

  /// Process payment (mock implementation)
  Future<Map<String, dynamic>> processPayment({
    required BuildContext? context,
    required String email,
    required double amount,
    required String reference,
    required String fullName,
  }) async {
    try {
      // This is a mock implementation that always succeeds
      // In a real app, this would integrate with a payment gateway

      // Simulate network delay
      await Future.delayed(const Duration(seconds: 2));

      // Always return success for testing
      return {
        'success': true,
        'reference': reference,
        'message': 'Payment successful (mock)',
      };
    } catch (e) {
      _logger.e('Error processing payment: $e');
      return {'success': false, 'message': e.toString()};
    }
  }

  /// Renew subscription
  Future<SubscriptionModel> renewSubscription({
    required String transactionReference,
    required DateTime startDate,
    required DateTime expiryDate,
    required double amount,
  }) async {
    try {
      if (!isAuthenticated) {
        throw Exception('User not authenticated');
      }

      return await createSubscription(
        userId: currentUserId!,
        transactionReference: transactionReference,
        startDate: startDate,
        expiryDate: expiryDate,
        amount: amount,
      );
    } catch (e) {
      _logger.e('Error renewing subscription: $e');
      rethrow;
    }
  }

  /// Get user's active subscription
  Future<SubscriptionModel?> getUserActiveSubscription(String userId) async {
    try {
      // Mock implementation
      // Create a mock subscription for testing
      final mockSubscription = SubscriptionModel(
        id: 'mock-subscription-id',
        createdAt: DateTime.now().subtract(const Duration(days: 5)),
        userId: userId,
        transactionReference: 'mock-transaction-ref',
        startDate: DateTime.now().subtract(const Duration(days: 5)),
        expiryDate: DateTime.now().add(const Duration(days: 25)),
        amount: 2500.0,
        isActive: true,
        paymentMethod: 'Paystack',
        autoRenew: false,
      );

      _logger.i('Mock active subscription retrieved for user: $userId');
      return mockSubscription;
    } catch (e) {
      _logger.e('Error getting user active subscription: $e');
      rethrow;
    }
  }

  /// Cancel subscription
  Future<void> cancelSubscription(String subscriptionId) async {
    try {
      await update(subscriptionId, {'isActive': false});

      // Update user's premium status if authenticated
      if (isAuthenticated) {
        await _authService.updatePremiumStatus(currentUserId!, false, null);
      }
    } catch (e) {
      _logger.e('Error canceling subscription: $e');
      rethrow;
    }
  }

  /// Get subscription history for current user
  Future<List<SubscriptionModel>> getSubscriptionHistory() async {
    try {
      if (!isAuthenticated) {
        return [];
      }

      // Mock implementation
      // Create a list of mock subscriptions for testing
      final mockSubscriptions = [
        SubscriptionModel(
          id: 'mock-subscription-id-1',
          createdAt: DateTime.now().subtract(const Duration(days: 65)),
          userId: currentUserId!,
          transactionReference: 'mock-transaction-ref-1',
          startDate: DateTime.now().subtract(const Duration(days: 65)),
          expiryDate: DateTime.now().subtract(const Duration(days: 35)),
          amount: 2500.0,
          isActive: false,
          paymentMethod: 'Paystack',
          autoRenew: false,
        ),
        SubscriptionModel(
          id: 'mock-subscription-id-2',
          createdAt: DateTime.now().subtract(const Duration(days: 35)),
          userId: currentUserId!,
          transactionReference: 'mock-transaction-ref-2',
          startDate: DateTime.now().subtract(const Duration(days: 35)),
          expiryDate: DateTime.now().subtract(const Duration(days: 5)),
          amount: 2500.0,
          isActive: false,
          paymentMethod: 'Paystack',
          autoRenew: false,
        ),
        SubscriptionModel(
          id: 'mock-subscription-id-3',
          createdAt: DateTime.now().subtract(const Duration(days: 5)),
          userId: currentUserId!,
          transactionReference: 'mock-transaction-ref-3',
          startDate: DateTime.now().subtract(const Duration(days: 5)),
          expiryDate: DateTime.now().add(const Duration(days: 25)),
          amount: 2500.0,
          isActive: true,
          paymentMethod: 'Paystack',
          autoRenew: false,
        ),
      ];

      _logger.i(
        'Mock subscription history retrieved for user: ${currentUserId!}',
      );
      return mockSubscriptions;
    } catch (e) {
      _logger.e('Error getting subscription history: $e');
      rethrow;
    }
  }

  /// Get subscription history for specific user
  Future<List<SubscriptionModel>> getUserSubscriptionHistory(
    String userId,
  ) async {
    try {
      // Mock implementation
      // Create a list of mock subscriptions for testing
      final mockSubscriptions = [
        SubscriptionModel(
          id: 'mock-subscription-id-1',
          createdAt: DateTime.now().subtract(const Duration(days: 65)),
          userId: userId,
          transactionReference: 'mock-transaction-ref-1',
          startDate: DateTime.now().subtract(const Duration(days: 65)),
          expiryDate: DateTime.now().subtract(const Duration(days: 35)),
          amount: 2500.0,
          isActive: false,
          paymentMethod: 'Paystack',
          autoRenew: false,
        ),
        SubscriptionModel(
          id: 'mock-subscription-id-2',
          createdAt: DateTime.now().subtract(const Duration(days: 35)),
          userId: userId,
          transactionReference: 'mock-transaction-ref-2',
          startDate: DateTime.now().subtract(const Duration(days: 35)),
          expiryDate: DateTime.now().subtract(const Duration(days: 5)),
          amount: 2500.0,
          isActive: false,
          paymentMethod: 'Paystack',
          autoRenew: false,
        ),
        SubscriptionModel(
          id: 'mock-subscription-id-3',
          createdAt: DateTime.now().subtract(const Duration(days: 5)),
          userId: userId,
          transactionReference: 'mock-transaction-ref-3',
          startDate: DateTime.now().subtract(const Duration(days: 5)),
          expiryDate: DateTime.now().add(const Duration(days: 25)),
          amount: 2500.0,
          isActive: true,
          paymentMethod: 'Paystack',
          autoRenew: false,
        ),
      ];

      _logger.i('Mock subscription history retrieved for user: $userId');
      return mockSubscriptions;
    } catch (e) {
      _logger.e('Error getting user subscription history: $e');
      rethrow;
    }
  }

  /// Check if subscription is about to expire (within 3 days)
  Future<bool> isSubscriptionAboutToExpire() async {
    try {
      final subscription = await getCurrentSubscription();
      if (subscription == null) return false;

      final now = DateTime.now();
      final threeDaysFromNow = now.add(const Duration(days: 3));

      return subscription.expiryDate.isBefore(threeDaysFromNow) &&
          subscription.expiryDate.isAfter(now);
    } catch (e) {
      _logger.e('Error checking if subscription is about to expire: $e');
      return false;
    }
  }

  /// Check if subscription is expired
  Future<bool> isSubscriptionExpired(String userId) async {
    try {
      final subscription = await getUserActiveSubscription(userId);
      if (subscription == null) {
        return true;
      }

      return subscription.expiryDate.isBefore(DateTime.now());
    } catch (e) {
      _logger.e('Error checking if subscription is expired: $e');
      return true;
    }
  }

  /// Activate subscription
  Future<bool> activateSubscription({
    required String userId,
    required String paymentReference,
  }) async {
    try {
      final subscriptionEndDate = DateTime.now().add(const Duration(days: 30));

      // Update user's premium status
      await _authService.updatePremiumStatus(userId, true, subscriptionEndDate);

      // Create mock subscription record
      final subscription = SubscriptionModel(
        id: generateId(),
        createdAt: DateTime.now(),
        userId: userId,
        transactionReference: paymentReference,
        startDate: DateTime.now(),
        expiryDate: subscriptionEndDate,
        amount: AppConfig.premiumSubscriptionPrice,
        isActive: true,
        paymentMethod: 'Paystack',
        autoRenew: false,
      );

      // Save the subscription (mock implementation)
      await create(subscription.toJson());

      _logger.i('Mock subscription activated for user: $userId');
      return true;
    } catch (e) {
      _logger.e('Error activating subscription: $e');
      return false;
    }
  }
}
