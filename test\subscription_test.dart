import 'package:flutter_test/flutter_test.dart';
import 'package:fit_4_force/shared/models/subscription_model.dart';

void main() {
  group('SubscriptionModel Tests', () {
    test('should create a subscription model with correct values', () {
      final now = DateTime.now();
      final expiryDate = now.add(const Duration(days: 30));
      
      final subscription = SubscriptionModel(
        id: '123',
        createdAt: now,
        userId: 'user123',
        transactionReference: 'txn123',
        startDate: now,
        expiryDate: expiryDate,
        amount: 2500.0,
        isActive: true,
        paymentMethod: 'Paystack',
        autoRenew: false,
      );
      
      expect(subscription.id, '123');
      expect(subscription.createdAt, now);
      expect(subscription.userId, 'user123');
      expect(subscription.transactionReference, 'txn123');
      expect(subscription.startDate, now);
      expect(subscription.expiryDate, expiryDate);
      expect(subscription.amount, 2500.0);
      expect(subscription.isActive, true);
      expect(subscription.paymentMethod, 'Paystack');
      expect(subscription.autoRenew, false);
    });
    
    test('should convert to and from JSON correctly', () {
      final now = DateTime.now();
      final expiryDate = now.add(const Duration(days: 30));
      
      final subscription = SubscriptionModel(
        id: '123',
        createdAt: now,
        userId: 'user123',
        transactionReference: 'txn123',
        startDate: now,
        expiryDate: expiryDate,
        amount: 2500.0,
        isActive: true,
        paymentMethod: 'Paystack',
        autoRenew: false,
      );
      
      final json = subscription.toJson();
      final fromJson = SubscriptionModel.fromJson(json);
      
      expect(fromJson.id, subscription.id);
      expect(fromJson.createdAt.toIso8601String(), subscription.createdAt.toIso8601String());
      expect(fromJson.userId, subscription.userId);
      expect(fromJson.transactionReference, subscription.transactionReference);
      expect(fromJson.startDate.toIso8601String(), subscription.startDate.toIso8601String());
      expect(fromJson.expiryDate.toIso8601String(), subscription.expiryDate.toIso8601String());
      expect(fromJson.amount, subscription.amount);
      expect(fromJson.isActive, subscription.isActive);
      expect(fromJson.paymentMethod, subscription.paymentMethod);
      expect(fromJson.autoRenew, subscription.autoRenew);
    });
    
    test('copyWith should create a new instance with updated values', () {
      final now = DateTime.now();
      final expiryDate = now.add(const Duration(days: 30));
      
      final subscription = SubscriptionModel(
        id: '123',
        createdAt: now,
        userId: 'user123',
        transactionReference: 'txn123',
        startDate: now,
        expiryDate: expiryDate,
        amount: 2500.0,
        isActive: true,
        paymentMethod: 'Paystack',
        autoRenew: false,
      );
      
      final newExpiryDate = now.add(const Duration(days: 60));
      final updatedSubscription = subscription.copyWith(
        expiryDate: newExpiryDate,
        isActive: false,
      ) as SubscriptionModel;
      
      expect(updatedSubscription.id, subscription.id);
      expect(updatedSubscription.createdAt, subscription.createdAt);
      expect(updatedSubscription.userId, subscription.userId);
      expect(updatedSubscription.transactionReference, subscription.transactionReference);
      expect(updatedSubscription.startDate, subscription.startDate);
      expect(updatedSubscription.expiryDate, newExpiryDate);
      expect(updatedSubscription.amount, subscription.amount);
      expect(updatedSubscription.isActive, false);
      expect(updatedSubscription.paymentMethod, subscription.paymentMethod);
      expect(updatedSubscription.autoRenew, subscription.autoRenew);
    });
  });
}
