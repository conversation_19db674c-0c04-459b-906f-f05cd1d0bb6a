import 'package:flutter/material.dart';
import 'package:fit_4_force/features/fitness/models/workout_model.dart';

/// Model representing a fitness challenge
class ChallengeModel {
  final String id;
  final String name;
  final String description;
  final String imageUrl;
  final DateTime startDate;
  final DateTime endDate;
  final String difficulty; // e.g., "Beginner", "Intermediate", "Advanced"
  final List<WorkoutModel> workouts;
  final IconData icon;
  final Color color;
  final bool isPremium;
  final int participantsCount;
  final List<String> rewards; // e.g., "Badge", "Certificate", "Points"
  final String targetAgency; // e.g., "Nigerian Army", "Navy", etc.
  final List<String> tags;
  final Map<String, int> leaderboard; // userId -> score

  const ChallengeModel({
    required this.id,
    required this.name,
    required this.description,
    required this.imageUrl,
    required this.startDate,
    required this.endDate,
    required this.difficulty,
    required this.workouts,
    required this.icon,
    required this.color,
    this.isPremium = false,
    required this.participantsCount,
    required this.rewards,
    required this.targetAgency,
    required this.tags,
    required this.leaderboard,
  });

  // Create a copy of the challenge with modified properties
  ChallengeModel copyWith({
    String? id,
    String? name,
    String? description,
    String? imageUrl,
    DateTime? startDate,
    DateTime? endDate,
    String? difficulty,
    List<WorkoutModel>? workouts,
    IconData? icon,
    Color? color,
    bool? isPremium,
    int? participantsCount,
    List<String>? rewards,
    String? targetAgency,
    List<String>? tags,
    Map<String, int>? leaderboard,
  }) {
    return ChallengeModel(
      id: id ?? this.id,
      name: name ?? this.name,
      description: description ?? this.description,
      imageUrl: imageUrl ?? this.imageUrl,
      startDate: startDate ?? this.startDate,
      endDate: endDate ?? this.endDate,
      difficulty: difficulty ?? this.difficulty,
      workouts: workouts ?? this.workouts,
      icon: icon ?? this.icon,
      color: color ?? this.color,
      isPremium: isPremium ?? this.isPremium,
      participantsCount: participantsCount ?? this.participantsCount,
      rewards: rewards ?? this.rewards,
      targetAgency: targetAgency ?? this.targetAgency,
      tags: tags ?? this.tags,
      leaderboard: leaderboard ?? this.leaderboard,
    );
  }

  // Convert challenge to map
  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'name': name,
      'description': description,
      'imageUrl': imageUrl,
      'startDate': startDate.toIso8601String(),
      'endDate': endDate.toIso8601String(),
      'difficulty': difficulty,
      'workouts': workouts.map((w) => w.toMap()).toList(),
      'isPremium': isPremium,
      'participantsCount': participantsCount,
      'rewards': rewards,
      'targetAgency': targetAgency,
      'tags': tags,
      'leaderboard': leaderboard,
    };
  }

  // Create challenge from map
  factory ChallengeModel.fromMap(Map<String, dynamic> map) {
    return ChallengeModel(
      id: map['id'],
      name: map['name'],
      description: map['description'],
      imageUrl: map['imageUrl'],
      startDate: DateTime.parse(map['startDate']),
      endDate: DateTime.parse(map['endDate']),
      difficulty: map['difficulty'],
      workouts: List<WorkoutModel>.from(
        map['workouts']?.map((x) => WorkoutModel.fromMap(x)),
      ),
      icon: Icons.emoji_events, // Default icon
      color: Colors.amber, // Default color
      isPremium: map['isPremium'] ?? false,
      participantsCount: map['participantsCount'] ?? 0,
      rewards: List<String>.from(map['rewards'] ?? []),
      targetAgency: map['targetAgency'] ?? 'All',
      tags: List<String>.from(map['tags'] ?? []),
      leaderboard: Map<String, int>.from(map['leaderboard'] ?? {}),
    );
  }

  // Check if challenge is active
  bool get isActive {
    final now = DateTime.now();
    return now.isAfter(startDate) && now.isBefore(endDate);
  }

  // Check if challenge is upcoming
  bool get isUpcoming {
    final now = DateTime.now();
    return now.isBefore(startDate);
  }

  // Check if challenge is completed
  bool get isCompleted {
    final now = DateTime.now();
    return now.isAfter(endDate);
  }

  // Get days remaining
  int get daysRemaining {
    final now = DateTime.now();
    if (isCompleted) return 0;
    if (isUpcoming) {
      return startDate.difference(now).inDays;
    }
    return endDate.difference(now).inDays;
  }

  // Get challenge duration in days
  int get durationDays {
    return endDate.difference(startDate).inDays;
  }

  // Get formatted start date
  String get formattedStartDate {
    return '${startDate.day}/${startDate.month}/${startDate.year}';
  }

  // Get formatted end date
  String get formattedEndDate {
    return '${endDate.day}/${endDate.month}/${endDate.year}';
  }
}

/// Model representing a user's challenge progress
class ChallengeProgressModel {
  final String id;
  final String challengeId;
  final String userId;
  final DateTime joinedDate;
  final int completedWorkouts;
  final int totalWorkouts;
  final int score;
  final int rank;
  final bool isCompleted;
  final Map<String, bool> workoutCompletionStatus; // workoutId -> completed

  const ChallengeProgressModel({
    required this.id,
    required this.challengeId,
    required this.userId,
    required this.joinedDate,
    required this.completedWorkouts,
    required this.totalWorkouts,
    required this.score,
    required this.rank,
    required this.isCompleted,
    required this.workoutCompletionStatus,
  });

  // Create a copy of the challenge progress with modified properties
  ChallengeProgressModel copyWith({
    String? id,
    String? challengeId,
    String? userId,
    DateTime? joinedDate,
    int? completedWorkouts,
    int? totalWorkouts,
    int? score,
    int? rank,
    bool? isCompleted,
    Map<String, bool>? workoutCompletionStatus,
  }) {
    return ChallengeProgressModel(
      id: id ?? this.id,
      challengeId: challengeId ?? this.challengeId,
      userId: userId ?? this.userId,
      joinedDate: joinedDate ?? this.joinedDate,
      completedWorkouts: completedWorkouts ?? this.completedWorkouts,
      totalWorkouts: totalWorkouts ?? this.totalWorkouts,
      score: score ?? this.score,
      rank: rank ?? this.rank,
      isCompleted: isCompleted ?? this.isCompleted,
      workoutCompletionStatus: workoutCompletionStatus ?? this.workoutCompletionStatus,
    );
  }

  // Convert challenge progress to map
  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'challengeId': challengeId,
      'userId': userId,
      'joinedDate': joinedDate.toIso8601String(),
      'completedWorkouts': completedWorkouts,
      'totalWorkouts': totalWorkouts,
      'score': score,
      'rank': rank,
      'isCompleted': isCompleted,
      'workoutCompletionStatus': workoutCompletionStatus,
    };
  }

  // Create challenge progress from map
  factory ChallengeProgressModel.fromMap(Map<String, dynamic> map) {
    return ChallengeProgressModel(
      id: map['id'],
      challengeId: map['challengeId'],
      userId: map['userId'],
      joinedDate: DateTime.parse(map['joinedDate']),
      completedWorkouts: map['completedWorkouts'] ?? 0,
      totalWorkouts: map['totalWorkouts'] ?? 0,
      score: map['score'] ?? 0,
      rank: map['rank'] ?? 0,
      isCompleted: map['isCompleted'] ?? false,
      workoutCompletionStatus: Map<String, bool>.from(map['workoutCompletionStatus'] ?? {}),
    );
  }

  // Get completion percentage
  double get completionPercentage {
    if (totalWorkouts == 0) return 0.0;
    return (completedWorkouts / totalWorkouts) * 100;
  }
}
