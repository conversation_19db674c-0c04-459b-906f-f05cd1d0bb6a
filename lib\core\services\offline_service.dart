import 'dart:convert';
import 'dart:io';
import 'package:flutter/foundation.dart';
import 'package:path_provider/path_provider.dart';
import 'package:connectivity_plus/connectivity_plus.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:logger/logger.dart';

/// Service for handling offline functionality and data caching
class OfflineService {
  static final OfflineService _instance = OfflineService._internal();
  factory OfflineService() => _instance;
  OfflineService._internal();

  final Logger _logger = Logger();
  final Connectivity _connectivity = Connectivity();
  
  bool _isOnline = true;
  List<Map<String, dynamic>> _pendingActions = [];
  
  /// Initialize offline service
  Future<void> initialize() async {
    await _checkConnectivity();
    await _loadPendingActions();
    _setupConnectivityListener();
  }

  /// Check current connectivity status
  Future<void> _checkConnectivity() async {
    try {
      final connectivityResult = await _connectivity.checkConnectivity();
      _isOnline = connectivityResult.contains(ConnectivityResult.mobile) ||
                  connectivityResult.contains(ConnectivityResult.wifi) ||
                  connectivityResult.contains(ConnectivityResult.ethernet);
      
      _logger.i('🌐 Connectivity status: ${_isOnline ? "Online" : "Offline"}');
    } catch (e) {
      _logger.e('❌ Error checking connectivity: $e');
      _isOnline = false;
    }
  }

  /// Setup connectivity listener
  void _setupConnectivityListener() {
    _connectivity.onConnectivityChanged.listen((List<ConnectivityResult> results) {
      final wasOnline = _isOnline;
      _isOnline = results.contains(ConnectivityResult.mobile) ||
                  results.contains(ConnectivityResult.wifi) ||
                  results.contains(ConnectivityResult.ethernet);
      
      _logger.i('🌐 Connectivity changed: ${_isOnline ? "Online" : "Offline"}');
      
      // If we just came back online, sync pending actions
      if (!wasOnline && _isOnline) {
        _syncPendingActions();
      }
    });
  }

  /// Check if device is currently online
  bool get isOnline => _isOnline;

  /// Check if device is currently offline
  bool get isOffline => !_isOnline;

  /// Cache data locally
  Future<bool> cacheData(String key, Map<String, dynamic> data) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final jsonString = jsonEncode(data);
      await prefs.setString('cache_$key', jsonString);
      await prefs.setInt('cache_${key}_timestamp', DateTime.now().millisecondsSinceEpoch);
      
      _logger.d('💾 Cached data for key: $key');
      return true;
    } catch (e) {
      _logger.e('❌ Error caching data: $e');
      return false;
    }
  }

  /// Get cached data
  Future<Map<String, dynamic>?> getCachedData(String key, {Duration? maxAge}) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final jsonString = prefs.getString('cache_$key');
      final timestamp = prefs.getInt('cache_${key}_timestamp');
      
      if (jsonString == null || timestamp == null) {
        return null;
      }

      // Check if data is too old
      if (maxAge != null) {
        final cacheAge = DateTime.now().millisecondsSinceEpoch - timestamp;
        if (cacheAge > maxAge.inMilliseconds) {
          _logger.d('🗑️ Cache expired for key: $key');
          await clearCachedData(key);
          return null;
        }
      }

      final data = jsonDecode(jsonString) as Map<String, dynamic>;
      _logger.d('📖 Retrieved cached data for key: $key');
      return data;
    } catch (e) {
      _logger.e('❌ Error getting cached data: $e');
      return null;
    }
  }

  /// Clear cached data
  Future<bool> clearCachedData(String key) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.remove('cache_$key');
      await prefs.remove('cache_${key}_timestamp');
      
      _logger.d('🗑️ Cleared cached data for key: $key');
      return true;
    } catch (e) {
      _logger.e('❌ Error clearing cached data: $e');
      return false;
    }
  }

  /// Cache study material for offline access
  Future<bool> cacheStudyMaterial(String materialId, Map<String, dynamic> material) async {
    try {
      // Cache the material metadata
      await cacheData('study_material_$materialId', material);
      
      // If material has a file URL, cache the file
      if (material['file_url'] != null) {
        await _cacheFile(material['file_url'], 'study_material_file_$materialId');
      }
      
      return true;
    } catch (e) {
      _logger.e('❌ Error caching study material: $e');
      return false;
    }
  }

  /// Get cached study material
  Future<Map<String, dynamic>?> getCachedStudyMaterial(String materialId) async {
    return await getCachedData('study_material_$materialId', maxAge: Duration(days: 7));
  }

  /// Cache file locally
  Future<String?> _cacheFile(String url, String cacheKey) async {
    try {
      if (kIsWeb) {
        // Web doesn't support file caching
        return null;
      }

      final directory = await getApplicationDocumentsDirectory();
      final cacheDir = Directory('${directory.path}/cache');
      if (!await cacheDir.exists()) {
        await cacheDir.create(recursive: true);
      }

      final fileName = url.split('/').last;
      final filePath = '${cacheDir.path}/$cacheKey-$fileName';
      final file = File(filePath);

      // Check if file already exists
      if (await file.exists()) {
        return filePath;
      }

      // Download and cache file (this would need HTTP client implementation)
      // For now, just return the original URL
      _logger.d('📁 File caching not implemented for: $url');
      return url;
    } catch (e) {
      _logger.e('❌ Error caching file: $e');
      return null;
    }
  }

  /// Add action to pending queue for when back online
  Future<void> addPendingAction(String action, Map<String, dynamic> data) async {
    try {
      final pendingAction = {
        'action': action,
        'data': data,
        'timestamp': DateTime.now().toIso8601String(),
        'id': DateTime.now().millisecondsSinceEpoch.toString(),
      };

      _pendingActions.add(pendingAction);
      await _savePendingActions();
      
      _logger.d('📝 Added pending action: $action');
    } catch (e) {
      _logger.e('❌ Error adding pending action: $e');
    }
  }

  /// Save pending actions to local storage
  Future<void> _savePendingActions() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final jsonString = jsonEncode(_pendingActions);
      await prefs.setString('pending_actions', jsonString);
    } catch (e) {
      _logger.e('❌ Error saving pending actions: $e');
    }
  }

  /// Load pending actions from local storage
  Future<void> _loadPendingActions() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final jsonString = prefs.getString('pending_actions');
      
      if (jsonString != null) {
        final List<dynamic> actions = jsonDecode(jsonString);
        _pendingActions = actions.cast<Map<String, dynamic>>();
        _logger.d('📖 Loaded ${_pendingActions.length} pending actions');
      }
    } catch (e) {
      _logger.e('❌ Error loading pending actions: $e');
      _pendingActions = [];
    }
  }

  /// Sync pending actions when back online
  Future<void> _syncPendingActions() async {
    if (_pendingActions.isEmpty) return;

    _logger.i('🔄 Syncing ${_pendingActions.length} pending actions');

    final actionsToSync = List<Map<String, dynamic>>.from(_pendingActions);
    _pendingActions.clear();
    await _savePendingActions();

    for (final action in actionsToSync) {
      try {
        await _executePendingAction(action);
        _logger.d('✅ Synced action: ${action['action']}');
      } catch (e) {
        _logger.e('❌ Failed to sync action: ${action['action']}, error: $e');
        // Re-add failed action to queue
        _pendingActions.add(action);
      }
    }

    if (_pendingActions.isNotEmpty) {
      await _savePendingActions();
    }
  }

  /// Execute a pending action
  Future<void> _executePendingAction(Map<String, dynamic> action) async {
    final actionType = action['action'] as String;
    final data = action['data'] as Map<String, dynamic>;

    switch (actionType) {
      case 'update_progress':
        // Implement progress update sync
        break;
      case 'rate_material':
        // Implement rating sync
        break;
      case 'bookmark_material':
        // Implement bookmark sync
        break;
      default:
        _logger.w('⚠️ Unknown pending action type: $actionType');
    }
  }

  /// Get pending actions count
  int get pendingActionsCount => _pendingActions.length;

  /// Clear all cached data
  Future<void> clearAllCache() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final keys = prefs.getKeys().where((key) => key.startsWith('cache_')).toList();
      
      for (final key in keys) {
        await prefs.remove(key);
      }
      
      _logger.i('🗑️ Cleared all cached data (${keys.length} items)');
    } catch (e) {
      _logger.e('❌ Error clearing all cache: $e');
    }
  }

  /// Get cache size (approximate)
  Future<int> getCacheSize() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final keys = prefs.getKeys().where((key) => key.startsWith('cache_')).toList();
      
      int totalSize = 0;
      for (final key in keys) {
        final value = prefs.getString(key);
        if (value != null) {
          totalSize += value.length;
        }
      }
      
      return totalSize;
    } catch (e) {
      _logger.e('❌ Error calculating cache size: $e');
      return 0;
    }
  }
}
