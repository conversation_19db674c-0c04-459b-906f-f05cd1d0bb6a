// This file contains fixes for the flutter_paystack package
// It should be used as a reference for manually patching the package

// 1. Replace 'headline6' with 'titleLarge'
// 2. Replace 'subtitle1' with 'titleMedium'
// 3. Replace 'bodyText1' with 'bodyLarge'
// 4. <PERSON>lace 'accentColor' with 'colorScheme.secondary'
// 5. For AnimatedSize, remove the 'vsync' parameter

// Example fixes:

// From:
// style: Theme.of(context).textTheme.headline6!,
// To:
// style: Theme.of(context).textTheme.titleLarge!,

// From:
// color: Theme.of(context).accentColor,
// To:
// color: Theme.of(context).colorScheme.secondary,

// From:
// AnimatedSize(vsync: this, ...)
// To:
// AnimatedSize(...)
