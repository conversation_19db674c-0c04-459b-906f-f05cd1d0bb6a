import 'dart:async';
import 'package:flutter/material.dart';
import 'package:fit_4_force/core/theme/app_theme.dart';
import 'package:fit_4_force/core/utils/navigation_service.dart';
import 'package:fit_4_force/features/fitness/models/workout_model.dart';
import 'package:fit_4_force/features/fitness/services/workout_service.dart';

class WorkoutSessionScreen extends StatefulWidget {
  final String workoutId;

  const WorkoutSessionScreen({super.key, required this.workoutId});

  @override
  State<WorkoutSessionScreen> createState() => _WorkoutSessionScreenState();
}

class _WorkoutSessionScreenState extends State<WorkoutSessionScreen> {
  final WorkoutService _workoutService = WorkoutService();
  late WorkoutModel _workout;
  bool _isLoading = true;

  // Session state
  int _currentExerciseIndex = 0;
  bool _isResting = false;
  int _currentSet = 1;

  // Timers
  Timer? _exerciseTimer;
  Timer? _restTimer;
  int _exerciseTimeRemaining = 0;
  int _restTimeRemaining = 0;

  // Session stats
  int _totalTimeElapsed = 0;
  int _caloriesBurned = 0;
  Timer? _sessionTimer;

  @override
  void initState() {
    super.initState();
    _loadWorkoutData();
  }

  @override
  void dispose() {
    _exerciseTimer?.cancel();
    _restTimer?.cancel();
    _sessionTimer?.cancel();
    super.dispose();
  }

  Future<void> _loadWorkoutData() async {
    // In a real app, this would be an async call to a service
    final workout = _workoutService.getWorkoutById(widget.workoutId);

    if (workout != null) {
      setState(() {
        _workout = workout;
        _isLoading = false;
      });

      // Initialize the first exercise
      _startExercise();

      // Start session timer
      _startSessionTimer();
    } else {
      // Handle workout not found
      NavigationService().goBack();
    }
  }

  void _startSessionTimer() {
    _sessionTimer = Timer.periodic(const Duration(seconds: 1), (timer) {
      setState(() {
        _totalTimeElapsed++;
        // Simple calorie calculation (very approximate)
        if (_totalTimeElapsed % 10 == 0) {
          _caloriesBurned = (_totalTimeElapsed / 60 * 8).round();
        }
      });
    });
  }

  void _startExercise() {
    final exercise = _workout.exercises[_currentExerciseIndex];
    _exerciseTimeRemaining = exercise.duration;

    _exerciseTimer = Timer.periodic(const Duration(seconds: 1), (timer) {
      if (_exerciseTimeRemaining > 0) {
        setState(() {
          _exerciseTimeRemaining--;
        });
      } else {
        _exerciseTimer?.cancel();

        // Check if we need to rest or move to next set/exercise
        if (_currentSet < _workout.exercises[_currentExerciseIndex].sets) {
          _startRest();
        } else {
          _moveToNextExercise();
        }
      }
    });
  }

  void _startRest() {
    _isResting = true;
    _restTimeRemaining = _workout.exercises[_currentExerciseIndex].restTime;

    _restTimer = Timer.periodic(const Duration(seconds: 1), (timer) {
      if (_restTimeRemaining > 0) {
        setState(() {
          _restTimeRemaining--;
        });
      } else {
        _restTimer?.cancel();
        setState(() {
          _isResting = false;
          _currentSet++;
        });
        _startExercise();
      }
    });
  }

  void _moveToNextExercise() {
    if (_currentExerciseIndex < _workout.exercises.length - 1) {
      setState(() {
        _currentExerciseIndex++;
        _currentSet = 1;
      });
      _startExercise();
    } else {
      _completeWorkout();
    }
  }

  void _completeWorkout() {
    _exerciseTimer?.cancel();
    _restTimer?.cancel();
    _sessionTimer?.cancel();

    // Add workout to history
    final historyEntry = WorkoutHistoryModel(
      id: DateTime.now().millisecondsSinceEpoch.toString(),
      workoutId: _workout.id,
      workoutName: _workout.name,
      date: DateTime.now(),
      duration: _totalTimeElapsed ~/ 60,
      calories: _caloriesBurned,
      icon: _workout.icon,
      color: _workout.color,
    );

    _workoutService.addWorkoutToHistory(historyEntry);

    // Show completion dialog
    showDialog(
      context: context,
      barrierDismissible: false,
      builder:
          (context) => AlertDialog(
            title: const Text('Workout Completed!'),
            content: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                Icon(Icons.emoji_events, size: 64, color: Colors.amber),
                const SizedBox(height: 16),
                Text(
                  'Great job! You\'ve completed the ${_workout.name} workout.',
                  textAlign: TextAlign.center,
                ),
                const SizedBox(height: 24),
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceAround,
                  children: [
                    _buildCompletionStat(
                      Icons.timer_outlined,
                      '${_totalTimeElapsed ~/ 60}',
                      'Minutes',
                    ),
                    _buildCompletionStat(
                      Icons.local_fire_department_outlined,
                      '$_caloriesBurned',
                      'Calories',
                    ),
                  ],
                ),
              ],
            ),
            actions: [
              TextButton(
                onPressed: () {
                  Navigator.pop(context);
                  NavigationService().goBack();
                },
                child: const Text('CLOSE'),
              ),
              ElevatedButton(
                onPressed: () {
                  Navigator.pop(context);
                  NavigationService().navigateTo('/workout-history');
                },
                style: ElevatedButton.styleFrom(
                  backgroundColor: AppTheme.primaryColor,
                ),
                child: const Text('VIEW HISTORY'),
              ),
            ],
          ),
    );
  }

  void _pauseWorkout() {
    if (_isResting) {
      _restTimer?.cancel();
    } else {
      _exerciseTimer?.cancel();
    }
    _sessionTimer?.cancel();

    showDialog(
      context: context,
      barrierDismissible: false,
      builder:
          (context) => AlertDialog(
            title: const Text('Workout Paused'),
            content: const Text('Take a break, but don\'t give up!'),
            actions: [
              TextButton(
                onPressed: () {
                  Navigator.pop(context);
                  NavigationService().goBack();
                },
                child: const Text('QUIT'),
              ),
              ElevatedButton(
                onPressed: () {
                  Navigator.pop(context);
                  if (_isResting) {
                    _startRest();
                  } else {
                    _startExercise();
                  }
                  _startSessionTimer();
                },
                style: ElevatedButton.styleFrom(
                  backgroundColor: AppTheme.primaryColor,
                ),
                child: const Text('RESUME'),
              ),
            ],
          ),
    );
  }

  @override
  Widget build(BuildContext context) {
    if (_isLoading) {
      return Scaffold(
        appBar: AppBar(title: const Text('Workout')),
        body: const Center(child: CircularProgressIndicator()),
      );
    }

    final currentExercise = _workout.exercises[_currentExerciseIndex];

    return Scaffold(
      body: SafeArea(
        child: Column(
          children: [
            _buildHeader(),
            Expanded(
              child:
                  _isResting
                      ? _buildRestView(currentExercise)
                      : _buildExerciseView(currentExercise),
            ),
            _buildControls(),
          ],
        ),
      ),
    );
  }

  Widget _buildHeader() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: _workout.color.withAlpha(
          26,
        ), // 0.1 opacity is approximately alpha 26
      ),
      child: Row(
        children: [
          IconButton(
            icon: const Icon(Icons.close),
            onPressed: () {
              _pauseWorkout();
            },
          ),
          const SizedBox(width: 8),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  _workout.name,
                  style: const TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                Text(
                  'Exercise ${_currentExerciseIndex + 1}/${_workout.exercises.length}',
                  style: TextStyle(
                    fontSize: 14,
                    color: AppTheme.textSecondaryLight,
                  ),
                ),
              ],
            ),
          ),
          Column(
            crossAxisAlignment: CrossAxisAlignment.end,
            children: [
              Text(
                '${_totalTimeElapsed ~/ 60}:${(_totalTimeElapsed % 60).toString().padLeft(2, '0')}',
                style: const TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                ),
              ),
              Text(
                '$_caloriesBurned calories',
                style: TextStyle(
                  fontSize: 14,
                  color: AppTheme.textSecondaryLight,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildExerciseView(ExerciseModel exercise) {
    return Column(
      children: [
        const SizedBox(height: 24),
        Text(
          exercise.name,
          style: const TextStyle(fontSize: 24, fontWeight: FontWeight.bold),
          textAlign: TextAlign.center,
        ),
        const SizedBox(height: 8),
        Text(
          'Set $_currentSet of ${exercise.sets}',
          style: TextStyle(
            fontSize: 18,
            color: _workout.color,
            fontWeight: FontWeight.bold,
          ),
          textAlign: TextAlign.center,
        ),
        const SizedBox(height: 24),
        Container(
          width: 200,
          height: 200,
          decoration: BoxDecoration(
            color: _workout.color.withAlpha(
              26,
            ), // 0.1 opacity is approximately alpha 26
            shape: BoxShape.circle,
          ),
          child: Center(
            child: Text(
              _formatTime(_exerciseTimeRemaining),
              style: TextStyle(
                fontSize: 48,
                fontWeight: FontWeight.bold,
                color: _workout.color,
              ),
            ),
          ),
        ),
        const SizedBox(height: 24),
        Padding(
          padding: const EdgeInsets.symmetric(horizontal: 24),
          child: Text(
            exercise.description,
            style: TextStyle(fontSize: 16, color: AppTheme.textSecondaryLight),
            textAlign: TextAlign.center,
          ),
        ),
        const SizedBox(height: 24),
        Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            _buildExerciseStat(Icons.fitness_center, '${exercise.reps} reps'),
            const SizedBox(width: 24),
            _buildExerciseStat(
              Icons.timer_outlined,
              '${exercise.restTime}s rest',
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildRestView(ExerciseModel exercise) {
    return Column(
      children: [
        const SizedBox(height: 24),
        const Text(
          'REST',
          style: TextStyle(fontSize: 24, fontWeight: FontWeight.bold),
          textAlign: TextAlign.center,
        ),
        const SizedBox(height: 8),
        Text(
          'Next: Set ${_currentSet + 1} of ${exercise.sets}',
          style: TextStyle(fontSize: 18, color: AppTheme.textSecondaryLight),
          textAlign: TextAlign.center,
        ),
        const SizedBox(height: 24),
        Container(
          width: 200,
          height: 200,
          decoration: BoxDecoration(
            color: Colors.blue.withAlpha(
              26,
            ), // 0.1 opacity is approximately alpha 26
            shape: BoxShape.circle,
          ),
          child: Center(
            child: Text(
              _formatTime(_restTimeRemaining),
              style: const TextStyle(
                fontSize: 48,
                fontWeight: FontWeight.bold,
                color: Colors.blue,
              ),
            ),
          ),
        ),
        const SizedBox(height: 24),
        const Padding(
          padding: EdgeInsets.symmetric(horizontal: 24),
          child: Text(
            'Take a deep breath and prepare for the next set',
            style: TextStyle(fontSize: 16),
            textAlign: TextAlign.center,
          ),
        ),
        const SizedBox(height: 24),
        Text(
          'Next: ${exercise.name}',
          style: const TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
          textAlign: TextAlign.center,
        ),
      ],
    );
  }

  Widget _buildControls() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withAlpha(
              26,
            ), // 0.1 opacity is approximately alpha 26
            blurRadius: 10,
            offset: const Offset(0, -4),
          ),
        ],
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceAround,
        children: [
          ElevatedButton(
            onPressed: _previousExercise,
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.grey.shade200,
              foregroundColor: Colors.black,
              shape: const CircleBorder(),
              padding: const EdgeInsets.all(16),
            ),
            child: const Icon(Icons.skip_previous),
          ),
          ElevatedButton(
            onPressed: _pauseWorkout,
            style: ElevatedButton.styleFrom(
              backgroundColor: _workout.color,
              foregroundColor: Colors.white,
              shape: const CircleBorder(),
              padding: const EdgeInsets.all(24),
            ),
            child: const Icon(Icons.pause, size: 32),
          ),
          ElevatedButton(
            onPressed: _skipExercise,
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.grey.shade200,
              foregroundColor: Colors.black,
              shape: const CircleBorder(),
              padding: const EdgeInsets.all(16),
            ),
            child: const Icon(Icons.skip_next),
          ),
        ],
      ),
    );
  }

  Widget _buildExerciseStat(IconData icon, String text) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      decoration: BoxDecoration(
        color: Colors.grey.shade100,
        borderRadius: BorderRadius.circular(16),
      ),
      child: Row(
        children: [
          Icon(icon, size: 16, color: AppTheme.textSecondaryLight),
          const SizedBox(width: 8),
          Text(
            text,
            style: TextStyle(
              fontSize: 14,
              fontWeight: FontWeight.bold,
              color: AppTheme.textSecondaryLight,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildCompletionStat(IconData icon, String value, String label) {
    return Column(
      children: [
        Icon(icon, size: 32, color: _workout.color),
        const SizedBox(height: 8),
        Text(
          value,
          style: const TextStyle(fontSize: 24, fontWeight: FontWeight.bold),
        ),
        Text(
          label,
          style: TextStyle(fontSize: 14, color: AppTheme.textSecondaryLight),
        ),
      ],
    );
  }

  void _previousExercise() {
    if (_currentExerciseIndex > 0) {
      _exerciseTimer?.cancel();
      _restTimer?.cancel();
      setState(() {
        _currentExerciseIndex--;
        _currentSet = 1;
        _isResting = false;
      });
      _startExercise();
    }
  }

  void _skipExercise() {
    _exerciseTimer?.cancel();
    _restTimer?.cancel();
    _moveToNextExercise();
  }

  String _formatTime(int seconds) {
    final minutes = seconds ~/ 60;
    final remainingSeconds = seconds % 60;
    return '$minutes:${remainingSeconds.toString().padLeft(2, '0')}';
  }
}
