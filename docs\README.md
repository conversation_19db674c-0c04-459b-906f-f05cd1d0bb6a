# Fit4Force Legal Documents

This repository contains the legal documents for the Fit4Force mobile application, hosted on GitHub Pages.

## 📋 Available Documents

- **Terms of Service** - Terms and conditions for using the app
- **Privacy Policy** - How we handle user data and privacy
- **<PERSON>ie Policy** - Information about cookies and tracking
- **Disclaimer** - Important disclaimers and limitations
- **Contact Us** - How to reach our support team
- **Refund Policy** - Information about refunds and cancellations

## 🚀 GitHub Pages Setup Instructions

### Step 1: Enable GitHub Pages
1. Go to your repository settings
2. Scroll down to "Pages" section
3. Under "Source", select "Deploy from a branch"
4. Choose "main" branch and "/docs" folder
5. Click "Save"

### Step 2: Access Your Legal Pages
Once GitHub Pages is enabled, your legal documents will be available at:
```
https://[YOUR-GITHUB-USERNAME].github.io/[REPOSITORY-NAME]/
```

### Step 3: Update Placeholder Content
Replace the following placeholders in all HTML files:
- `[DATE]` - Effective dates for policies
- `[EMAIL]` - Your support email address
- `[COMPANY NAME]` - Your company name
- `[ADDRESS]` - Your business address
- `[SOCIAL MEDIA HANDLES]` - Your social media accounts

### Step 4: Customize for Your Needs
1. Review all legal documents with a legal professional
2. Update content to match your specific business practices
3. Add your company branding and colors
4. Test all links and functionality

## 📱 Integration with Flutter App

### Using URL Launcher
Add these URLs to your Flutter app using the `url_launcher` package:

```dart
// Example URLs (replace with your actual GitHub Pages URLs)
const String termsOfServiceUrl = 'https://yourusername.github.io/fit4force/terms-of-service.html';
const String privacyPolicyUrl = 'https://yourusername.github.io/fit4force/privacy-policy.html';
const String cookiePolicyUrl = 'https://yourusername.github.io/fit4force/cookie-policy.html';

// Launch URL function
Future<void> launchLegalDocument(String url) async {
  if (await canLaunchUrl(Uri.parse(url))) {
    await launchUrl(Uri.parse(url), mode: LaunchMode.externalApplication);
  } else {
    throw 'Could not launch $url';
  }
}
```

### Example Implementation
```dart
// In your settings screen or legal section
ListTile(
  title: Text('Terms of Service'),
  trailing: Icon(Icons.open_in_new),
  onTap: () => launchLegalDocument(termsOfServiceUrl),
),
ListTile(
  title: Text('Privacy Policy'),
  trailing: Icon(Icons.open_in_new),
  onTap: () => launchLegalDocument(privacyPolicyUrl),
),
```

## 🎨 Customization

### Colors and Branding
Update the CSS variables in `styles.css`:
```css
:root {
    --primary-color: #2563eb;  /* Your brand color */
    --primary-dark: #1d4ed8;   /* Darker shade */
    /* Add more custom colors */
}
```

### Adding New Pages
1. Create a new HTML file in the `/docs` folder
2. Follow the same structure as existing pages
3. Add a link to the new page in `index.html`
4. Update the navigation as needed

## 📝 Legal Compliance

### Important Notes
- These are template documents and should be reviewed by a legal professional
- Update all placeholder content with your actual information
- Ensure compliance with Nigerian and international laws
- Review and update documents regularly
- Keep records of when documents were updated

### Required Updates
Before going live, you must:
1. Replace all placeholder text
2. Have documents reviewed by a lawyer
3. Ensure accuracy of all information
4. Set proper effective dates
5. Test all links and functionality

## 🔄 Maintenance

### Regular Updates
- Review documents quarterly
- Update dates when changes are made
- Monitor legal requirements changes
- Keep backup copies of all versions

### Version Control
- Use Git to track changes to legal documents
- Tag important versions
- Maintain changelog for significant updates

## 📞 Support

If you need help with the legal documents or GitHub Pages setup:
1. Check GitHub Pages documentation
2. Review Flutter url_launcher package docs
3. Consult with legal professionals for content
4. Contact technical support for implementation issues

## 📄 License

These template documents are provided as-is for educational purposes. Please customize them for your specific needs and have them reviewed by qualified legal professionals before use.
