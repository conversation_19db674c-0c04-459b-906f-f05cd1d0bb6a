import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:fit_4_force/features/stats/models/user_stats.dart';
import 'package:fit_4_force/features/training/models/fitness_assessment.dart';
import 'package:fit_4_force/features/training/models/workout.dart';
import 'package:logger/logger.dart';
import 'package:uuid/uuid.dart';

class StatsService {
  final FirebaseFirestore _firestore = FirebaseFirestore.instance;
  final Logger _logger = Logger();
  final Uuid _uuid = const Uuid();
  
  // Collection references
  CollectionReference get _statsCollection => 
      _firestore.collection('user_stats');
      
  CollectionReference get _achievementsCollection => 
      _firestore.collection('achievements');
      
  CollectionReference get _userAchievementsCollection => 
      _firestore.collection('user_achievements');
      
  CollectionReference get _upcomingTestsCollection => 
      _firestore.collection('upcoming_tests');
      
  CollectionReference get _assessmentsCollection => 
      _firestore.collection('fitness_assessments');
      
  CollectionReference get _workoutLogsCollection => 
      _firestore.collection('workout_logs');
  
  // Get user stats
  Future<UserStats> getUserStats(String userId) async {
    try {
      final docSnapshot = await _statsCollection.doc(userId).get();
      
      if (!docSnapshot.exists) {
        // Create default stats if none exist
        final defaultStats = UserStats(
          userId: userId,
          fitnessScore: 0,
          totalWorkoutsCompleted: 0,
          streakDays: 0,
          lastWorkoutDate: DateTime.now(),
          progressMetrics: {},
          recentWorkouts: [],
        );
        
        await _statsCollection.doc(userId).set(defaultStats.toJson());
        return defaultStats;
      }
      
      return UserStats.fromFirestore(docSnapshot);
    } catch (e) {
      _logger.e('Error getting user stats: $e');
      
      // Return default stats if there's an error
      return UserStats(
        userId: userId,
        fitnessScore: 0,
        totalWorkoutsCompleted: 0,
        streakDays: 0,
        lastWorkoutDate: DateTime.now(),
        progressMetrics: {},
        recentWorkouts: [],
      );
    }
  }
  
  // Update user stats when a workout is completed
  Future<void> updateStatsForCompletedWorkout(
    String userId, 
    WorkoutDay workout,
  ) async {
    try {
      // Get current stats
      final currentStats = await getUserStats(userId);
      
      // Create workout log
      final workoutLog = WorkoutLog(
        id: _uuid.v4(),
        workoutId: workout.id,
        workoutTitle: workout.title,
        completedDate: DateTime.now(),
        durationMinutes: workout.estimatedDurationMinutes,
        focusArea: workout.focusArea,
        intensity: _calculateWorkoutIntensity(workout),
      );
      
      // Update stats
      final updatedWorkouts = [
        workoutLog,
        ...currentStats.recentWorkouts,
      ];
      
      // Keep only the 10 most recent workouts
      if (updatedWorkouts.length > 10) {
        updatedWorkouts.removeRange(10, updatedWorkouts.length);
      }
      
      // Calculate streak
      int streak = currentStats.streakDays;
      final now = DateTime.now();
      final lastWorkout = currentStats.lastWorkoutDate;
      
      if (now.difference(lastWorkout).inDays <= 1) {
        // If the last workout was today or yesterday, increment streak
        streak++;
      } else {
        // Reset streak
        streak = 1;
      }
      
      // Update stats document
      await _statsCollection.doc(userId).update({
        'totalWorkoutsCompleted': currentStats.totalWorkoutsCompleted + 1,
        'streakDays': streak,
        'lastWorkoutDate': Timestamp.fromDate(now),
        'recentWorkouts': updatedWorkouts.map((w) => w.toJson()).toList(),
      });
      
      // Save workout log
      await _workoutLogsCollection.add({
        'userId': userId,
        'workoutId': workout.id,
        'workoutTitle': workout.title,
        'completedDate': Timestamp.fromDate(now),
        'durationMinutes': workout.estimatedDurationMinutes,
        'focusArea': workout.focusArea,
        'intensity': _calculateWorkoutIntensity(workout),
      });
      
      // Check for achievements
      await _checkForAchievements(userId, currentStats.totalWorkoutsCompleted + 1, streak);
      
      // Update fitness score based on latest assessment
      await _updateFitnessScore(userId);
    } catch (e) {
      _logger.e('Error updating stats for completed workout: $e');
    }
  }
  
  // Calculate workout intensity (1-10 scale)
  double _calculateWorkoutIntensity(WorkoutDay workout) {
    // Base intensity on workout difficulty
    double baseIntensity;
    switch (workout.difficulty) {
      case 'Beginner':
        baseIntensity = 3.0;
        break;
      case 'Intermediate':
        baseIntensity = 6.0;
        break;
      case 'Advanced':
        baseIntensity = 9.0;
        break;
      default:
        baseIntensity = 5.0;
    }
    
    // Adjust based on number of exercises and duration
    final exerciseCount = workout.exercises.length;
    final duration = workout.estimatedDurationMinutes;
    
    double intensityAdjustment = 0;
    
    // More exercises = higher intensity
    if (exerciseCount > 8) {
      intensityAdjustment += 1.0;
    } else if (exerciseCount > 5) {
      intensityAdjustment += 0.5;
    }
    
    // Longer duration = higher intensity
    if (duration > 60) {
      intensityAdjustment += 1.0;
    } else if (duration > 45) {
      intensityAdjustment += 0.5;
    }
    
    // Cap at 10
    return (baseIntensity + intensityAdjustment).clamp(1.0, 10.0);
  }
  
  // Check for achievements based on completed workouts and streak
  Future<void> _checkForAchievements(
    String userId, 
    int totalWorkouts,
    int streak,
  ) async {
    try {
      // Check workout count achievements
      if (totalWorkouts == 1) {
        await _awardAchievement(
          userId, 
          'first_workout', 
          'First Workout', 
          'Completed your first workout!',
          'Consistency',
          10,
        );
      } else if (totalWorkouts == 5) {
        await _awardAchievement(
          userId, 
          'five_workouts', 
          'Getting Started', 
          'Completed 5 workouts',
          'Consistency',
          20,
        );
      } else if (totalWorkouts == 10) {
        await _awardAchievement(
          userId, 
          'ten_workouts', 
          'Dedicated', 
          'Completed 10 workouts',
          'Consistency',
          30,
        );
      } else if (totalWorkouts == 25) {
        await _awardAchievement(
          userId, 
          'twentyfive_workouts', 
          'Committed', 
          'Completed 25 workouts',
          'Consistency',
          50,
        );
      } else if (totalWorkouts == 50) {
        await _awardAchievement(
          userId, 
          'fifty_workouts', 
          'Fitness Enthusiast', 
          'Completed 50 workouts',
          'Consistency',
          100,
        );
      } else if (totalWorkouts == 100) {
        await _awardAchievement(
          userId, 
          'hundred_workouts', 
          'Fitness Master', 
          'Completed 100 workouts',
          'Consistency',
          200,
        );
      }
      
      // Check streak achievements
      if (streak == 3) {
        await _awardAchievement(
          userId, 
          'three_day_streak', 
          '3-Day Streak', 
          'Worked out for 3 days in a row',
          'Consistency',
          15,
        );
      } else if (streak == 7) {
        await _awardAchievement(
          userId, 
          'week_streak', 
          'Week Warrior', 
          'Worked out for 7 days in a row',
          'Consistency',
          30,
        );
      } else if (streak == 14) {
        await _awardAchievement(
          userId, 
          'two_week_streak', 
          'Two-Week Titan', 
          'Worked out for 14 days in a row',
          'Consistency',
          50,
        );
      } else if (streak == 30) {
        await _awardAchievement(
          userId, 
          'month_streak', 
          'Monthly Master', 
          'Worked out for 30 days in a row',
          'Consistency',
          100,
        );
      }
    } catch (e) {
      _logger.e('Error checking for achievements: $e');
    }
  }
  
  // Award an achievement to a user
  Future<void> _awardAchievement(
    String userId,
    String achievementId,
    String title,
    String description,
    String category,
    int points,
  ) async {
    try {
      // Check if user already has this achievement
      final querySnapshot = await _userAchievementsCollection
          .where('userId', isEqualTo: userId)
          .where('achievementId', isEqualTo: achievementId)
          .get();
          
      if (querySnapshot.docs.isNotEmpty) {
        // User already has this achievement
        return;
      }
      
      // Create the achievement if it doesn't exist
      final achievementDoc = await _achievementsCollection.doc(achievementId).get();
      
      if (!achievementDoc.exists) {
        await _achievementsCollection.doc(achievementId).set({
          'title': title,
          'description': description,
          'iconUrl': 'https://example.com/achievements/$achievementId.png', // Placeholder
          'category': category,
          'points': points,
        });
      }
      
      // Award the achievement to the user
      await _userAchievementsCollection.add({
        'userId': userId,
        'achievementId': achievementId,
        'earnedDate': Timestamp.fromDate(DateTime.now()),
      });
    } catch (e) {
      _logger.e('Error awarding achievement: $e');
    }
  }
  
  // Update fitness score based on latest assessment
  Future<void> _updateFitnessScore(String userId) async {
    try {
      // Get latest assessment
      final querySnapshot = await _assessmentsCollection
          .where('userId', isEqualTo: userId)
          .orderBy('assessmentDate', descending: true)
          .limit(1)
          .get();
          
      if (querySnapshot.docs.isEmpty) {
        return;
      }
      
      final assessment = FitnessAssessment.fromFirestore(querySnapshot.docs.first);
      final fitnessScore = assessment.calculateScore();
      
      // Update stats with new fitness score
      await _statsCollection.doc(userId).update({
        'fitnessScore': fitnessScore,
        'progressMetrics': {
          'pushUps': assessment.pushUps.toDouble(),
          'sitUps': assessment.sitUps.toDouble(),
          'runTime': assessment.runTime,
        },
      });
    } catch (e) {
      _logger.e('Error updating fitness score: $e');
    }
  }
  
  // Get user achievements
  Future<List<Achievement>> getUserAchievements(String userId) async {
    try {
      final querySnapshot = await _userAchievementsCollection
          .where('userId', isEqualTo: userId)
          .get();
          
      if (querySnapshot.docs.isEmpty) {
        return [];
      }
      
      final achievements = <Achievement>[];
      
      for (final doc in querySnapshot.docs) {
        final data = doc.data() as Map<String, dynamic>;
        final achievementId = data['achievementId'];
        final earnedDate = (data['earnedDate'] as Timestamp).toDate();
        
        // Get achievement details
        final achievementDoc = await _achievementsCollection.doc(achievementId).get();
        
        if (achievementDoc.exists) {
          final achievementData = achievementDoc.data() as Map<String, dynamic>;
          
          achievements.add(
            Achievement(
              id: achievementId,
              title: achievementData['title'],
              description: achievementData['description'],
              iconUrl: achievementData['iconUrl'],
              earnedDate: earnedDate,
              points: achievementData['points'],
              category: achievementData['category'],
            ),
          );
        }
      }
      
      // Sort by earned date (newest first)
      achievements.sort((a, b) => b.earnedDate.compareTo(a.earnedDate));
      
      return achievements;
    } catch (e) {
      _logger.e('Error getting user achievements: $e');
      return [];
    }
  }
  
  // Get recent workouts
  Future<List<WorkoutLog>> getRecentWorkouts(String userId, {int limit = 10}) async {
    try {
      final querySnapshot = await _workoutLogsCollection
          .where('userId', isEqualTo: userId)
          .orderBy('completedDate', descending: true)
          .limit(limit)
          .get();
          
      return querySnapshot.docs.map((doc) {
        final data = doc.data() as Map<String, dynamic>;
        
        return WorkoutLog(
          id: doc.id,
          workoutId: data['workoutId'],
          workoutTitle: data['workoutTitle'],
          completedDate: (data['completedDate'] as Timestamp).toDate(),
          durationMinutes: data['durationMinutes'],
          focusArea: data['focusArea'],
          intensity: data['intensity'],
        );
      }).toList();
    } catch (e) {
      _logger.e('Error getting recent workouts: $e');
      return [];
    }
  }
  
  // Get upcoming tests
  Future<List<UpcomingTest>> getUpcomingTests(String agency, {bool isPremium = false}) async {
    try {
      Query query = _upcomingTestsCollection
          .where('agency', isEqualTo: agency)
          .where('testDate', isGreaterThanOrEqualTo: Timestamp.fromDate(DateTime.now()))
          .orderBy('testDate');
          
      if (!isPremium) {
        // If not premium, only show non-premium tests
        query = query.where('isPremium', isEqualTo: false);
      }
      
      final querySnapshot = await query.get();
      
      return querySnapshot.docs
          .map((doc) => UpcomingTest.fromFirestore(doc))
          .toList();
    } catch (e) {
      _logger.e('Error getting upcoming tests: $e');
      return [];
    }
  }
  
  // Get fitness progress over time
  Future<Map<String, List<Map<String, dynamic>>>> getFitnessProgress(String userId) async {
    try {
      final querySnapshot = await _assessmentsCollection
          .where('userId', isEqualTo: userId)
          .orderBy('assessmentDate')
          .get();
          
      if (querySnapshot.docs.isEmpty) {
        return {
          'pushUps': [],
          'sitUps': [],
          'runTime': [],
        };
      }
      
      final pushUps = <Map<String, dynamic>>[];
      final sitUps = <Map<String, dynamic>>[];
      final runTime = <Map<String, dynamic>>[];
      
      for (final doc in querySnapshot.docs) {
        final assessment = FitnessAssessment.fromFirestore(doc);
        final date = assessment.assessmentDate;
        
        pushUps.add({
          'date': date.millisecondsSinceEpoch,
          'value': assessment.pushUps,
        });
        
        sitUps.add({
          'date': date.millisecondsSinceEpoch,
          'value': assessment.sitUps,
        });
        
        runTime.add({
          'date': date.millisecondsSinceEpoch,
          'value': assessment.runTime,
        });
      }
      
      return {
        'pushUps': pushUps,
        'sitUps': sitUps,
        'runTime': runTime,
      };
    } catch (e) {
      _logger.e('Error getting fitness progress: $e');
      return {
        'pushUps': [],
        'sitUps': [],
        'runTime': [],
      };
    }
  }
  
  // Get workout distribution by focus area
  Future<Map<String, int>> getWorkoutDistribution(String userId) async {
    try {
      final querySnapshot = await _workoutLogsCollection
          .where('userId', isEqualTo: userId)
          .get();
          
      final distribution = <String, int>{};
      
      for (final doc in querySnapshot.docs) {
        final data = doc.data() as Map<String, dynamic>;
        final focusArea = data['focusArea'];
        
        distribution[focusArea] = (distribution[focusArea] ?? 0) + 1;
      }
      
      return distribution;
    } catch (e) {
      _logger.e('Error getting workout distribution: $e');
      return {};
    }
  }
}
