import 'package:flutter/material.dart';
import 'package:fit_4_force/core/theme/app_theme.dart';
import 'package:fit_4_force/features/community/models/study_group_model.dart';
import 'package:fit_4_force/shared/models/user_model.dart';
import 'package:fit_4_force/shared/widgets/base_button.dart';
import 'package:intl/intl.dart';

class StudyGroupsScreen extends StatefulWidget {
  final UserModel user;

  const StudyGroupsScreen({super.key, required this.user});

  @override
  State<StudyGroupsScreen> createState() => _StudyGroupsScreenState();
}

class _StudyGroupsScreenState extends State<StudyGroupsScreen>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;
  final TextEditingController _searchController = TextEditingController();
  String _selectedAgency = 'All';
  final List<String> _agencies = [
    'All',
    'Nigerian Army',
    'Navy',
    'Air Force',
    'DSSC',
    'NDA',
    'NSCDC',
    'EFCC',
  ];

  // Mock data for study groups
  final List<StudyGroupModel> _studyGroups = [
    StudyGroupModel(
      id: '1',
      createdAt: DateTime.now().subtract(const Duration(days: 5)),
      name: 'Nigerian Army Aptitude Test Prep',
      description: 'A group dedicated to preparing for the Nigerian Army aptitude test. We meet twice a week to practice questions and share resources.',
      creatorId: 'user1',
      creatorName: 'John Doe',
      creatorProfileImageUrl: null,
      agency: 'Nigerian Army',
      topics: ['Aptitude Test', 'Mathematics', 'English', 'Current Affairs'],
      membersCount: 12,
      maxMembers: 20,
      isPrivate: false,
      nextMeetingTime: DateTime.now().add(const Duration(days: 2)),
    ),
    StudyGroupModel(
      id: '2',
      createdAt: DateTime.now().subtract(const Duration(days: 10)),
      name: 'DSSC Interview Preparation',
      description: 'Focused on preparing for DSSC interviews. We conduct mock interviews and provide feedback to each other.',
      creatorId: 'user2',
      creatorName: 'Jane Smith',
      creatorProfileImageUrl: null,
      agency: 'DSSC',
      topics: ['Interview', 'Leadership', 'Military Knowledge'],
      membersCount: 8,
      maxMembers: 15,
      isPrivate: true,
      nextMeetingTime: DateTime.now().add(const Duration(days: 1, hours: 3)),
    ),
    StudyGroupModel(
      id: '3',
      createdAt: DateTime.now().subtract(const Duration(days: 15)),
      name: 'Air Force Physical Training',
      description: 'Group for those preparing for the Air Force physical fitness test. We share workout routines and tips.',
      creatorId: 'user3',
      creatorName: 'Michael Johnson',
      creatorProfileImageUrl: null,
      agency: 'Air Force',
      topics: ['Physical Training', 'Fitness', 'Endurance'],
      membersCount: 18,
      maxMembers: 25,
      isPrivate: false,
      nextMeetingTime: DateTime.now().add(const Duration(days: 3, hours: 2)),
    ),
  ];

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 3, vsync: this);
  }

  @override
  void dispose() {
    _tabController.dispose();
    _searchController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text(
          'Study Groups',
          style: TextStyle(fontWeight: FontWeight.bold),
        ),
        actions: [
          IconButton(
            icon: const Icon(Icons.search),
            onPressed: () {
              // Show search dialog
              _showSearchDialog();
            },
          ),
          IconButton(
            icon: const Icon(Icons.filter_list),
            onPressed: () {
              // Show filter dialog
              _showFilterDialog();
            },
          ),
        ],
        bottom: TabBar(
          controller: _tabController,
          labelColor: AppTheme.primaryColor,
          unselectedLabelColor: AppTheme.textSecondaryLight,
          indicatorColor: AppTheme.primaryColor,
          indicatorWeight: 3,
          tabs: const [
            Tab(text: 'ALL GROUPS'),
            Tab(text: 'MY GROUPS'),
            Tab(text: 'RECOMMENDED'),
          ],
        ),
      ),
      body: TabBarView(
        controller: _tabController,
        children: [
          _buildAllGroupsTab(),
          _buildMyGroupsTab(),
          _buildRecommendedTab(),
        ],
      ),
      floatingActionButton: FloatingActionButton(
        backgroundColor: AppTheme.primaryColor,
        child: const Icon(Icons.add),
        onPressed: () {
          _showCreateGroupDialog();
        },
      ),
    );
  }

  Widget _buildAllGroupsTab() {
    return _studyGroups.isEmpty
        ? _buildEmptyState(
            'No study groups available',
            'Create a new study group to get started!',
          )
        : ListView.builder(
            padding: const EdgeInsets.all(16),
            itemCount: _studyGroups.length,
            itemBuilder: (context, index) {
              return _buildStudyGroupCard(_studyGroups[index]);
            },
          );
  }

  Widget _buildMyGroupsTab() {
    final myGroups = _studyGroups
        .where((group) => group.creatorId == widget.user.id)
        .toList();

    return myGroups.isEmpty
        ? _buildEmptyState(
            'You haven\'t created any study groups yet',
            'Create a new study group to collaborate with others!',
          )
        : ListView.builder(
            padding: const EdgeInsets.all(16),
            itemCount: myGroups.length,
            itemBuilder: (context, index) {
              return _buildStudyGroupCard(myGroups[index]);
            },
          );
  }

  Widget _buildRecommendedTab() {
    // In a real app, this would use an algorithm to recommend groups
    // For now, we'll just show groups matching the user's target agency
    final recommendedGroups = _studyGroups
        .where((group) => group.agency == widget.user.targetAgency)
        .toList();

    return recommendedGroups.isEmpty
        ? _buildEmptyState(
            'No recommended study groups',
            'We\'ll recommend groups based on your interests and agency.',
          )
        : ListView.builder(
            padding: const EdgeInsets.all(16),
            itemCount: recommendedGroups.length,
            itemBuilder: (context, index) {
              return _buildStudyGroupCard(recommendedGroups[index]);
            },
          );
  }

  Widget _buildEmptyState(String title, String subtitle) {
    return Center(
      child: Padding(
        padding: const EdgeInsets.all(24.0),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.group,
              size: 80,
              color: Colors.grey.withValues(alpha: 0.3 * 255),
            ),
            const SizedBox(height: 16),
            Text(
              title,
              style: const TextStyle(
                fontSize: 20,
                fontWeight: FontWeight.bold,
                color: Colors.black87,
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 8),
            Text(
              subtitle,
              style: TextStyle(
                fontSize: 16,
                color: Colors.grey.shade600,
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 24),
            BaseButton(
              text: 'Create Study Group',
              icon: Icons.add,
              backgroundColor: AppTheme.primaryColor,
              onPressed: () {
                _showCreateGroupDialog();
              },
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildStudyGroupCard(StudyGroupModel group) {
    return Card(
      margin: const EdgeInsets.only(bottom: 16),
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(16),
      ),
      child: InkWell(
        onTap: () => _navigateToGroupDetail(group),
        borderRadius: BorderRadius.circular(16),
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  // Group icon
                  Container(
                    padding: const EdgeInsets.all(12),
                    decoration: BoxDecoration(
                      color: _getAgencyColor(group.agency).withValues(alpha: 0.2 * 255),
                      shape: BoxShape.circle,
                    ),
                    child: Icon(
                      _getAgencyIcon(group.agency),
                      color: _getAgencyColor(group.agency),
                      size: 24,
                    ),
                  ),
                  const SizedBox(width: 12),
                  // Group name and agency
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          group.name,
                          style: const TextStyle(
                            fontSize: 18,
                            fontWeight: FontWeight.bold,
                            color: Colors.black87,
                          ),
                        ),
                        const SizedBox(height: 4),
                        Text(
                          group.agency,
                          style: TextStyle(
                            fontSize: 14,
                            color: Colors.grey.shade600,
                          ),
                        ),
                      ],
                    ),
                  ),
                  // Private indicator
                  if (group.isPrivate)
                    const Tooltip(
                      message: 'Private Group',
                      child: Icon(Icons.lock, color: Colors.grey, size: 18),
                    ),
                ],
              ),
              const SizedBox(height: 12),
              // Description
              Text(
                group.description,
                style: const TextStyle(
                  fontSize: 14,
                  color: Colors.black87,
                ),
                maxLines: 2,
                overflow: TextOverflow.ellipsis,
              ),
              const SizedBox(height: 12),
              // Topics
              Wrap(
                spacing: 8,
                runSpacing: 8,
                children: group.topics.map((topic) {
                  return Chip(
                    label: Text(
                      topic,
                      style: const TextStyle(fontSize: 12),
                    ),
                    backgroundColor: Colors.grey.withValues(alpha: 0.1 * 255),
                    padding: EdgeInsets.zero,
                    materialTapTargetSize: MaterialTapTargetSize.shrinkWrap,
                  );
                }).toList(),
              ),
              const SizedBox(height: 12),
              // Stats and next meeting
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  // Members count
                  Row(
                    children: [
                      const Icon(Icons.people, size: 16, color: Colors.grey),
                      const SizedBox(width: 4),
                      Text(
                        '${group.membersCount}/${group.maxMembers} members',
                        style: TextStyle(
                          fontSize: 12,
                          color: Colors.grey.shade600,
                        ),
                      ),
                    ],
                  ),
                  // Next meeting
                  if (group.nextMeetingTime != null)
                    Row(
                      children: [
                        const Icon(Icons.event, size: 16, color: Colors.grey),
                        const SizedBox(width: 4),
                        Text(
                          'Next: ${DateFormat('MMM d, h:mm a').format(group.nextMeetingTime!)}',
                          style: TextStyle(
                            fontSize: 12,
                            color: Colors.grey.shade600,
                          ),
                        ),
                      ],
                    ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  void _showSearchDialog() {
    showDialog(
      context: context,
      builder: (context) {
        return AlertDialog(
          title: const Text('Search Study Groups'),
          content: TextField(
            controller: _searchController,
            decoration: const InputDecoration(
              hintText: 'Enter keywords...',
              prefixIcon: Icon(Icons.search),
            ),
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.pop(context),
              child: const Text('CANCEL'),
            ),
            TextButton(
              onPressed: () {
                // Perform search
                Navigator.pop(context);
                // Update UI with search results
              },
              child: const Text('SEARCH'),
            ),
          ],
        );
      },
    );
  }

  void _showFilterDialog() {
    showDialog(
      context: context,
      builder: (context) {
        return StatefulBuilder(
          builder: (context, setState) {
            return AlertDialog(
              title: const Text('Filter Study Groups'),
              content: Column(
                mainAxisSize: MainAxisSize.min,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  const Text(
                    'Agency',
                    style: TextStyle(fontWeight: FontWeight.bold, fontSize: 16),
                  ),
                  const SizedBox(height: 8),
                  Wrap(
                    spacing: 8,
                    runSpacing: 8,
                    children: _agencies.map((agency) {
                      return ChoiceChip(
                        label: Text(agency),
                        selected: _selectedAgency == agency,
                        onSelected: (selected) {
                          setState(() {
                            _selectedAgency = agency;
                          });
                        },
                      );
                    }).toList(),
                  ),
                ],
              ),
              actions: [
                TextButton(
                  onPressed: () => Navigator.pop(context),
                  child: const Text('CANCEL'),
                ),
                TextButton(
                  onPressed: () {
                    // Apply filters
                    Navigator.pop(context);
                    // Update UI with filtered groups
                    this.setState(() {});
                  },
                  child: const Text('APPLY'),
                ),
              ],
            );
          },
        );
      },
    );
  }

  void _showCreateGroupDialog() {
    final nameController = TextEditingController();
    final descriptionController = TextEditingController();
    String selectedAgency = widget.user.targetAgency;
    final topicsController = TextEditingController();
    int maxMembers = 20;
    bool isPrivate = false;

    showDialog(
      context: context,
      builder: (context) {
        return StatefulBuilder(
          builder: (context, setState) {
            return AlertDialog(
              title: const Text('Create Study Group'),
              content: SingleChildScrollView(
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    TextField(
                      controller: nameController,
                      decoration: const InputDecoration(
                        labelText: 'Group Name',
                        hintText: 'Enter a name for your study group',
                      ),
                    ),
                    const SizedBox(height: 16),
                    TextField(
                      controller: descriptionController,
                      decoration: const InputDecoration(
                        labelText: 'Description',
                        hintText: 'Describe the purpose of your group',
                      ),
                      maxLines: 3,
                    ),
                    const SizedBox(height: 16),
                    DropdownButtonFormField<String>(
                      value: selectedAgency,
                      decoration: const InputDecoration(
                        labelText: 'Agency',
                      ),
                      items: _agencies.skip(1).map((agency) {
                        return DropdownMenuItem<String>(
                          value: agency,
                          child: Text(agency),
                        );
                      }).toList(),
                      onChanged: (value) {
                        if (value != null) {
                          setState(() {
                            selectedAgency = value;
                          });
                        }
                      },
                    ),
                    const SizedBox(height: 16),
                    TextField(
                      controller: topicsController,
                      decoration: const InputDecoration(
                        labelText: 'Topics (comma separated)',
                        hintText: 'e.g. Aptitude Test, Mathematics, English',
                      ),
                    ),
                    const SizedBox(height: 16),
                    Row(
                      children: [
                        const Text('Max Members:'),
                        Expanded(
                          child: Slider(
                            value: maxMembers.toDouble(),
                            min: 5,
                            max: 50,
                            divisions: 9,
                            label: maxMembers.toString(),
                            onChanged: (value) {
                              setState(() {
                                maxMembers = value.toInt();
                              });
                            },
                          ),
                        ),
                        Text(maxMembers.toString()),
                      ],
                    ),
                    const SizedBox(height: 8),
                    Row(
                      children: [
                        Checkbox(
                          value: isPrivate,
                          onChanged: (value) {
                            setState(() {
                              isPrivate = value ?? false;
                            });
                          },
                        ),
                        const Text('Private Group'),
                        const Tooltip(
                          message: 'Private groups require approval to join',
                          child: Icon(Icons.info_outline, size: 16),
                        ),
                      ],
                    ),
                  ],
                ),
              ),
              actions: [
                TextButton(
                  onPressed: () => Navigator.pop(context),
                  child: const Text('CANCEL'),
                ),
                TextButton(
                  onPressed: () {
                    // Create study group
                    if (nameController.text.isNotEmpty &&
                        descriptionController.text.isNotEmpty) {
                      // Parse topics
                      final topics = topicsController.text
                          .split(',')
                          .map((e) => e.trim())
                          .where((e) => e.isNotEmpty)
                          .toList();

                      // Add group to the list
                      setState(() {
                        _studyGroups.insert(
                          0,
                          StudyGroupModel(
                            id: DateTime.now().millisecondsSinceEpoch.toString(),
                            createdAt: DateTime.now(),
                            name: nameController.text,
                            description: descriptionController.text,
                            creatorId: widget.user.id,
                            creatorName: widget.user.fullName,
                            creatorProfileImageUrl: widget.user.profileImageUrl,
                            agency: selectedAgency,
                            topics: topics.isEmpty ? ['General'] : topics,
                            membersCount: 1, // Creator is the first member
                            maxMembers: maxMembers,
                            isPrivate: isPrivate,
                            nextMeetingTime: DateTime.now().add(const Duration(days: 7)),
                          ),
                        );
                      });
                      Navigator.pop(context);
                      ScaffoldMessenger.of(context).showSnackBar(
                        const SnackBar(content: Text('Study group created successfully!')),
                      );
                    }
                  },
                  child: const Text('CREATE'),
                ),
              ],
            );
          },
        );
      },
    );
  }

  void _navigateToGroupDetail(StudyGroupModel group) {
    // Navigate to group detail screen
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(content: Text('Viewing group: ${group.name}')),
    );
  }

  Color _getAgencyColor(String agency) {
    switch (agency) {
      case 'Nigerian Army':
        return Colors.green;
      case 'Navy':
        return Colors.blue;
      case 'Air Force':
        return Colors.indigo;
      case 'DSSC':
        return Colors.purple;
      case 'NDA':
        return Colors.red;
      case 'NSCDC':
        return Colors.orange;
      case 'EFCC':
        return Colors.teal;
      default:
        return Colors.grey;
    }
  }

  IconData _getAgencyIcon(String agency) {
    switch (agency) {
      case 'Nigerian Army':
        return Icons.military_tech;
      case 'Navy':
        return Icons.sailing;
      case 'Air Force':
        return Icons.airplanemode_active;
      case 'DSSC':
        return Icons.school;
      case 'NDA':
        return Icons.shield;
      case 'NSCDC':
        return Icons.security;
      case 'EFCC':
        return Icons.gavel;
      default:
        return Icons.group;
    }
  }
}
