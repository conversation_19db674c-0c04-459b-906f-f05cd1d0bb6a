import 'package:flutter/material.dart';

/// Model representing a nutrition plan
class NutritionPlanModel {
  final String id;
  final String name;
  final String description;
  final String imageUrl;
  final String category; // e.g., "Weight Loss", "Muscle Gain", "Maintenance"
  final String targetAgency; // e.g., "Nigerian Army", "Navy", etc.
  final List<MealModel> meals;
  final IconData icon;
  final Color color;
  final bool isPremium;
  final List<String> tags;
  final int calories;
  final Map<String, double> macros; // protein, carbs, fat in grams

  const NutritionPlanModel({
    required this.id,
    required this.name,
    required this.description,
    required this.imageUrl,
    required this.category,
    required this.targetAgency,
    required this.meals,
    required this.icon,
    required this.color,
    this.isPremium = false,
    required this.tags,
    required this.calories,
    required this.macros,
  });

  // Create a copy of the nutrition plan with modified properties
  NutritionPlanModel copyWith({
    String? id,
    String? name,
    String? description,
    String? imageUrl,
    String? category,
    String? targetAgency,
    List<MealModel>? meals,
    IconData? icon,
    Color? color,
    bool? isPremium,
    List<String>? tags,
    int? calories,
    Map<String, double>? macros,
  }) {
    return NutritionPlanModel(
      id: id ?? this.id,
      name: name ?? this.name,
      description: description ?? this.description,
      imageUrl: imageUrl ?? this.imageUrl,
      category: category ?? this.category,
      targetAgency: targetAgency ?? this.targetAgency,
      meals: meals ?? this.meals,
      icon: icon ?? this.icon,
      color: color ?? this.color,
      isPremium: isPremium ?? this.isPremium,
      tags: tags ?? this.tags,
      calories: calories ?? this.calories,
      macros: macros ?? this.macros,
    );
  }

  // Convert nutrition plan to map
  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'name': name,
      'description': description,
      'imageUrl': imageUrl,
      'category': category,
      'targetAgency': targetAgency,
      'meals': meals.map((m) => m.toMap()).toList(),
      'isPremium': isPremium,
      'tags': tags,
      'calories': calories,
      'macros': macros,
    };
  }

  // Create nutrition plan from map
  factory NutritionPlanModel.fromMap(Map<String, dynamic> map) {
    return NutritionPlanModel(
      id: map['id'],
      name: map['name'],
      description: map['description'],
      imageUrl: map['imageUrl'],
      category: map['category'],
      targetAgency: map['targetAgency'],
      meals: List<MealModel>.from(
        map['meals']?.map((x) => MealModel.fromMap(x)),
      ),
      icon: Icons.restaurant, // Default icon
      color: Colors.green, // Default color
      isPremium: map['isPremium'] ?? false,
      tags: List<String>.from(map['tags'] ?? []),
      calories: map['calories'] ?? 0,
      macros: Map<String, double>.from(map['macros'] ?? {}),
    );
  }
}

/// Model representing a meal
class MealModel {
  final String id;
  final String name;
  final String description;
  final String imageUrl;
  final String mealType; // e.g., "Breakfast", "Lunch", "Dinner", "Snack"
  final List<FoodItemModel> foodItems;
  final int calories;
  final Map<String, double> macros; // protein, carbs, fat in grams
  final int preparationTimeMinutes;
  final List<String> instructions;
  final bool isPremium;

  const MealModel({
    required this.id,
    required this.name,
    required this.description,
    required this.imageUrl,
    required this.mealType,
    required this.foodItems,
    required this.calories,
    required this.macros,
    required this.preparationTimeMinutes,
    required this.instructions,
    this.isPremium = false,
  });

  // Create a copy of the meal with modified properties
  MealModel copyWith({
    String? id,
    String? name,
    String? description,
    String? imageUrl,
    String? mealType,
    List<FoodItemModel>? foodItems,
    int? calories,
    Map<String, double>? macros,
    int? preparationTimeMinutes,
    List<String>? instructions,
    bool? isPremium,
  }) {
    return MealModel(
      id: id ?? this.id,
      name: name ?? this.name,
      description: description ?? this.description,
      imageUrl: imageUrl ?? this.imageUrl,
      mealType: mealType ?? this.mealType,
      foodItems: foodItems ?? this.foodItems,
      calories: calories ?? this.calories,
      macros: macros ?? this.macros,
      preparationTimeMinutes: preparationTimeMinutes ?? this.preparationTimeMinutes,
      instructions: instructions ?? this.instructions,
      isPremium: isPremium ?? this.isPremium,
    );
  }

  // Convert meal to map
  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'name': name,
      'description': description,
      'imageUrl': imageUrl,
      'mealType': mealType,
      'foodItems': foodItems.map((f) => f.toMap()).toList(),
      'calories': calories,
      'macros': macros,
      'preparationTimeMinutes': preparationTimeMinutes,
      'instructions': instructions,
      'isPremium': isPremium,
    };
  }

  // Create meal from map
  factory MealModel.fromMap(Map<String, dynamic> map) {
    return MealModel(
      id: map['id'],
      name: map['name'],
      description: map['description'],
      imageUrl: map['imageUrl'],
      mealType: map['mealType'],
      foodItems: List<FoodItemModel>.from(
        map['foodItems']?.map((x) => FoodItemModel.fromMap(x)),
      ),
      calories: map['calories'] ?? 0,
      macros: Map<String, double>.from(map['macros'] ?? {}),
      preparationTimeMinutes: map['preparationTimeMinutes'] ?? 0,
      instructions: List<String>.from(map['instructions'] ?? []),
      isPremium: map['isPremium'] ?? false,
    );
  }
}

/// Model representing a food item
class FoodItemModel {
  final String id;
  final String name;
  final String description;
  final String imageUrl;
  final String category; // e.g., "Protein", "Carbs", "Vegetables", "Fruits"
  final double quantity;
  final String unit; // e.g., "g", "ml", "cup", "tbsp"
  final int calories;
  final Map<String, double> macros; // protein, carbs, fat in grams
  final Map<String, double>? micronutrients; // vitamins, minerals, etc.

  const FoodItemModel({
    required this.id,
    required this.name,
    required this.description,
    required this.imageUrl,
    required this.category,
    required this.quantity,
    required this.unit,
    required this.calories,
    required this.macros,
    this.micronutrients,
  });

  // Create a copy of the food item with modified properties
  FoodItemModel copyWith({
    String? id,
    String? name,
    String? description,
    String? imageUrl,
    String? category,
    double? quantity,
    String? unit,
    int? calories,
    Map<String, double>? macros,
    Map<String, double>? micronutrients,
  }) {
    return FoodItemModel(
      id: id ?? this.id,
      name: name ?? this.name,
      description: description ?? this.description,
      imageUrl: imageUrl ?? this.imageUrl,
      category: category ?? this.category,
      quantity: quantity ?? this.quantity,
      unit: unit ?? this.unit,
      calories: calories ?? this.calories,
      macros: macros ?? this.macros,
      micronutrients: micronutrients ?? this.micronutrients,
    );
  }

  // Convert food item to map
  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'name': name,
      'description': description,
      'imageUrl': imageUrl,
      'category': category,
      'quantity': quantity,
      'unit': unit,
      'calories': calories,
      'macros': macros,
      'micronutrients': micronutrients,
    };
  }

  // Create food item from map
  factory FoodItemModel.fromMap(Map<String, dynamic> map) {
    return FoodItemModel(
      id: map['id'],
      name: map['name'],
      description: map['description'],
      imageUrl: map['imageUrl'],
      category: map['category'],
      quantity: map['quantity']?.toDouble() ?? 0.0,
      unit: map['unit'],
      calories: map['calories'] ?? 0,
      macros: Map<String, double>.from(map['macros'] ?? {}),
      micronutrients: map['micronutrients'] != null
          ? Map<String, double>.from(map['micronutrients'])
          : null,
    );
  }
}
