import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:equatable/equatable.dart';

abstract class BaseState extends Equatable {
  const BaseState();

  @override
  List<Object?> get props => [];
}

abstract class BaseEvent extends Equatable {
  const BaseEvent();

  @override
  List<Object?> get props => [];
}

abstract class BaseBloc<Event extends BaseEvent, State extends BaseState>
    extends Bloc<Event, State> {
  BaseBloc(super.initialState) {
    on<Event>((event, emit) async {
      try {
        await handleEvent(event, emit);
      } catch (error) {
        emit(ErrorState(error.toString()) as State);
      }
    });
  }

  Future<void> handleEvent(Event event, Emitter<State> emit);
}

class ErrorState extends BaseState {
  final String message;

  const ErrorState(this.message);

  @override
  List<Object?> get props => [message];
}

class LoadingState extends BaseState {}

class InitialState extends BaseState {} 