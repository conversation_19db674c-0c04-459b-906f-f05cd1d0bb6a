import 'package:flutter/material.dart';
import 'package:fit_4_force/core/config/supabase_config.dart';
import 'package:fit_4_force/core/config/environment_config.dart';
import 'package:logger/logger.dart';

/// Widget to test and display Supabase connection status
class SupabaseConnectionTest extends StatefulWidget {
  const SupabaseConnectionTest({super.key});

  @override
  State<SupabaseConnectionTest> createState() => _SupabaseConnectionTestState();
}

class _SupabaseConnectionTestState extends State<SupabaseConnectionTest> {
  final Logger _logger = Logger();
  bool _isConnected = false;
  bool _isTesting = false;
  String _connectionStatus = 'Not tested';
  Map<String, dynamic>? _connectionInfo;

  @override
  void initState() {
    super.initState();
    _testConnection();
  }

  Future<void> _testConnection() async {
    setState(() {
      _isTesting = true;
      _connectionStatus = 'Testing connection...';
    });

    try {
      _logger.i('🔍 Testing Supabase connection...');

      // Test 1: Check if Supabase is initialized
      if (!SupabaseConfig.isInitialized) {
        throw Exception('Supabase is not initialized');
      }

      // Test 2: Try to access the client
      final client = SupabaseConfig.client;
      
      // Test 3: Try a simple query to test database connection
      // We'll query the auth.users table metadata (this doesn't require authentication)
      final response = await client
          .from('information_schema.tables')
          .select('table_name')
          .eq('table_schema', 'auth')
          .eq('table_name', 'users')
          .limit(1);

      _logger.i('✅ Supabase connection test successful');
      _logger.i('📊 Response: $response');

      setState(() {
        _isConnected = true;
        _connectionStatus = 'Connected successfully';
        _connectionInfo = {
          'url': SupabaseConfig.supabaseUrl,
          'environment': EnvironmentConfig.currentEnvironment,
          'app_name': EnvironmentConfig.appName,
          'is_production': EnvironmentConfig.isProduction,
          'auth_user': SupabaseConfig.currentUser?.id ?? 'Not authenticated',
          'session_active': SupabaseConfig.currentSession != null,
          'test_query_result': response.isNotEmpty ? 'Success' : 'No data',
          'timestamp': DateTime.now().toIso8601String(),
        };
      });
    } catch (e) {
      _logger.e('❌ Supabase connection test failed: $e');

      setState(() {
        _isConnected = false;
        _connectionStatus = 'Connection failed: ${e.toString()}';
        _connectionInfo = {
          'url': SupabaseConfig.supabaseUrl,
          'environment': EnvironmentConfig.currentEnvironment,
          'error': e.toString(),
          'timestamp': DateTime.now().toIso8601String(),
        };
      });
    } finally {
      setState(() {
        _isTesting = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Card(
      margin: const EdgeInsets.all(16),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  _isConnected ? Icons.cloud_done : Icons.cloud_off,
                  color: _isConnected ? Colors.green : Colors.red,
                  size: 24,
                ),
                const SizedBox(width: 8),
                Text(
                  'Supabase Connection',
                  style: Theme.of(context).textTheme.titleLarge?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const Spacer(),
                if (_isTesting)
                  const SizedBox(
                    width: 20,
                    height: 20,
                    child: CircularProgressIndicator(strokeWidth: 2),
                  )
                else
                  IconButton(
                    icon: const Icon(Icons.refresh),
                    onPressed: _testConnection,
                    tooltip: 'Test connection again',
                  ),
              ],
            ),
            const SizedBox(height: 16),
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: _isConnected ? Colors.green.shade50 : Colors.red.shade50,
                borderRadius: BorderRadius.circular(8),
                border: Border.all(
                  color: _isConnected ? Colors.green : Colors.red,
                  width: 1,
                ),
              ),
              child: Row(
                children: [
                  Icon(
                    _isConnected ? Icons.check_circle : Icons.error,
                    color: _isConnected ? Colors.green : Colors.red,
                    size: 20,
                  ),
                  const SizedBox(width: 8),
                  Expanded(
                    child: Text(
                      _connectionStatus,
                      style: TextStyle(
                        color: _isConnected ? Colors.green.shade800 : Colors.red.shade800,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ),
                ],
              ),
            ),
            if (_connectionInfo != null) ...[
              const SizedBox(height: 16),
              Text(
                'Connection Details',
                style: Theme.of(context).textTheme.titleMedium?.copyWith(
                  fontWeight: FontWeight.bold,
                ),
              ),
              const SizedBox(height: 8),
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: Colors.grey.shade50,
                  borderRadius: BorderRadius.circular(8),
                  border: Border.all(color: Colors.grey.shade300),
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: _connectionInfo!.entries.map((entry) {
                    return Padding(
                      padding: const EdgeInsets.symmetric(vertical: 2),
                      child: Row(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          SizedBox(
                            width: 120,
                            child: Text(
                              '${entry.key}:',
                              style: const TextStyle(
                                fontWeight: FontWeight.w500,
                                color: Colors.grey,
                              ),
                            ),
                          ),
                          Expanded(
                            child: Text(
                              entry.value.toString(),
                              style: const TextStyle(
                                fontFamily: 'monospace',
                              ),
                            ),
                          ),
                        ],
                      ),
                    );
                  }).toList(),
                ),
              ),
            ],
            const SizedBox(height: 16),
            Row(
              children: [
                Expanded(
                  child: ElevatedButton.icon(
                    onPressed: _isTesting ? null : _testConnection,
                    icon: const Icon(Icons.wifi_find),
                    label: const Text('Test Connection'),
                  ),
                ),
                const SizedBox(width: 8),
                Expanded(
                  child: OutlinedButton.icon(
                    onPressed: () {
                      _showConnectionHelp(context);
                    },
                    icon: const Icon(Icons.help_outline),
                    label: const Text('Help'),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  void _showConnectionHelp(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Supabase Connection Help'),
        content: const SingleChildScrollView(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisSize: MainAxisSize.min,
            children: [
              Text(
                'Connection Status Meanings:',
                style: TextStyle(fontWeight: FontWeight.bold),
              ),
              SizedBox(height: 8),
              Text('✅ Connected: Supabase is working correctly'),
              Text('❌ Failed: Check your internet connection or Supabase configuration'),
              SizedBox(height: 16),
              Text(
                'Troubleshooting:',
                style: TextStyle(fontWeight: FontWeight.bold),
              ),
              SizedBox(height: 8),
              Text('1. Check your internet connection'),
              Text('2. Verify Supabase project URL and API key'),
              Text('3. Ensure Supabase project is active'),
              Text('4. Check if RLS policies allow access'),
            ],
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('OK'),
          ),
        ],
      ),
    );
  }
}
