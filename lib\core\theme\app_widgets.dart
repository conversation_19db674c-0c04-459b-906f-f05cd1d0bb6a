import 'package:flutter/material.dart';
import 'package:fit_4_force/core/theme/app_theme.dart';
import 'package:fit_4_force/core/theme/app_ui.dart';
import 'package:fit_4_force/core/theme/app_text.dart';

/// A utility class for consistent widget components
class AppWidgets {
  // Prevent instantiation
  AppWidgets._();
  
  /// Standard card widget
  static Widget card({
    required BuildContext context,
    required Widget child,
    Color? backgroundColor,
    BorderRadius? borderRadius,
    EdgeInsetsGeometry? padding,
    List<BoxShadow>? boxShadow,
    Border? border,
    Gradient? gradient,
    VoidCallback? onTap,
  }) {
    final decoration = AppUI.cardDecoration(
      color: gradient != null ? null : (backgroundColor ?? Colors.white),
      borderRadius: borderRadius ?? AppUI.borderRadiusXLarge,
      boxShadow: boxShadow ?? AppUI.shadowMedium,
      border: border,
      gradient: gradient,
    );
    
    final content = Padding(
      padding: padding ?? AppUI.paddingMedium,
      child: child,
    );
    
    if (onTap != null) {
      return Material(
        color: Colors.transparent,
        child: InkWell(
          onTap: onTap,
          borderRadius: borderRadius ?? AppUI.borderRadiusXLarge,
          child: Ink(
            decoration: decoration,
            child: content,
          ),
        ),
      );
    }
    
    return Container(
      decoration: decoration,
      child: content,
    );
  }
  
  /// Standard gradient card widget
  static Widget gradientCard({
    required BuildContext context,
    required Widget child,
    required List<Color> colors,
    AlignmentGeometry begin = Alignment.topLeft,
    AlignmentGeometry end = Alignment.bottomRight,
    BorderRadius? borderRadius,
    EdgeInsetsGeometry? padding,
    List<BoxShadow>? boxShadow,
    Border? border,
    VoidCallback? onTap,
  }) {
    return card(
      context: context,
      child: child,
      gradient: LinearGradient(
        colors: colors,
        begin: begin,
        end: end,
      ),
      borderRadius: borderRadius,
      padding: padding,
      boxShadow: boxShadow,
      border: border,
      onTap: onTap,
    );
  }
  
  /// Standard section header
  static Widget sectionHeader({
    required BuildContext context,
    required String title,
    String? subtitle,
    Widget? trailing,
    VoidCallback? onSeeAll,
    EdgeInsetsGeometry? padding,
  }) {
    return Padding(
      padding: padding ?? AppUI.paddingHorizontalMedium,
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  title,
                  style: AppText.titleLarge(context),
                  maxLines: 1,
                  overflow: TextOverflow.ellipsis,
                ),
                if (subtitle != null) ...[
                  const SizedBox(height: 4),
                  Text(
                    subtitle,
                    style: AppText.bodySmall(
                      context,
                      color: AppTheme.textSecondaryLight,
                    ),
                    maxLines: 1,
                    overflow: TextOverflow.ellipsis,
                  ),
                ],
              ],
            ),
          ),
          if (trailing != null)
            trailing
          else if (onSeeAll != null)
            TextButton(
              onPressed: onSeeAll,
              style: AppUI.textButtonStyle(),
              child: Text(
                'See All',
                style: AppText.labelMedium(
                  context,
                  color: AppTheme.primaryColor,
                ),
              ),
            ),
        ],
      ),
    );
  }
  
  /// Standard empty state widget
  static Widget emptyState({
    required BuildContext context,
    required String title,
    required String message,
    IconData icon = Icons.info_outline,
    Color? iconColor,
    String? buttonText,
    VoidCallback? onButtonPressed,
    EdgeInsetsGeometry? padding,
  }) {
    return Center(
      child: Padding(
        padding: padding ?? AppUI.paddingMedium,
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              icon,
              size: 80,
              color: iconColor ?? Colors.grey.withValues(alpha: 0.3 * 255),
            ),
            const SizedBox(height: 16),
            Text(
              title,
              style: AppText.titleLarge(context),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 8),
            Text(
              message,
              style: AppText.bodyMedium(
                context,
                color: AppTheme.textSecondaryLight,
              ),
              textAlign: TextAlign.center,
            ),
            if (buttonText != null && onButtonPressed != null) ...[
              const SizedBox(height: 24),
              ElevatedButton(
                onPressed: onButtonPressed,
                style: AppUI.buttonStyle(),
                child: Text(buttonText),
              ),
            ],
          ],
        ),
      ),
    );
  }
  
  /// Standard loading indicator
  static Widget loadingIndicator({
    Color? color,
    double size = 40,
  }) {
    return Center(
      child: SizedBox(
        width: size,
        height: size,
        child: CircularProgressIndicator(
          color: color ?? AppTheme.primaryColor,
          strokeWidth: 3,
        ),
      ),
    );
  }
  
  /// Standard error widget
  static Widget errorWidget({
    required BuildContext context,
    required String message,
    VoidCallback? onRetry,
    EdgeInsetsGeometry? padding,
  }) {
    return Center(
      child: Padding(
        padding: padding ?? AppUI.paddingMedium,
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.error_outline,
              size: 60,
              color: AppTheme.errorColor.withValues(alpha: 0.7 * 255),
            ),
            const SizedBox(height: 16),
            Text(
              'Error',
              style: AppText.titleLarge(context),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 8),
            Text(
              message,
              style: AppText.bodyMedium(
                context,
                color: AppTheme.textSecondaryLight,
              ),
              textAlign: TextAlign.center,
            ),
            if (onRetry != null) ...[
              const SizedBox(height: 24),
              ElevatedButton.icon(
                onPressed: onRetry,
                style: AppUI.buttonStyle(),
                icon: const Icon(Icons.refresh),
                label: const Text('Retry'),
              ),
            ],
          ],
        ),
      ),
    );
  }
  
  /// Standard badge widget
  static Widget badge({
    required BuildContext context,
    required String text,
    Color? backgroundColor,
    Color? textColor,
    BorderRadius? borderRadius,
    EdgeInsetsGeometry? padding,
  }) {
    return Container(
      padding: padding ?? const EdgeInsets.symmetric(
        horizontal: 8,
        vertical: 4,
      ),
      decoration: BoxDecoration(
        color: backgroundColor ?? AppTheme.primaryColor,
        borderRadius: borderRadius ?? AppUI.borderRadiusCircular,
      ),
      child: Text(
        text,
        style: AppText.labelSmall(
          context,
          color: textColor ?? Colors.white,
        ),
      ),
    );
  }
}
