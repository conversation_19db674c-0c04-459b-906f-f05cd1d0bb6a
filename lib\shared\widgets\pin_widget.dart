import 'package:flutter/material.dart';

/// A widget that displays a row of pins, useful for OTP or PIN entry visualization
class PinWidget extends StatelessWidget {
  final int count;
  final int pinLength;
  final double height;
  final double width;
  final double space;
  final BoxDecoration? decoration;

  const PinWidget({
    super.key,
    required this.count,
    required this.pinLength,
    this.height = 20.0,
    this.width = 20.0,
    this.space = 10.0,
    this.decoration,
  });

  @override
  Widget build(BuildContext context) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.center,
      children: List.generate(
        pinLength,
        (i) => Container(
          margin: EdgeInsets.symmetric(horizontal: space / 2),
          height: height,
          width: width,
          decoration: decoration ??
              BoxDecoration(
                color: i < count ? Theme.of(context).colorScheme.primary : null,
                border: Border.all(
                  // Use titleMedium instead of headline6
                  color: Theme.of(context).textTheme.titleMedium?.color ?? Colors.black,
                ),
                borderRadius: BorderRadius.circular(height / 2),
              ),
        ),
      ),
    );
  }
}
