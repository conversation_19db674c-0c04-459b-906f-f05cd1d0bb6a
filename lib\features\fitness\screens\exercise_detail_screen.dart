import 'package:flutter/material.dart';
import 'package:fit_4_force/core/theme/app_theme.dart';
import 'package:fit_4_force/features/fitness/models/workout_model.dart';
import 'package:fit_4_force/features/fitness/screens/exercise_video_screen.dart';

class ExerciseDetailScreen extends StatefulWidget {
  final ExerciseModel exercise;

  const ExerciseDetailScreen({super.key, required this.exercise});

  @override
  State<ExerciseDetailScreen> createState() => _ExerciseDetailScreenState();
}

class _ExerciseDetailScreenState extends State<ExerciseDetailScreen> {
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(widget.exercise.name),
        actions: [
          IconButton(
            icon: const Icon(Icons.bookmark_outline),
            onPressed: () {
              ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(
                  content: Text('${widget.exercise.name} saved to favorites'),
                ),
              );
            },
          ),
        ],
      ),
      body: SingleChildScrollView(
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Exercise Image
            Container(
              height: 250,
              width: double.infinity,
              decoration: BoxDecoration(color: Colors.grey.shade200),
              child: Hero(
                tag: 'exercise_image_${widget.exercise.id}',
                child: Image.asset(
                  widget.exercise.imageUrl,
                  fit: BoxFit.contain,
                  errorBuilder: (context, error, stackTrace) {
                    return const Center(
                      child: Icon(
                        Icons.fitness_center,
                        size: 80,
                        color: Colors.grey,
                      ),
                    );
                  },
                ),
              ),
            ),

            // Exercise Info
            Padding(
              padding: const EdgeInsets.all(16.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Title and stats
                  Row(
                    children: [
                      Expanded(
                        child: Text(
                          widget.exercise.name,
                          style: Theme.of(context).textTheme.headlineSmall
                              ?.copyWith(fontWeight: FontWeight.bold),
                        ),
                      ),
                      _buildDifficultyBadge(),
                    ],
                  ),
                  const SizedBox(height: 16),

                  // Stats row
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceAround,
                    children: [
                      _buildStatItem(
                        Icons.fitness_center,
                        widget.exercise.duration > 0
                            ? '${widget.exercise.duration}s'
                            : '${widget.exercise.reps} reps',
                        'Per Set',
                      ),
                      _buildStatItem(
                        Icons.repeat,
                        '${widget.exercise.sets}',
                        'Sets',
                      ),
                      _buildStatItem(
                        Icons.timer_outlined,
                        '${widget.exercise.restTime}s',
                        'Rest',
                      ),
                    ],
                  ),

                  const SizedBox(height: 24),

                  // Description
                  Text(
                    'Description',
                    style: Theme.of(context).textTheme.titleLarge?.copyWith(
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const SizedBox(height: 8),
                  Text(
                    widget.exercise.description,
                    style: Theme.of(
                      context,
                    ).textTheme.bodyLarge?.copyWith(height: 1.5),
                  ),

                  const SizedBox(height: 24),

                  // Video demonstration
                  Text(
                    'Video Demonstration',
                    style: Theme.of(context).textTheme.titleLarge?.copyWith(
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const SizedBox(height: 16),
                  _buildVideoPlayer(),

                  const SizedBox(height: 24),

                  // Instructions
                  Text(
                    'Instructions',
                    style: Theme.of(context).textTheme.titleLarge?.copyWith(
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const SizedBox(height: 8),
                  _buildInstructions(),

                  const SizedBox(height: 24),

                  // Muscles worked
                  Text(
                    'Muscles Worked',
                    style: Theme.of(context).textTheme.titleLarge?.copyWith(
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const SizedBox(height: 16),
                  _buildMusclesWorked(),

                  const SizedBox(height: 32),
                ],
              ),
            ),
          ],
        ),
      ),
      bottomNavigationBar: Padding(
        padding: const EdgeInsets.all(16.0),
        child: ElevatedButton(
          onPressed: () {
            Navigator.pop(context);
          },
          style: ElevatedButton.styleFrom(
            backgroundColor: AppTheme.primaryColor,
            foregroundColor: Colors.white,
            padding: const EdgeInsets.symmetric(vertical: 16),
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(12),
            ),
          ),
          child: const Text(
            'Start Exercise',
            style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
          ),
        ),
      ),
    );
  }

  Widget _buildDifficultyBadge() {
    // Determine difficulty based on reps or duration
    String difficulty = 'Beginner';
    Color color = Colors.green;

    if (widget.exercise.duration > 0) {
      if (widget.exercise.duration > 30) {
        difficulty = 'Advanced';
        color = Colors.red;
      } else if (widget.exercise.duration > 15) {
        difficulty = 'Intermediate';
        color = Colors.orange;
      }
    } else {
      if (widget.exercise.reps > 12) {
        difficulty = 'Advanced';
        color = Colors.red;
      } else if (widget.exercise.reps > 8) {
        difficulty = 'Intermediate';
        color = Colors.orange;
      }
    }

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 26), // 0.1 opacity
        borderRadius: BorderRadius.circular(16),
        border: Border.all(color: color),
      ),
      child: Text(
        difficulty,
        style: TextStyle(color: color, fontWeight: FontWeight.bold),
      ),
    );
  }

  Widget _buildStatItem(IconData icon, String value, String label) {
    return Column(
      children: [
        Container(
          padding: const EdgeInsets.all(12),
          decoration: BoxDecoration(
            color: AppTheme.primaryColor.withValues(alpha: 26), // 0.1 opacity
            shape: BoxShape.circle,
          ),
          child: Icon(icon, color: AppTheme.primaryColor, size: 24),
        ),
        const SizedBox(height: 8),
        Text(
          value,
          style: const TextStyle(fontWeight: FontWeight.bold, fontSize: 16),
        ),
        Text(
          label,
          style: TextStyle(color: Colors.grey.shade600, fontSize: 12),
        ),
      ],
    );
  }

  Widget _buildVideoPlayer() {
    return GestureDetector(
      onTap: () {
        // Navigate to the video player screen
        Navigator.push(
          context,
          MaterialPageRoute(
            builder:
                (context) => ExerciseVideoScreen(exercise: widget.exercise),
          ),
        );
      },
      child: Container(
        height: 200,
        width: double.infinity,
        decoration: BoxDecoration(
          color: Colors.black,
          borderRadius: BorderRadius.circular(12),
        ),
        child: Stack(
          alignment: Alignment.center,
          children: [
            ClipRRect(
              borderRadius: BorderRadius.circular(12),
              child: Image.asset(
                widget.exercise.imageUrl,
                width: double.infinity,
                height: double.infinity,
                fit: BoxFit.cover,
                errorBuilder: (context, error, stackTrace) {
                  return Container(color: Colors.grey.shade800);
                },
              ),
            ),
            Container(
              color: Colors.black.withValues(alpha: 128),
            ), // 0.5 opacity
            Container(
              padding: const EdgeInsets.all(16),
              decoration: const BoxDecoration(
                color: Colors.red,
                shape: BoxShape.circle,
              ),
              child: const Icon(
                Icons.play_arrow,
                color: Colors.white,
                size: 32,
              ),
            ),
            // Video duration indicator
            Positioned(
              bottom: 12,
              right: 12,
              child: Container(
                padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                decoration: BoxDecoration(
                  color: Colors.black.withValues(alpha: 178), // 0.7 opacity
                  borderRadius: BorderRadius.circular(4),
                ),
                child: const Row(
                  children: [
                    Icon(Icons.videocam, color: Colors.white, size: 14),
                    SizedBox(width: 4),
                    Text(
                      'Watch Demo',
                      style: TextStyle(
                        color: Colors.white,
                        fontSize: 12,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildInstructions() {
    // Enhanced instructions based on exercise name
    List<Map<String, dynamic>> instructions = [];

    if (widget.exercise.name.toLowerCase().contains('push-up')) {
      instructions = [
        {
          'text': 'Start in a plank position with hands shoulder-width apart',
          'detail':
              'Place your hands firmly on the ground, directly under your shoulders. Your body should form a straight line from head to heels.',
        },
        {
          'text': 'Keep your core engaged and body in a straight line',
          'detail':
              'Tighten your abdominal muscles and glutes to maintain proper alignment. Don\'t let your hips sag or lift too high.',
        },
        {
          'text': 'Lower your body until your chest nearly touches the floor',
          'detail':
              'Bend your elbows at about a 45-degree angle to your body as you lower yourself. Keep your neck neutral, looking slightly ahead.',
        },
        {
          'text': 'Push back up to the starting position',
          'detail':
              'Exhale as you push through your palms to straighten your arms. Keep your body rigid throughout the movement.',
        },
        {
          'text': 'Repeat for the prescribed number of repetitions',
          'detail':
              'Maintain consistent form throughout all repetitions. If form begins to suffer, take a short rest or switch to an easier variation.',
        },
      ];
    } else if (widget.exercise.name.toLowerCase().contains('jumping jack')) {
      instructions = [
        {
          'text': 'Stand with feet together and arms at your sides',
          'detail':
              'Begin with a neutral spine, shoulders relaxed, and knees slightly bent. Engage your core muscles.',
        },
        {
          'text': 'Jump up, spreading your feet beyond shoulder width',
          'detail':
              'As you jump, simultaneously raise your arms above your head. Your feet should land wider than shoulder-width apart.',
        },
        {
          'text': 'Jump again, returning to the starting position',
          'detail':
              'Bring your arms back down to your sides as you jump your feet back together. Land softly with slightly bent knees.',
        },
        {
          'text': 'Repeat at a quick pace for the prescribed duration',
          'detail':
              'Maintain a consistent rhythm. Focus on controlled movements rather than maximum speed.',
        },
      ];
    } else {
      // Default instructions with more detail
      instructions = [
        {
          'text': 'Maintain proper form throughout the exercise',
          'detail':
              'Focus on technique over speed or weight. Proper form reduces injury risk and maximizes effectiveness.',
        },
        {
          'text': 'Breathe steadily and don\'t hold your breath',
          'detail':
              'Generally, exhale during exertion (lifting, pushing) and inhale during the easier phase (lowering, returning).',
        },
        {
          'text': 'Focus on the muscle groups being worked',
          'detail':
              'Establish a mind-muscle connection by consciously engaging the target muscles throughout the movement.',
        },
        {
          'text': 'Complete all prescribed repetitions and sets',
          'detail':
              'Rest appropriately between sets. If you cannot maintain proper form, reduce weight or modify the exercise.',
        },
      ];
    }

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        ...instructions.asMap().entries.map((entry) {
          final index = entry.key;
          final instruction = entry.value;

          return Padding(
            padding: const EdgeInsets.only(bottom: 16.0),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Container(
                      width: 24,
                      height: 24,
                      decoration: BoxDecoration(
                        color: AppTheme.primaryColor,
                        shape: BoxShape.circle,
                      ),
                      child: Center(
                        child: Text(
                          '${index + 1}',
                          style: const TextStyle(
                            color: Colors.white,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ),
                    ),
                    const SizedBox(width: 12),
                    Expanded(
                      child: Text(
                        instruction['text'],
                        style: const TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.w600,
                          height: 1.4,
                        ),
                      ),
                    ),
                  ],
                ),
                Padding(
                  padding: const EdgeInsets.only(left: 36.0, top: 4.0),
                  child: Text(
                    instruction['detail'],
                    style: TextStyle(
                      fontSize: 14,
                      height: 1.4,
                      color: Colors.grey.shade700,
                    ),
                  ),
                ),
              ],
            ),
          );
        }),

        const SizedBox(height: 24),

        // Alternative Exercises Section
        Text(
          'Alternative Exercises',
          style: Theme.of(
            context,
          ).textTheme.titleMedium?.copyWith(fontWeight: FontWeight.bold),
        ),
        const SizedBox(height: 16),
        _buildAlternativeExercises(),

        const SizedBox(height: 24),

        // Progress Tracking Section
        Text(
          'Your Progress',
          style: Theme.of(
            context,
          ).textTheme.titleMedium?.copyWith(fontWeight: FontWeight.bold),
        ),
        const SizedBox(height: 16),
        _buildProgressTracking(),
      ],
    );
  }

  Widget _buildAlternativeExercises() {
    // Alternative exercises based on difficulty levels
    List<Map<String, dynamic>> alternatives = [];

    if (widget.exercise.name.toLowerCase().contains('push-up')) {
      alternatives = [
        {
          'name': 'Wall Push-Ups',
          'level': 'Beginner',
          'description': 'Perform push-ups against a wall instead of the floor',
          'imageUrl': 'assets/images/exercises/wall_pushups.png',
        },
        {
          'name': 'Knee Push-Ups',
          'level': 'Beginner-Intermediate',
          'description': 'Perform push-ups with knees on the ground',
          'imageUrl': 'assets/images/exercises/knee_pushups.png',
        },
        {
          'name': 'Diamond Push-Ups',
          'level': 'Advanced',
          'description':
              'Push-ups with hands close together forming a diamond shape',
          'imageUrl': 'assets/images/exercises/diamond_pushups.png',
        },
      ];
    } else if (widget.exercise.name.toLowerCase().contains('jumping jack')) {
      alternatives = [
        {
          'name': 'Step Jacks',
          'level': 'Beginner',
          'description': 'Step out to the side instead of jumping',
          'imageUrl': 'assets/images/exercises/step_jacks.png',
        },
        {
          'name': 'Low-Impact Jacks',
          'level': 'Beginner-Intermediate',
          'description': 'Alternate stepping out with each foot',
          'imageUrl': 'assets/images/exercises/low_impact_jacks.png',
        },
        {
          'name': 'Plyo Jacks',
          'level': 'Advanced',
          'description':
              'Jumping jacks with a higher jump and more explosive movement',
          'imageUrl': 'assets/images/exercises/plyo_jacks.png',
        },
      ];
    } else {
      // Default alternatives
      alternatives = [
        {
          'name': 'Modified Version',
          'level': 'Beginner',
          'description':
              'Easier variation with reduced range of motion or assistance',
          'imageUrl': 'assets/images/exercises/modified_exercise.png',
        },
        {
          'name': 'Standard Version',
          'level': 'Intermediate',
          'description': 'The standard form of this exercise',
          'imageUrl': widget.exercise.imageUrl,
        },
        {
          'name': 'Advanced Version',
          'level': 'Advanced',
          'description':
              'More challenging variation with increased resistance or complexity',
          'imageUrl': 'assets/images/exercises/advanced_exercise.png',
        },
      ];
    }

    return SizedBox(
      height: 120,
      child: ListView.separated(
        scrollDirection: Axis.horizontal,
        itemCount: alternatives.length,
        separatorBuilder: (context, index) => const SizedBox(width: 16),
        itemBuilder: (context, index) {
          final alternative = alternatives[index];
          return _buildAlternativeExerciseCard(alternative);
        },
      ),
    );
  }

  Widget _buildAlternativeExerciseCard(Map<String, dynamic> alternative) {
    Color levelColor;

    switch (alternative['level']) {
      case 'Beginner':
        levelColor = Colors.green;
        break;
      case 'Beginner-Intermediate':
        levelColor = Colors.teal;
        break;
      case 'Intermediate':
        levelColor = Colors.blue;
        break;
      case 'Advanced':
        levelColor = Colors.red;
        break;
      default:
        levelColor = Colors.blue;
    }

    return Container(
      width: 200,
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withValues(alpha: 38), // 0.15 opacity
            blurRadius: 6,
            offset: const Offset(0, 3),
          ),
        ],
      ),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          borderRadius: BorderRadius.circular(12),
          onTap: () {
            // In a real app, this would navigate to the alternative exercise
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                content: Text('Switching to ${alternative['name']}'),
                duration: const Duration(seconds: 2),
              ),
            );
          },
          child: Padding(
            padding: const EdgeInsets.all(8.0),
            child: Row(
              children: [
                ClipRRect(
                  borderRadius: BorderRadius.circular(8),
                  child: Image.asset(
                    alternative['imageUrl'],
                    width: 60,
                    height: 60,
                    fit: BoxFit.cover,
                    errorBuilder: (context, error, stackTrace) {
                      return Container(
                        width: 60,
                        height: 60,
                        color: levelColor.withValues(alpha: 26),
                        child: Icon(Icons.fitness_center, color: levelColor),
                      );
                    },
                  ),
                ),
                const SizedBox(width: 8),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Text(
                        alternative['name'],
                        style: const TextStyle(
                          fontWeight: FontWeight.bold,
                          fontSize: 14,
                        ),
                        maxLines: 1,
                        overflow: TextOverflow.ellipsis,
                      ),
                      const SizedBox(height: 2),
                      Container(
                        padding: const EdgeInsets.symmetric(
                          horizontal: 6,
                          vertical: 2,
                        ),
                        decoration: BoxDecoration(
                          color: levelColor.withValues(alpha: 26),
                          borderRadius: BorderRadius.circular(4),
                        ),
                        child: Text(
                          alternative['level'],
                          style: TextStyle(
                            color: levelColor,
                            fontSize: 10,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ),
                      const SizedBox(height: 4),
                      Text(
                        alternative['description'],
                        style: TextStyle(
                          fontSize: 12,
                          color: Colors.grey.shade600,
                        ),
                        maxLines: 2,
                        overflow: TextOverflow.ellipsis,
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildProgressTracking() {
    // Mock progress data
    final progressData = [
      {'date': 'Today', 'reps': 12, 'sets': 3, 'weight': '0 kg'},
      {'date': 'Yesterday', 'reps': 10, 'sets': 3, 'weight': '0 kg'},
      {'date': '3 days ago', 'reps': 8, 'sets': 3, 'weight': '0 kg'},
      {'date': '1 week ago', 'reps': 6, 'sets': 2, 'weight': '0 kg'},
    ];

    return Column(
      children: [
        // Progress chart
        Container(
          height: 150,
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(12),
            boxShadow: [
              BoxShadow(
                color: Colors.grey.withValues(alpha: 38), // 0.15 opacity
                blurRadius: 6,
                offset: const Offset(0, 3),
              ),
            ],
          ),
          child: Row(
            children: [
              // Y-axis labels
              Column(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(
                    '15',
                    style: TextStyle(fontSize: 10, color: Colors.grey.shade600),
                  ),
                  Text(
                    '10',
                    style: TextStyle(fontSize: 10, color: Colors.grey.shade600),
                  ),
                  Text(
                    '5',
                    style: TextStyle(fontSize: 10, color: Colors.grey.shade600),
                  ),
                  Text(
                    '0',
                    style: TextStyle(fontSize: 10, color: Colors.grey.shade600),
                  ),
                ],
              ),
              const SizedBox(width: 8),
              // Chart
              Expanded(
                child: CustomPaint(
                  painter: ProgressChartPainter(
                    data: [6, 8, 10, 12],
                    maxValue: 15,
                  ),
                  child: Container(),
                ),
              ),
            ],
          ),
        ),

        const SizedBox(height: 16),

        // Progress history
        Container(
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(12),
            boxShadow: [
              BoxShadow(
                color: Colors.grey.withValues(alpha: 38), // 0.15 opacity
                blurRadius: 6,
                offset: const Offset(0, 3),
              ),
            ],
          ),
          child: Column(
            children: [
              // Header
              Padding(
                padding: const EdgeInsets.all(12.0),
                child: Row(
                  children: [
                    Expanded(
                      flex: 2,
                      child: Text(
                        'Date',
                        style: TextStyle(
                          fontWeight: FontWeight.bold,
                          color: Colors.grey.shade800,
                        ),
                      ),
                    ),
                    Expanded(
                      child: Text(
                        'Reps',
                        style: TextStyle(
                          fontWeight: FontWeight.bold,
                          color: Colors.grey.shade800,
                        ),
                        textAlign: TextAlign.center,
                      ),
                    ),
                    Expanded(
                      child: Text(
                        'Sets',
                        style: TextStyle(
                          fontWeight: FontWeight.bold,
                          color: Colors.grey.shade800,
                        ),
                        textAlign: TextAlign.center,
                      ),
                    ),
                    Expanded(
                      child: Text(
                        'Weight',
                        style: TextStyle(
                          fontWeight: FontWeight.bold,
                          color: Colors.grey.shade800,
                        ),
                        textAlign: TextAlign.center,
                      ),
                    ),
                  ],
                ),
              ),

              const Divider(height: 1),

              // Progress rows
              ...progressData
                  .map(
                    (entry) => Column(
                      children: [
                        Padding(
                          padding: const EdgeInsets.all(12.0),
                          child: Row(
                            children: [
                              Expanded(
                                flex: 2,
                                child: Text(
                                  entry['date'] as String,
                                  style: TextStyle(color: Colors.grey.shade700),
                                ),
                              ),
                              Expanded(
                                child: Text(
                                  '${entry['reps']}',
                                  style: TextStyle(color: Colors.grey.shade700),
                                  textAlign: TextAlign.center,
                                ),
                              ),
                              Expanded(
                                child: Text(
                                  '${entry['sets']}',
                                  style: TextStyle(color: Colors.grey.shade700),
                                  textAlign: TextAlign.center,
                                ),
                              ),
                              Expanded(
                                child: Text(
                                  entry['weight'] as String,
                                  style: TextStyle(color: Colors.grey.shade700),
                                  textAlign: TextAlign.center,
                                ),
                              ),
                            ],
                          ),
                        ),
                        if (entry != progressData.last)
                          const Divider(height: 1),
                      ],
                    ),
                  )
                  ,

              // Add new entry button
              Padding(
                padding: const EdgeInsets.all(12.0),
                child: ElevatedButton.icon(
                  onPressed: () {
                    // Show dialog to add new progress entry
                    _showAddProgressDialog();
                  },
                  icon: const Icon(Icons.add),
                  label: const Text('Add New Entry'),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: AppTheme.primaryColor,
                    foregroundColor: Colors.white,
                    minimumSize: const Size(double.infinity, 40),
                  ),
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }

  void _showAddProgressDialog() {
    showDialog(
      context: context,
      builder:
          (context) => AlertDialog(
            title: const Text('Add Progress Entry'),
            content: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                TextField(
                  decoration: const InputDecoration(
                    labelText: 'Reps',
                    border: OutlineInputBorder(),
                  ),
                  keyboardType: TextInputType.number,
                ),
                const SizedBox(height: 16),
                TextField(
                  decoration: const InputDecoration(
                    labelText: 'Sets',
                    border: OutlineInputBorder(),
                  ),
                  keyboardType: TextInputType.number,
                ),
                const SizedBox(height: 16),
                TextField(
                  decoration: const InputDecoration(
                    labelText: 'Weight (kg)',
                    border: OutlineInputBorder(),
                  ),
                  keyboardType: TextInputType.number,
                ),
              ],
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.pop(context),
                child: const Text('CANCEL'),
              ),
              ElevatedButton(
                onPressed: () {
                  Navigator.pop(context);
                  ScaffoldMessenger.of(context).showSnackBar(
                    const SnackBar(
                      content: Text('Progress entry added'),
                      duration: Duration(seconds: 2),
                    ),
                  );
                },
                style: ElevatedButton.styleFrom(
                  backgroundColor: AppTheme.primaryColor,
                ),
                child: const Text('SAVE'),
              ),
            ],
          ),
    );
  }

  Widget _buildMusclesWorked() {
    // Sample muscles worked based on exercise name
    List<Map<String, dynamic>> muscles = [];

    if (widget.exercise.name.toLowerCase().contains('push-up')) {
      muscles = [
        {'name': 'Chest', 'intensity': 0.9},
        {'name': 'Shoulders', 'intensity': 0.6},
        {'name': 'Triceps', 'intensity': 0.7},
        {'name': 'Core', 'intensity': 0.5},
      ];
    } else if (widget.exercise.name.toLowerCase().contains('jumping jack')) {
      muscles = [
        {'name': 'Calves', 'intensity': 0.8},
        {'name': 'Shoulders', 'intensity': 0.5},
        {'name': 'Cardiovascular', 'intensity': 0.9},
      ];
    } else {
      // Default muscles
      muscles = [
        {'name': 'Full Body', 'intensity': 0.7},
        {'name': 'Core', 'intensity': 0.6},
      ];
    }

    return Column(
      children:
          muscles.map((muscle) {
            final name = muscle['name'] as String;
            final intensity = muscle['intensity'] as double;

            return Padding(
              padding: const EdgeInsets.only(bottom: 12.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    name,
                    style: const TextStyle(
                      fontWeight: FontWeight.bold,
                      fontSize: 16,
                    ),
                  ),
                  const SizedBox(height: 4),
                  LinearProgressIndicator(
                    value: intensity,
                    backgroundColor: Colors.grey.shade200,
                    valueColor: AlwaysStoppedAnimation<Color>(
                      _getIntensityColor(intensity),
                    ),
                    minHeight: 8,
                    borderRadius: BorderRadius.circular(4),
                  ),
                ],
              ),
            );
          }).toList(),
    );
  }

  Color _getIntensityColor(double intensity) {
    if (intensity > 0.7) {
      return Colors.red;
    } else if (intensity > 0.4) {
      return Colors.orange;
    } else {
      return Colors.green;
    }
  }
}

/// Custom painter for drawing the progress chart
class ProgressChartPainter extends CustomPainter {
  final List<int> data;
  final int maxValue;

  ProgressChartPainter({required this.data, required this.maxValue});

  @override
  void paint(Canvas canvas, Size size) {
    final paint =
        Paint()
          ..color = Colors.blue
          ..strokeWidth = 3
          ..style = PaintingStyle.stroke
          ..strokeCap = StrokeCap.round;

    final fillPaint =
        Paint()
          ..color = Colors.blue.withValues(alpha: 50)
          ..style = PaintingStyle.fill;

    final dotPaint =
        Paint()
          ..color = Colors.blue
          ..style = PaintingStyle.fill;

    final path = Path();
    final fillPath = Path();

    // Draw grid lines
    final gridPaint =
        Paint()
          ..color = Colors.grey.withValues(alpha: 50)
          ..strokeWidth = 1;

    for (int i = 0; i <= 3; i++) {
      final y = size.height - (size.height * i / 3);
      canvas.drawLine(Offset(0, y), Offset(size.width, y), gridPaint);
    }

    if (data.isEmpty) return;

    final segmentWidth = size.width / (data.length - 1);

    // Start at the first point
    final firstPoint = Offset(
      0,
      size.height - (data[0] / maxValue * size.height),
    );
    path.moveTo(firstPoint.dx, firstPoint.dy);
    fillPath.moveTo(0, size.height);
    fillPath.lineTo(firstPoint.dx, firstPoint.dy);

    // Draw lines between points
    for (int i = 1; i < data.length; i++) {
      final x = segmentWidth * i;
      final y = size.height - (data[i] / maxValue * size.height);
      path.lineTo(x, y);
      fillPath.lineTo(x, y);

      // Draw data point
      canvas.drawCircle(Offset(x, y), 5, dotPaint);
    }

    // Draw first data point
    canvas.drawCircle(firstPoint, 5, dotPaint);

    // Complete the fill path
    fillPath.lineTo(size.width, size.height);
    fillPath.close();

    // Draw the paths
    canvas.drawPath(fillPath, fillPaint);
    canvas.drawPath(path, paint);
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => true;
}
