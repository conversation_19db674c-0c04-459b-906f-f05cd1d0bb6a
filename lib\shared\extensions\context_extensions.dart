import 'package:flutter/material.dart';

/// Extensions on BuildContext to provide compatibility with older packages
extension BuildContextExtensions on BuildContext {
  /// Get the theme data
  ThemeData theme() => Theme.of(this);
  
  /// Get the text theme
  TextTheme textTheme() => Theme.of(this).textTheme;
  
  /// Get the color scheme
  ColorScheme colorScheme() => Theme.of(this).colorScheme;
}

/// Extensions on TextTheme to provide compatibility with older packages
extension TextThemeExtensions on TextTheme {
  /// Compatibility for headline1 (now headlineLarge)
  TextStyle? get headline1 => headlineLarge;
  
  /// Compatibility for headline2 (now headlineMedium)
  TextStyle? get headline2 => headlineMedium;
  
  /// Compatibility for headline3 (now headlineSmall)
  TextStyle? get headline3 => headlineSmall;
  
  /// Compatibility for headline4 (now titleLarge)
  TextStyle? get headline4 => titleLarge;
  
  /// Compatibility for headline5 (now titleMedium)
  TextStyle? get headline5 => titleMedium;
  
  /// Compatibility for headline6 (now titleSmall)
  TextStyle? get headline6 => titleSmall;
  
  /// Compatibility for subtitle1 (now bodyLarge)
  TextStyle? get subtitle1 => bodyLarge;
  
  /// Compatibility for subtitle2 (now bodyMedium)
  TextStyle? get subtitle2 => bodyMedium;
  
  /// Compatibility for bodyText1 (now bodyLarge)
  TextStyle? get bodyText1 => bodyLarge;
  
  /// Compatibility for bodyText2 (now bodyMedium)
  TextStyle? get bodyText2 => bodyMedium;
}
