import 'package:fit_4_force/core/services/api_service.dart';

/// API service for community-related endpoints
class CommunityApiService {
  /// Base API service
  final ApiService _apiService;

  /// Constructor
  CommunityApiService({ApiService? apiService})
    : _apiService = apiService ?? ApiService();

  /// Get community posts
  Future<List<dynamic>> getPosts(
    String token, {
    int page = 1,
    int limit = 20,
    String? agency,
  }) async {
    final queryParams = <String, dynamic>{'page': page, 'limit': limit};

    if (agency != null) {
      queryParams['agency'] = agency;
    }

    final response = await _apiService.get(
      '/community/posts',
      queryParams: queryParams,
      token: token,
    );

    return response['data'];
  }

  /// Get post by ID
  Future<Map<String, dynamic>> getPostById(String postId, String token) async {
    final response = await _apiService.get(
      '/community/posts/$postId',
      token: token,
    );

    return response;
  }

  /// Create post
  Future<Map<String, dynamic>> createPost(
    Map<String, dynamic> data,
    String token,
  ) async {
    final response = await _apiService.post(
      '/community/posts',
      body: data,
      token: token,
    );

    return response;
  }

  /// Update post
  Future<Map<String, dynamic>> updatePost(
    String postId,
    Map<String, dynamic> data,
    String token,
  ) async {
    final response = await _apiService.put(
      '/community/posts/$postId',
      body: data,
      token: token,
    );

    return response;
  }

  /// Delete post
  Future<void> deletePost(String postId, String token) async {
    await _apiService.delete('/community/posts/$postId', token: token);
  }

  /// Like post
  Future<void> likePost(String postId, String token) async {
    await _apiService.post('/community/posts/$postId/like', token: token);
  }

  /// Unlike post
  Future<void> unlikePost(String postId, String token) async {
    await _apiService.post('/community/posts/$postId/unlike', token: token);
  }

  /// Get post comments
  Future<List<dynamic>> getPostComments(
    String postId,
    String token, {
    int page = 1,
    int limit = 20,
  }) async {
    final response = await _apiService.get(
      '/community/posts/$postId/comments',
      queryParams: {'page': page, 'limit': limit},
      token: token,
    );

    return response['data'];
  }

  /// Add comment to post
  Future<Map<String, dynamic>> addComment(
    String postId,
    String content,
    String token,
  ) async {
    final response = await _apiService.post(
      '/community/posts/$postId/comments',
      body: {'content': content},
      token: token,
    );

    return response;
  }

  /// Delete comment
  Future<void> deleteComment(
    String postId,
    String commentId,
    String token,
  ) async {
    await _apiService.delete(
      '/community/posts/$postId/comments/$commentId',
      token: token,
    );
  }

  /// Get user posts
  Future<List<dynamic>> getUserPosts(
    String userId,
    String token, {
    int page = 1,
    int limit = 20,
  }) async {
    final response = await _apiService.get(
      '/community/users/$userId/posts',
      queryParams: {'page': page, 'limit': limit},
      token: token,
    );

    return response['data'];
  }
}
