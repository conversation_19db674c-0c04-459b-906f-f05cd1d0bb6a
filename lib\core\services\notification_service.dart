import 'package:supabase_flutter/supabase_flutter.dart';
import 'package:fit_4_force/core/config/supabase_config.dart';
import 'package:logger/logger.dart';

enum NotificationType {
  communityPost,
  communityReply,
  workoutReminder,
  studyGroupActivity,
  premiumUpdate,
  achievement,
  general,
}

class NotificationPreferences {
  final bool pushNotifications;
  final bool emailNotifications;
  final bool communityUpdates;
  final bool workoutReminders;
  final bool studyGroupNotifications;
  final bool achievementNotifications;
  final bool premiumNotifications;

  const NotificationPreferences({
    this.pushNotifications = true,
    this.emailNotifications = true,
    this.communityUpdates = true,
    this.workoutReminders = true,
    this.studyGroupNotifications = true,
    this.achievementNotifications = true,
    this.premiumNotifications = true,
  });

  Map<String, dynamic> toJson() => {
    'push_notifications': pushNotifications,
    'email_notifications': emailNotifications,
    'community_updates': communityUpdates,
    'workout_reminders': workoutReminders,
    'study_group_notifications': studyGroupNotifications,
    'achievement_notifications': achievementNotifications,
    'premium_notifications': premiumNotifications,
  };

  factory NotificationPreferences.fromJson(Map<String, dynamic> json) {
    return NotificationPreferences(
      pushNotifications: json['push_notifications'] ?? true,
      emailNotifications: json['email_notifications'] ?? true,
      communityUpdates: json['community_updates'] ?? true,
      workoutReminders: json['workout_reminders'] ?? true,
      studyGroupNotifications: json['study_group_notifications'] ?? true,
      achievementNotifications: json['achievement_notifications'] ?? true,
      premiumNotifications: json['premium_notifications'] ?? true,
    );
  }
}

class NotificationService {
  static final NotificationService _instance = NotificationService._internal();
  factory NotificationService() => _instance;
  NotificationService._internal();

  final Logger _logger = Logger();

  /// Get Supabase client
  SupabaseClient get _client => SupabaseConfig.client;

  /// Initialize notification service
  Future<void> initialize() async {
    try {
      await _createNotificationTables();
      _logger.i('✅ Notification service initialized');
    } catch (e) {
      _logger.e('❌ Error initializing notification service: $e');
    }
  }

  /// Create notification tables if they don't exist
  Future<void> _createNotificationTables() async {
    try {
      // This would typically be done via SQL migrations
      // For now, we'll assume the tables exist
      _logger.d('📋 Notification tables checked');
    } catch (e) {
      _logger.e('❌ Error creating notification tables: $e');
    }
  }

  /// Send notification via Supabase Edge Function
  Future<bool> sendNotification({
    required String userId,
    required NotificationType type,
    required String title,
    required String message,
    Map<String, dynamic>? data,
    bool immediate = false,
  }) async {
    try {
      // Check user's notification preferences
      final preferences = await getUserNotificationPreferences(userId);
      if (!_shouldSendNotification(type, preferences)) {
        _logger.d('🔕 Notification blocked by user preferences');
        return false;
      }

      // Prepare notification payload
      final payload = {
        'user_id': userId,
        'type': type.name,
        'title': title,
        'message': message,
        'data': data ?? {},
        'immediate': immediate,
        'created_at': DateTime.now().toIso8601String(),
      };

      // Store notification in database
      await _storeNotification(payload);

      // Send via Edge Function (if available)
      if (immediate) {
        await _sendPushNotification(payload);
      }

      _logger.i('✅ Notification sent: $title');
      return true;
    } catch (e) {
      _logger.e('❌ Error sending notification: $e');
      return false;
    }
  }

  /// Store notification in database
  Future<void> _storeNotification(Map<String, dynamic> payload) async {
    try {
      await _client.from('notifications').insert({
        'user_id': payload['user_id'],
        'type': payload['type'],
        'title': payload['title'],
        'message': payload['message'],
        'data': payload['data'],
        'is_read': false,
        'created_at': payload['created_at'],
      });
    } catch (e) {
      _logger.e('❌ Error storing notification: $e');
    }
  }

  /// Send push notification via Edge Function
  Future<void> _sendPushNotification(Map<String, dynamic> payload) async {
    try {
      // Call Supabase Edge Function for push notifications
      final response = await _client.functions.invoke(
        'send-push-notification',
        body: payload,
      );

      if (response.status == 200) {
        _logger.i('✅ Push notification sent successfully');
      } else {
        _logger.w('⚠️ Push notification failed: ${response.status}');
      }
    } catch (e) {
      _logger.e('❌ Error sending push notification: $e');
    }
  }

  /// Check if notification should be sent based on preferences
  bool _shouldSendNotification(
    NotificationType type,
    NotificationPreferences preferences,
  ) {
    if (!preferences.pushNotifications) return false;

    switch (type) {
      case NotificationType.communityPost:
      case NotificationType.communityReply:
        return preferences.communityUpdates;
      case NotificationType.workoutReminder:
        return preferences.workoutReminders;
      case NotificationType.studyGroupActivity:
        return preferences.studyGroupNotifications;
      case NotificationType.achievement:
        return preferences.achievementNotifications;
      case NotificationType.premiumUpdate:
        return preferences.premiumNotifications;
      case NotificationType.general:
        return true;
    }
  }

  /// Get user's notification preferences
  Future<NotificationPreferences> getUserNotificationPreferences(
    String userId,
  ) async {
    try {
      final response =
          await _client
              .from('users')
              .select('notification_preferences')
              .eq('id', userId)
              .single();

      if (response['notification_preferences'] != null) {
        return NotificationPreferences.fromJson(
          response['notification_preferences'],
        );
      }
    } catch (e) {
      _logger.e('❌ Error getting notification preferences: $e');
    }

    return const NotificationPreferences(); // Default preferences
  }

  /// Update user's notification preferences
  Future<bool> updateNotificationPreferences(
    String userId,
    NotificationPreferences preferences,
  ) async {
    try {
      await _client
          .from('users')
          .update({'notification_preferences': preferences.toJson()})
          .eq('id', userId);

      _logger.i('✅ Notification preferences updated');
      return true;
    } catch (e) {
      _logger.e('❌ Error updating notification preferences: $e');
      return false;
    }
  }

  /// Get user's notifications
  Future<List<Map<String, dynamic>>> getUserNotifications(
    String userId, {
    int limit = 50,
  }) async {
    try {
      final response = await _client
          .from('notifications')
          .select('*')
          .eq('user_id', userId)
          .order('created_at', ascending: false)
          .limit(limit);

      return List<Map<String, dynamic>>.from(response);
    } catch (e) {
      _logger.e('❌ Error getting notifications: $e');
      return [];
    }
  }

  /// Mark notification as read
  Future<bool> markAsRead(String notificationId) async {
    try {
      await _client
          .from('notifications')
          .update({
            'is_read': true,
            'read_at': DateTime.now().toIso8601String(),
          })
          .eq('id', notificationId);

      return true;
    } catch (e) {
      _logger.e('❌ Error marking notification as read: $e');
      return false;
    }
  }

  /// Mark all notifications as read for user
  Future<bool> markAllAsRead(String userId) async {
    try {
      await _client
          .from('notifications')
          .update({
            'is_read': true,
            'read_at': DateTime.now().toIso8601String(),
          })
          .eq('user_id', userId)
          .eq('is_read', false);

      return true;
    } catch (e) {
      _logger.e('❌ Error marking all notifications as read: $e');
      return false;
    }
  }

  /// Get unread notification count
  Future<int> getUnreadCount(String userId) async {
    try {
      final response = await _client
          .from('notifications')
          .select('id')
          .eq('user_id', userId)
          .eq('is_read', false);

      return response.length;
    } catch (e) {
      _logger.e('❌ Error getting unread count: $e');
      return 0;
    }
  }

  /// Delete notification
  Future<bool> deleteNotification(String notificationId) async {
    try {
      await _client.from('notifications').delete().eq('id', notificationId);

      return true;
    } catch (e) {
      _logger.e('❌ Error deleting notification: $e');
      return false;
    }
  }

  /// Send community post notification
  Future<void> sendCommunityPostNotification(
    String postId,
    String authorName,
    String postTitle,
  ) async {
    try {
      // Get all users who should receive community notifications
      final users = await _getUsersForCommunityNotifications();

      for (final user in users) {
        await sendNotification(
          userId: user['id'],
          type: NotificationType.communityPost,
          title: 'New Community Post',
          message: '$authorName posted: $postTitle',
          data: {'post_id': postId, 'author': authorName},
        );
      }
    } catch (e) {
      _logger.e('❌ Error sending community post notifications: $e');
    }
  }

  /// Send workout reminder notification
  Future<void> sendWorkoutReminder(
    String userId,
    String workoutName,
    DateTime scheduledTime,
  ) async {
    await sendNotification(
      userId: userId,
      type: NotificationType.workoutReminder,
      title: 'Workout Reminder',
      message: 'Time for your $workoutName workout!',
      data: {
        'workout_name': workoutName,
        'scheduled_time': scheduledTime.toIso8601String(),
      },
      immediate: true,
    );
  }

  /// Send achievement notification
  Future<void> sendAchievementNotification(
    String userId,
    String achievementName,
    String description,
  ) async {
    await sendNotification(
      userId: userId,
      type: NotificationType.achievement,
      title: 'Achievement Unlocked! 🏆',
      message: 'You earned: $achievementName',
      data: {'achievement': achievementName, 'description': description},
      immediate: true,
    );
  }

  /// Get users for community notifications
  Future<List<Map<String, dynamic>>>
  _getUsersForCommunityNotifications() async {
    try {
      final response = await _client
          .from('users')
          .select('id, notification_preferences')
          .neq('notification_preferences->community_updates', false);

      return List<Map<String, dynamic>>.from(response);
    } catch (e) {
      _logger.e('❌ Error getting users for community notifications: $e');
      return [];
    }
  }
}
