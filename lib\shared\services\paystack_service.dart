import 'package:flutter/material.dart';
import 'package:fit_4_force/core/config/app_config.dart';
import 'package:fit_4_force/shared/services/auth_service.dart';
import 'package:fit_4_force/shared/services/base_service.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:fit_4_force/shared/patches/paystack_wrapper.dart';

/// A real implementation of the Paystack service for payment processing
class PaystackService extends BaseService {
  final AuthService _authService = AuthService();
  bool _initialized = false;

  @override
  CollectionReference<Map<String, dynamic>> get collection =>
      firestore.collection('transactions');

  PaystackService() {
    _initializePaystack();
  }

  /// Initialize the Paystack plugin with the public key
  Future<void> _initializePaystack() async {
    if (!_initialized) {
      await PaystackWrapper.initialize(publicKey: AppConfig.paystackPublicKey);
      _initialized = true;
    }
  }

  /// Process a payment using Paystack
  ///
  /// Note: This method requires a BuildContext. The caller should ensure that
  /// the widget is still mounted before using the result of this method.
  ///
  /// The [context] parameter is used to show the Paystack checkout UI.
  /// The [isMounted] parameter is a function that returns whether the widget is still mounted.
  /// If provided, it will be called before and after async operations to ensure
  /// the widget is still mounted.
  Future<bool> processPayment({
    required BuildContext context,
    required String email,
    required String fullName,
    required double amount,
    bool isYearly = false,
    bool Function()? isMounted,
  }) async {
    try {
      // Ensure Paystack is initialized
      if (!_initialized) {
        await _initializePaystack();
      }

      // Check if the widget is still mounted before proceeding
      if (isMounted != null && !isMounted()) {
        return false;
      }

      // Generate a unique reference for this transaction
      final reference = 'FIT4FORCE_${DateTime.now().millisecondsSinceEpoch}';

      // Check if the widget is still mounted before showing the UI
      if (isMounted != null && !isMounted()) {
        return false;
      }

      // Process the payment using our custom wrapper
      final response = await PaystackWrapper.chargeCard(
        context: context,
        amount: (amount * 100).toInt(), // Convert to kobo
        email: email,
        reference: reference,
      );

      // Check if the widget is still mounted after the checkout process
      if (isMounted != null && !isMounted()) {
        return false;
      }

      if (response['status'] == 'success' && response['reference'] != null) {
        // Payment was successful
        await _handleSuccessfulPayment(response['reference'], isYearly: isYearly);
        return true;
      } else {
        // Payment failed
        return false;
      }
    } catch (e) {
      debugPrint('Paystack error: $e');
      return false;
    }
  }

  /// Handle a successful payment
  Future<void> _handleSuccessfulPayment(
    String reference, {
    bool isYearly = false,
  }) async {
    if (currentUserId == null) return;

    try {
      // Since getTransaction is not available in the current Paystack plugin version,
      // we'll assume the transaction is valid if we got this far
      final expiryDate = DateTime.now().add(
        Duration(days: isYearly ? 365 : 30),
      );

      // Update user's premium status
      await _authService.updatePremiumStatus(currentUserId!, true, expiryDate);

      // Create a subscription record
      await firestore.collection('subscriptions').add({
        'userId': currentUserId,
        'reference': reference,
        'amount':
            isYearly
                ? AppConfig.premiumSubscriptionPrice *
                    10 // 10 months for yearly (2 months free)
                : AppConfig.premiumSubscriptionPrice,
        'startDate': DateTime.now().toIso8601String(),
        'expiryDate': expiryDate.toIso8601String(),
        'status': 'active',
        'plan': isYearly ? 'yearly' : 'monthly',
        'createdAt': DateTime.now().toIso8601String(),
      });
    } catch (e) {
      debugPrint('Error processing successful payment: $e');
    }
  }

  /// Verify a transaction
  Future<bool> verifyTransaction(String reference) async {
    try {
      // Since getTransaction is not available in the current Paystack plugin version,
      // we'll use a workaround by checking if the transaction exists in our database
      final querySnapshot =
          await collection
              .where('reference', isEqualTo: reference)
              .limit(1)
              .get();

      return querySnapshot.docs.isNotEmpty;
    } catch (e) {
      debugPrint('Error verifying transaction: $e');
      return false;
    }
  }
}
