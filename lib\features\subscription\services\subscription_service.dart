import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:fit_4_force/core/config/app_config.dart';
import 'package:logger/logger.dart';

class SubscriptionService {
  final Logger _logger = Logger();
  final FirebaseFirestore _firestore = FirebaseFirestore.instance;

  Future<bool> checkSubscriptionStatus(String userId) async {
    try {
      final userDoc = await _firestore.collection('users').doc(userId).get();
      final userData = userDoc.data();

      if (userData == null) return false;

      final isPremium = userData['isPremium'] ?? false;
      final subscriptionEndDate = userData['subscriptionEndDate'];

      if (!isPremium) return false;

      if (subscriptionEndDate == null) return false;

      final endDate = (subscriptionEndDate as Timestamp).toDate();
      return DateTime.now().isBefore(endDate);
    } catch (e) {
      _logger.e('Error checking subscription status: $e');
      return false;
    }
  }

  Future<bool> activateSubscription({
    required String userId,
    required String paymentReference,
  }) async {
    try {
      final subscriptionEndDate = DateTime.now().add(const Duration(days: 30));

      await _firestore.collection('users').doc(userId).update({
        'isPremium': true,
        'subscriptionStartDate': FieldValue.serverTimestamp(),
        'subscriptionEndDate': Timestamp.fromDate(subscriptionEndDate),
        'lastPaymentReference': paymentReference,
        'subscriptionHistory': FieldValue.arrayUnion([
          {
            'startDate': FieldValue.serverTimestamp(),
            'endDate': Timestamp.fromDate(subscriptionEndDate),
            'paymentReference': paymentReference,
            'amount': AppConfig.premiumSubscriptionPrice,
            'status': 'active',
          },
        ]),
      });

      return true;
    } catch (e) {
      _logger.e('Error activating subscription: $e');
      return false;
    }
  }

  Future<bool> cancelSubscription(String userId) async {
    try {
      await _firestore.collection('users').doc(userId).update({
        'isPremium': false,
        'subscriptionEndDate': FieldValue.serverTimestamp(),
      });

      return true;
    } catch (e) {
      _logger.e('Error canceling subscription: $e');
      return false;
    }
  }

  Future<Map<String, dynamic>?> getSubscriptionDetails(String userId) async {
    try {
      final userDoc = await _firestore.collection('users').doc(userId).get();
      final userData = userDoc.data();

      if (userData == null) return null;

      return {
        'isPremium': userData['isPremium'] ?? false,
        'subscriptionStartDate': userData['subscriptionStartDate'],
        'subscriptionEndDate': userData['subscriptionEndDate'],
        'lastPaymentReference': userData['lastPaymentReference'],
        'subscriptionHistory': userData['subscriptionHistory'] ?? [],
      };
    } catch (e) {
      _logger.e('Error getting subscription details: $e');
      return null;
    }
  }
}
