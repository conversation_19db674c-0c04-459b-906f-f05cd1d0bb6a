import 'dart:async';
import 'dart:convert';
import 'dart:io';

import 'package:fit_4_force/core/config/api_environment.dart';
import 'package:fit_4_force/core/exceptions/api_exception.dart';
import 'package:flutter/foundation.dart';
import 'package:http/http.dart' as http;

/// Base API service for handling HTTP requests
class ApiService {
  /// HTTP client
  final http.Client _client;

  /// Constructor
  ApiService({http.Client? client}) : _client = client ?? http.Client();

  /// Base URL for API requests
  String get baseUrl => ApiConfig.baseUrl;

  /// Default headers for API requests
  Map<String, String> get defaultHeaders => ApiConfig.headers;

  /// Timeout duration for API requests
  Duration get timeout => Duration(milliseconds: ApiConfig.timeout);

  /// Make a GET request
  Future<dynamic> get(
    String endpoint, {
    Map<String, String>? headers,
    Map<String, dynamic>? queryParams,
    String? token,
  }) async {
    try {
      final uri = _buildUri(endpoint, queryParams);
      final requestHeaders = _buildHeaders(headers, token);

      final response = await _client
          .get(uri, headers: requestHeaders)
          .timeout(timeout);

      return _handleResponse(response);
    } on SocketException {
      throw ApiException('No internet connection');
    } on http.ClientException {
      throw ApiException('Failed to connect to server');
    } on TimeoutException {
      throw ApiException('Request timed out');
    } catch (e) {
      throw ApiException('An unexpected error occurred: ${e.toString()}');
    }
  }

  /// Make a POST request
  Future<dynamic> post(
    String endpoint, {
    Map<String, String>? headers,
    Map<String, dynamic>? queryParams,
    dynamic body,
    String? token,
  }) async {
    try {
      final uri = _buildUri(endpoint, queryParams);
      final requestHeaders = _buildHeaders(headers, token);

      final response = await _client
          .post(
            uri,
            headers: requestHeaders,
            body: body != null ? jsonEncode(body) : null,
          )
          .timeout(timeout);

      return _handleResponse(response);
    } on SocketException {
      throw ApiException('No internet connection');
    } on http.ClientException {
      throw ApiException('Failed to connect to server');
    } on TimeoutException {
      throw ApiException('Request timed out');
    } catch (e) {
      throw ApiException('An unexpected error occurred: ${e.toString()}');
    }
  }

  /// Make a PUT request
  Future<dynamic> put(
    String endpoint, {
    Map<String, String>? headers,
    Map<String, dynamic>? queryParams,
    dynamic body,
    String? token,
  }) async {
    try {
      final uri = _buildUri(endpoint, queryParams);
      final requestHeaders = _buildHeaders(headers, token);

      final response = await _client
          .put(
            uri,
            headers: requestHeaders,
            body: body != null ? jsonEncode(body) : null,
          )
          .timeout(timeout);

      return _handleResponse(response);
    } on SocketException {
      throw ApiException('No internet connection');
    } on http.ClientException {
      throw ApiException('Failed to connect to server');
    } on TimeoutException {
      throw ApiException('Request timed out');
    } catch (e) {
      throw ApiException('An unexpected error occurred: ${e.toString()}');
    }
  }

  /// Make a DELETE request
  Future<dynamic> delete(
    String endpoint, {
    Map<String, String>? headers,
    Map<String, dynamic>? queryParams,
    dynamic body,
    String? token,
  }) async {
    try {
      final uri = _buildUri(endpoint, queryParams);
      final requestHeaders = _buildHeaders(headers, token);

      final response = await _client
          .delete(
            uri,
            headers: requestHeaders,
            body: body != null ? jsonEncode(body) : null,
          )
          .timeout(timeout);

      return _handleResponse(response);
    } on SocketException {
      throw ApiException('No internet connection');
    } on http.ClientException {
      throw ApiException('Failed to connect to server');
    } on TimeoutException {
      throw ApiException('Request timed out');
    } catch (e) {
      throw ApiException('An unexpected error occurred: ${e.toString()}');
    }
  }

  /// Build URI for API requests
  Uri _buildUri(String endpoint, Map<String, dynamic>? queryParams) {
    final apiPath = endpoint.startsWith('/') ? endpoint : '/$endpoint';
    final url = '$baseUrl$apiPath';

    if (queryParams != null) {
      return Uri.parse(url).replace(
        queryParameters: queryParams.map(
          (key, value) => MapEntry(key, value.toString()),
        ),
      );
    }

    return Uri.parse(url);
  }

  /// Build headers for API requests
  Map<String, String> _buildHeaders(
    Map<String, String>? headers,
    String? token,
  ) {
    final Map<String, String> requestHeaders = {...defaultHeaders};

    if (headers != null) {
      requestHeaders.addAll(headers);
    }

    if (token != null) {
      requestHeaders['Authorization'] = 'Bearer $token';
    }

    return requestHeaders;
  }

  /// Handle API response
  dynamic _handleResponse(http.Response response) {
    final statusCode = response.statusCode;
    final responseBody = _parseResponseBody(response);

    if (statusCode >= 200 && statusCode < 300) {
      return responseBody;
    } else {
      final message = responseBody is Map ? responseBody['message'] : null;
      throw ApiException(
        message ?? 'Request failed with status code: $statusCode',
        statusCode: statusCode,
        data: responseBody,
      );
    }
  }

  /// Parse response body
  dynamic _parseResponseBody(http.Response response) {
    if (response.body.isEmpty) {
      return null;
    }

    try {
      return jsonDecode(response.body);
    } catch (e) {
      if (kDebugMode) {
        print('Failed to parse response body: ${e.toString()}');
      }
      return response.body;
    }
  }
}
