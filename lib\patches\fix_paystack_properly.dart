import 'dart:io';
import 'package:logger/logger.dart';

void main() async {
  final logger = Logger();

  logger.i('Running comprehensive Flutter Paystack fixes...');

  final pubCachePath =
      Platform.isWindows
          ? '${Platform.environment['LOCALAPPDATA']}\\Pub\\Cache'
          : '${Platform.environment['HOME']}/.pub-cache';

  final paystackPath =
      '$pubCachePath\\git\\flutter_paystack-a4a33c3dd0a12f46d655a2e63d11e9f20ba82d01';

  // Check if the directory exists
  final paystackDir = Directory(paystackPath);
  if (!paystackDir.existsSync()) {
    logger.w('Flutter Paystack package not found at $paystackPath');
    return;
  }

  logger.i('Found Flutter Paystack package at $paystackPath');

  // Fix checkout_widget.dart
  final checkoutWidgetPath =
      '$paystackPath\\lib\\src\\widgets\\checkout\\checkout_widget.dart';
  final checkoutWidgetFile = File(checkoutWidgetPath);

  if (checkoutWidgetFile.existsSync()) {
    logger.i('Found checkout_widget.dart at $checkoutWidgetPath');

    // Create backup
    final backupPath = '$checkoutWidgetPath.bak2';
    checkoutWidgetFile.copySync(backupPath);
    logger.i('Created backup at $backupPath');

    // Read file content
    var content = checkoutWidgetFile.readAsStringSync();

    // Fix TabController
    content = content.replaceAll(
      '_tabController = new TabController(',
      '_tabController = new TabController(vsync: this,',
    );

    // Fix AnimationController
    content = content.replaceAll(
      '_animationController = new AnimationController(',
      '_animationController = new AnimationController(vsync: this,',
    );

    // Fix bodyText1
    content = content.replaceAll('bodyText1', 'bodyLarge');

    // Fix accentColor properly
    content = content.replaceAll(
      'final accentColor = Theme.of(context).accentColor;',
      'final accentColor = Theme.of(context).colorScheme.secondary;',
    );

    // Fix buildCheckoutMethods
    content = content.replaceAll(
      'Widget buildCheckoutMethods(Color accentColor) {',
      'Widget buildCheckoutMethods(Color accentColor) {',
    );

    // Write fixed content
    checkoutWidgetFile.writeAsStringSync(content);
    logger.i('Fixed checkout_widget.dart');
  } else {
    logger.w('File not found: $checkoutWidgetPath');
  }

  // Fix buttons.dart
  final buttonsPath = '$paystackPath\\lib\\src\\widgets\\buttons.dart';
  final buttonsFile = File(buttonsPath);

  if (buttonsFile.existsSync()) {
    logger.i('Found buttons.dart at $buttonsPath');

    // Create backup
    final backupPath = '$buttonsPath.bak2';
    buttonsFile.copySync(backupPath);
    logger.i('Created backup at $backupPath');

    // Read file content
    var content = buttonsFile.readAsStringSync();

    // Fix accentColor properly
    content = content.replaceAll(
      'color: Theme.of(context).accentColor,',
      'color: Theme.of(context).colorScheme.secondary,',
    );

    // Fix copyWith
    content = content.replaceAll(
      '.copyWith(accentColor: Colors.white)',
      '.copyWith(colorScheme: ColorScheme.dark(secondary: Colors.white))',
    );

    // Write fixed content
    buttonsFile.writeAsStringSync(content);
    logger.i('Fixed buttons.dart');
  } else {
    logger.w('File not found: $buttonsPath');
  }

  // Fix successful_widget.dart
  final successfulWidgetPath =
      '$paystackPath\\lib\\src\\widgets\\sucessful_widget.dart';
  final successfulWidgetFile = File(successfulWidgetPath);

  if (successfulWidgetFile.existsSync()) {
    logger.i('Found sucessful_widget.dart at $successfulWidgetPath');

    // Create backup
    final backupPath = '$successfulWidgetPath.bak2';
    successfulWidgetFile.copySync(backupPath);
    logger.i('Created backup at $backupPath');

    // Read file content
    var content = successfulWidgetFile.readAsStringSync();

    // Fix AnimationController
    content = content.replaceAll(
      '_mainController = new AnimationController(',
      '_mainController = new AnimationController(vsync: this,',
    );

    content = content.replaceAll(
      '_countdownController = new AnimationController(',
      '_countdownController = new AnimationController(vsync: this,',
    );

    content = content.replaceAll(
      '_opacityController = new AnimationController(',
      '_opacityController = new AnimationController(vsync: this,',
    );

    // Fix accentColor properly
    content = content.replaceAll(
      'final accentColor = Theme.of(context).accentColor;',
      'final accentColor = Theme.of(context).colorScheme.secondary;',
    );

    // Write fixed content
    successfulWidgetFile.writeAsStringSync(content);
    logger.i('Fixed sucessful_widget.dart');
  } else {
    logger.w('File not found: $successfulWidgetPath');
  }

  // Fix bank_checkout.dart
  final bankCheckoutPath =
      '$paystackPath\\lib\\src\\widgets\\checkout\\bank_checkout.dart';
  final bankCheckoutFile = File(bankCheckoutPath);

  if (bankCheckoutFile.existsSync()) {
    logger.i('Found bank_checkout.dart at $bankCheckoutPath');

    // Create backup
    final backupPath = '$bankCheckoutPath.bak2';
    bankCheckoutFile.copySync(backupPath);
    logger.i('Created backup at $backupPath');

    // Read file content
    var content = bankCheckoutFile.readAsStringSync();

    // Fix AnimationController
    content = content.replaceAll(
      '_controller = new AnimationController(',
      '_controller = new AnimationController(vsync: this,',
    );

    // Fix accentColor properly
    content = content.replaceAll(
      'color: Theme.of(context).accentColor,',
      'color: Theme.of(context).colorScheme.secondary,',
    );

    // Write fixed content
    bankCheckoutFile.writeAsStringSync(content);
    logger.i('Fixed bank_checkout.dart');
  } else {
    logger.w('File not found: $bankCheckoutPath');
  }

  // Fix error_widget.dart
  final errorWidgetPath = '$paystackPath\\lib\\src\\widgets\\error_widget.dart';
  final errorWidgetFile = File(errorWidgetPath);

  if (errorWidgetFile.existsSync()) {
    logger.i('Found error_widget.dart at $errorWidgetPath');

    // Create backup
    final backupPath = '$errorWidgetPath.bak2';
    errorWidgetFile.copySync(backupPath);
    logger.i('Created backup at $backupPath');

    // Read file content
    var content = errorWidgetFile.readAsStringSync();

    // Fix constructor
    if (content.contains('ErrorWidget({')) {
      content = content.replaceAll(
        'ErrorWidget({',
        'ErrorWidget({required this.vSync,',
      );
    }

    // Write fixed content
    errorWidgetFile.writeAsStringSync(content);
    logger.i('Fixed error_widget.dart');
  } else {
    logger.w('File not found: $errorWidgetPath');
  }

  logger.i('All fixes applied successfully!');
  logger.i('Run "flutter pub get" to update the package.');
}
