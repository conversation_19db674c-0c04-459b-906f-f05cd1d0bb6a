import 'package:flutter/material.dart';

/// Model representing a study material
class StudyMaterialModel {
  final String id;
  final String title;
  final String description;
  final String category;
  final String agency;
  final String contentType; // 'document', 'quiz', 'video', 'interactive'
  final String contentUrl;
  final DateTime publishedDate;
  final bool isPremium;
  final IconData icon;
  final Color color;
  final int estimatedReadTime; // in minutes
  final List<String> tags;
  final String? difficulty; // 'beginner', 'intermediate', 'advanced'
  final double rating; // Average rating (0.0 - 5.0)
  final int totalRatings; // Number of ratings
  final int viewCount; // Number of views
  final int downloadCount; // Number of downloads

  const StudyMaterialModel({
    required this.id,
    required this.title,
    required this.description,
    required this.category,
    required this.agency,
    required this.contentType,
    required this.contentUrl,
    required this.publishedDate,
    required this.isPremium,
    required this.icon,
    required this.color,
    required this.estimatedReadTime,
    required this.tags,
    this.difficulty,
    this.rating = 0.0,
    this.totalRatings = 0,
    this.viewCount = 0,
    this.downloadCount = 0,
  });

  // Create a copy of the study material with modified properties
  StudyMaterialModel copyWith({
    String? id,
    String? title,
    String? description,
    String? category,
    String? agency,
    String? contentType,
    String? contentUrl,
    DateTime? publishedDate,
    bool? isPremium,
    IconData? icon,
    Color? color,
    int? estimatedReadTime,
    List<String>? tags,
    String? difficulty,
    double? rating,
    int? totalRatings,
    int? viewCount,
    int? downloadCount,
  }) {
    return StudyMaterialModel(
      id: id ?? this.id,
      title: title ?? this.title,
      description: description ?? this.description,
      category: category ?? this.category,
      agency: agency ?? this.agency,
      contentType: contentType ?? this.contentType,
      contentUrl: contentUrl ?? this.contentUrl,
      publishedDate: publishedDate ?? this.publishedDate,
      isPremium: isPremium ?? this.isPremium,
      icon: icon ?? this.icon,
      color: color ?? this.color,
      estimatedReadTime: estimatedReadTime ?? this.estimatedReadTime,
      tags: tags ?? this.tags,
      difficulty: difficulty ?? this.difficulty,
      rating: rating ?? this.rating,
      totalRatings: totalRatings ?? this.totalRatings,
      viewCount: viewCount ?? this.viewCount,
      downloadCount: downloadCount ?? this.downloadCount,
    );
  }

  // Convert study material to map
  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'title': title,
      'description': description,
      'category': category,
      'agency': agency,
      'contentType': contentType,
      'contentUrl': contentUrl,
      'publishedDate': publishedDate.toIso8601String(),
      'isPremium': isPremium,
      'estimatedReadTime': estimatedReadTime,
      'tags': tags,
    };
  }

  // Create study material from map
  factory StudyMaterialModel.fromMap(Map<String, dynamic> map) {
    return StudyMaterialModel(
      id: map['id'],
      title: map['title'],
      description: map['description'],
      category: map['category'],
      agency: map['agency'],
      contentType: map['contentType'],
      contentUrl: map['contentUrl'],
      publishedDate: DateTime.parse(map['publishedDate']),
      isPremium: map['isPremium'] ?? false,
      icon: _getIconForContentType(map['contentType']),
      color: _getColorForCategory(map['category']),
      estimatedReadTime: map['estimatedReadTime'] ?? 10,
      tags: List<String>.from(map['tags'] ?? []),
    );
  }

  // Get icon based on content type
  static IconData _getIconForContentType(String contentType) {
    switch (contentType) {
      case 'pdf':
        return Icons.picture_as_pdf;
      case 'text':
        return Icons.article;
      case 'video':
        return Icons.video_library;
      case 'image':
        return Icons.image;
      default:
        return Icons.description;
    }
  }

  // Get color based on category
  static Color _getColorForCategory(String category) {
    switch (category) {
      case 'History':
        return Colors.brown;
      case 'Mathematics':
        return Colors.blue;
      case 'English':
        return Colors.green;
      case 'Current Affairs':
        return Colors.orange;
      case 'General Knowledge':
        return Colors.purple;
      case 'Physical Training':
        return Colors.red;
      default:
        return Colors.teal;
    }
  }
}

/// Model representing a study material category
class StudyMaterialCategory {
  final String id;
  final String name;
  final String description;
  final IconData icon;
  final Color color;
  final int materialCount;

  const StudyMaterialCategory({
    required this.id,
    required this.name,
    required this.description,
    required this.icon,
    required this.color,
    required this.materialCount,
  });

  // Create a copy of the category with modified properties
  StudyMaterialCategory copyWith({
    String? id,
    String? name,
    String? description,
    IconData? icon,
    Color? color,
    int? materialCount,
  }) {
    return StudyMaterialCategory(
      id: id ?? this.id,
      name: name ?? this.name,
      description: description ?? this.description,
      icon: icon ?? this.icon,
      color: color ?? this.color,
      materialCount: materialCount ?? this.materialCount,
    );
  }
}
