import 'package:flutter/material.dart';
import 'package:fit_4_force/features/ai/services/ai_features_manager.dart';
import 'package:fit_4_force/core/theme/app_theme.dart';

class SmartLearningAssistantScreen extends StatefulWidget {
  final String targetAgency;

  const SmartLearningAssistantScreen({super.key, required this.targetAgency});

  @override
  _SmartLearningAssistantScreenState createState() =>
      _SmartLearningAssistantScreenState();
}

class _SmartLearningAssistantScreenState
    extends State<SmartLearningAssistantScreen> {
  final TextEditingController _questionController = TextEditingController();
  final ScrollController _scrollController = ScrollController();
  final AIFeaturesManager _aiManager = AIFeaturesManager();

  final List<Map<String, dynamic>> _chatHistory = [];
  bool _isLoading = false;

  @override
  void initState() {
    super.initState();
    _initializeAI();
  }

  Future<void> _initializeAI() async {
    setState(() {
      _isLoading = true;
    });

    try {
      await _aiManager.initialize(learningOnly: true);

      // Add welcome message
      _addMessage({
        'isUser': false,
        'content':
            'Hello! I\'m your Smart Learning Assistant for ${widget.targetAgency} preparation. How can I help you today?',
        'timestamp': DateTime.now(),
      });

      // Add suggested questions
      _addMessage({
        'isUser': false,
        'content':
            'You can ask me about military concepts, regulations, or request study materials.',
        'suggestions': [
          'Explain military ranks in ${widget.targetAgency}',
          'Help me prepare for the physical fitness test',
          'Create a study plan for the entrance exam',
          'Quiz me on military protocols',
        ],
        'timestamp': DateTime.now(),
      });
    } catch (e) {
      _addMessage({
        'isUser': false,
        'content': 'Sorry, I had trouble initializing. Please try again later.',
        'error': e.toString(),
        'timestamp': DateTime.now(),
      });
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  void _addMessage(Map<String, dynamic> message) {
    setState(() {
      _chatHistory.add(message);
    });

    // Scroll to bottom after message is added
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (_scrollController.hasClients) {
        _scrollController.animateTo(
          _scrollController.position.maxScrollExtent,
          duration: Duration(milliseconds: 300),
          curve: Curves.easeOut,
        );
      }
    });
  }

  Future<void> _sendQuestion(String question) async {
    if (question.trim().isEmpty) return;

    // Clear input field
    _questionController.clear();

    // Add user message to chat
    _addMessage({
      'isUser': true,
      'content': question,
      'timestamp': DateTime.now(),
    });

    setState(() {
      _isLoading = true;
    });

    try {
      // Get user context from chat history
      String userContext = _generateUserContext();

      // Process question
      final response = await _aiManager.askQuestion(question, userContext);

      // Add AI response to chat
      _addMessage({
        'isUser': false,
        'content': response['content'],
        'responseType': response['responseType'],
        'suggestions': response['suggestions'],
        'plan': response['plan'],
        'questions': response['questions'],
        'relatedTopics': response['relatedTopics'],
        'timestamp': DateTime.now(),
      });
    } catch (e) {
      _addMessage({
        'isUser': false,
        'content': 'Sorry, I encountered an error. Please try again.',
        'error': e.toString(),
        'timestamp': DateTime.now(),
      });
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  String _generateUserContext() {
    // Extract recent user questions and topics
    List<String> recentQuestions = [];

    for (var message in _chatHistory.reversed) {
      if (message['isUser'] == true && recentQuestions.length < 5) {
        recentQuestions.add(message['content']);
      }
    }

    return recentQuestions.join(' ');
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text('Smart Learning Assistant'),
        backgroundColor: AppTheme.primaryColor,
      ),
      body: Column(
        children: [
          // Chat messages area
          Expanded(
            child:
                _chatHistory.isEmpty
                    ? Center(
                      child: Text(
                        'Your learning assistant is getting ready...',
                        style: TextStyle(color: Colors.grey),
                      ),
                    )
                    : ListView.builder(
                      controller: _scrollController,
                      padding: EdgeInsets.all(16),
                      itemCount: _chatHistory.length,
                      itemBuilder: (context, index) {
                        final message = _chatHistory[index];
                        return _buildMessageWidget(message);
                      },
                    ),
          ),

          // Loading indicator
          if (_isLoading)
            Padding(
              padding: const EdgeInsets.all(8.0),
              child: LinearProgressIndicator(),
            ),

          // Input area
          Container(
            padding: EdgeInsets.all(8),
            decoration: BoxDecoration(
              color: Colors.white,
              boxShadow: [
                BoxShadow(
                  color: Colors.black12,
                  offset: Offset(0, -1),
                  blurRadius: 4,
                ),
              ],
            ),
            child: Row(
              children: [
                Expanded(
                  child: TextField(
                    controller: _questionController,
                    decoration: InputDecoration(
                      hintText: 'Ask a question...',
                      border: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(24),
                        borderSide: BorderSide.none,
                      ),
                      filled: true,
                      fillColor: Colors.grey[100],
                      contentPadding: EdgeInsets.symmetric(
                        horizontal: 16,
                        vertical: 8,
                      ),
                    ),
                    onSubmitted: _sendQuestion,
                  ),
                ),
                SizedBox(width: 8),
                FloatingActionButton(
                  onPressed: () => _sendQuestion(_questionController.text),
                  backgroundColor: AppTheme.primaryColor,
                  mini: true,
                  child: Icon(Icons.send),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildMessageWidget(Map<String, dynamic> message) {
    final isUser = message['isUser'] ?? false;
    final content = message['content'] ?? '';

    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 8.0),
      child: Row(
        mainAxisAlignment:
            isUser ? MainAxisAlignment.end : MainAxisAlignment.start,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          if (!isUser) _buildAvatarWidget(),
          SizedBox(width: 8),
          Flexible(
            child: Container(
              padding: EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: isUser ? AppTheme.primaryColor : Colors.grey[200],
                borderRadius: BorderRadius.circular(16),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    content,
                    style: TextStyle(
                      color: isUser ? Colors.white : Colors.black87,
                    ),
                  ),
                  if (!isUser && message['suggestions'] != null)
                    _buildSuggestions(message['suggestions']),
                  if (!isUser && message['plan'] != null)
                    _buildPlan(message['plan']),
                  if (!isUser && message['questions'] != null)
                    _buildQuiz(message['questions']),
                  if (!isUser && message['relatedTopics'] != null)
                    _buildRelatedTopics(message['relatedTopics']),
                ],
              ),
            ),
          ),
          SizedBox(width: 8),
          if (isUser) _buildUserAvatarWidget(),
        ],
      ),
    );
  }

  Widget _buildAvatarWidget() {
    return CircleAvatar(
      backgroundColor: AppTheme.primaryColor,
      child: Icon(Icons.school, color: Colors.white),
    );
  }

  Widget _buildUserAvatarWidget() {
    return CircleAvatar(
      backgroundColor: Colors.grey[300],
      child: Icon(Icons.person, color: Colors.grey[700]),
    );
  }

  Widget _buildSuggestions(List<dynamic> suggestions) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        SizedBox(height: 8),
        Text(
          'Suggestions:',
          style: TextStyle(fontWeight: FontWeight.bold, color: Colors.black87),
        ),
        SizedBox(height: 4),
        Wrap(
          spacing: 8,
          runSpacing: 8,
          children:
              suggestions.map((suggestion) {
                return InkWell(
                  onTap: () => _sendQuestion(suggestion),
                  child: Container(
                    padding: EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                    decoration: BoxDecoration(
                      color: AppTheme.primaryColor.withValues(alpha: 0.1),
                      borderRadius: BorderRadius.circular(16),
                      border: Border.all(
                        color: AppTheme.primaryColor.withValues(alpha: 0.3),
                      ),
                    ),
                    child: Text(
                      suggestion,
                      style: TextStyle(
                        color: AppTheme.primaryColor,
                        fontSize: 12,
                      ),
                    ),
                  ),
                );
              }).toList(),
        ),
      ],
    );
  }

  Widget _buildPlan(List<dynamic> plan) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        SizedBox(height: 8),
        Text(
          'Study Plan:',
          style: TextStyle(fontWeight: FontWeight.bold, color: Colors.black87),
        ),
        SizedBox(height: 4),
        ...plan.map((item) {
          return Padding(
            padding: const EdgeInsets.only(bottom: 4.0),
            child: Row(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Icon(
                  Icons.check_circle,
                  size: 16,
                  color: AppTheme.primaryColor,
                ),
                SizedBox(width: 4),
                Expanded(child: Text(item)),
              ],
            ),
          );
        }),
      ],
    );
  }

  Widget _buildQuiz(List<dynamic> questions) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        SizedBox(height: 8),
        Text(
          'Quiz:',
          style: TextStyle(fontWeight: FontWeight.bold, color: Colors.black87),
        ),
        SizedBox(height: 4),
        ...questions.map((question) {
          return Padding(
            padding: const EdgeInsets.only(bottom: 8.0),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  question['question'],
                  style: TextStyle(fontWeight: FontWeight.w500),
                ),
                SizedBox(height: 4),
                ...question['options'].map((option) {
                  bool isCorrect = option == question['answer'];
                  return Padding(
                    padding: const EdgeInsets.only(bottom: 2.0),
                    child: Row(
                      children: [
                        Icon(
                          isCorrect
                              ? Icons.check_circle
                              : Icons.circle_outlined,
                          size: 16,
                          color: isCorrect ? Colors.green : Colors.grey,
                        ),
                        SizedBox(width: 4),
                        Text(option),
                      ],
                    ),
                  );
                }).toList(),
              ],
            ),
          );
        }),
      ],
    );
  }

  Widget _buildRelatedTopics(List<dynamic> topics) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        SizedBox(height: 8),
        Text(
          'Related Topics:',
          style: TextStyle(fontWeight: FontWeight.bold, color: Colors.black87),
        ),
        SizedBox(height: 4),
        Wrap(
          spacing: 8,
          runSpacing: 8,
          children:
              topics.map((topic) {
                return InkWell(
                  onTap: () => _sendQuestion('Tell me about $topic'),
                  child: Container(
                    padding: EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                    decoration: BoxDecoration(
                      color: Colors.blue.withValues(alpha: 0.1),
                      borderRadius: BorderRadius.circular(16),
                      border: Border.all(
                        color: Colors.blue.withValues(alpha: 0.3),
                      ),
                    ),
                    child: Text(
                      topic,
                      style: TextStyle(color: Colors.blue, fontSize: 12),
                    ),
                  ),
                );
              }).toList(),
        ),
      ],
    );
  }

  @override
  void dispose() {
    _questionController.dispose();
    _scrollController.dispose();
    super.dispose();
  }
}
