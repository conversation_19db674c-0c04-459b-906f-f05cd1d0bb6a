/// Exception thrown when an API request fails
class ApiException implements Exception {
  /// Error message
  final String message;

  /// HTTP status code
  final int? statusCode;

  /// Additional error data
  final dynamic data;

  /// Constructor
  ApiException(
    this.message, {
    this.statusCode,
    this.data,
  });

  @override
  String toString() {
    return 'ApiException: $message${statusCode != null ? ' (Status code: $statusCode)' : ''}';
  }
}
