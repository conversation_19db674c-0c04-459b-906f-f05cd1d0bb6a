import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:flutter/foundation.dart' show kIsWeb;

class AppTheme {
  // Primary Colors
  static const Color primaryColor = Color(0xFF6C63FF); // Purple
  static const Color primaryLightColor = Color(0xFF9D97FF);
  static const Color primaryDarkColor = Color(0xFF4A42C8);

  // Secondary Colors
  static const Color secondaryColor = Color(0xFF5E35B1); // Deep Purple
  static const Color secondaryLightColor = Color(0xFF9162E4);
  static const Color secondaryDarkColor = Color(0xFF3B1C80);

  // Accent Colors
  static const Color accentColor = Color(0xFF3F51B5); // Indigo
  static const Color accentLightColor = Color(0xFF757DE8);
  static const Color accentDarkColor = Color(0xFF002984);

  // Background Colors
  static const Color backgroundLight = Color(0xFFF8F9FF); // Light purple tint
  static const Color backgroundDark = Color(0xFF121225); // Dark purple tint

  // Text Colors - Enhanced for accessibility
  static const Color textPrimaryLight = Color(
    0xFF1A1A2E,
  ); // Very dark for high contrast
  static const Color textSecondaryLight = Color(
    0xFF4A4A6A,
  ); // Medium dark for good contrast
  static const Color textPrimaryDark = Color(
    0xFFFAFAFF,
  ); // Very light for dark backgrounds
  static const Color textSecondaryDark = Color(
    0xFFE0E0F0,
  ); // Light for dark backgrounds

  // Accessible text colors for specific backgrounds
  static const Color textOnPrimary = Colors.white; // White on blue
  static const Color textOnSecondary = Colors.white; // White on purple
  static const Color textOnAccent = Colors.white; // White on indigo
  static const Color textOnSuccess = Colors.white; // White on green
  static const Color textOnWarning = Color(0xFF1A1A1A); // Dark on yellow
  static const Color textOnError = Colors.white; // White on red
  static const Color textOnPremium = Color(0xFF1A1A1A); // Dark on gold

  // Status Colors
  static const Color successColor = Color(0xFF4CAF50); // Green
  static const Color errorColor = Color(0xFFE53935); // Red
  static const Color warningColor = Color(0xFFFFC107); // Amber
  static const Color infoColor = Color(0xFF6C63FF); // Purple

  // Premium Colors
  static const Color premiumColor = Color(0xFF9C27B0); // Purple
  static const Color premiumDarkColor = Color(0xFF6A1B9A); // Dark Purple

  // Agency Colors
  static const Map<String, Color> agencyColors = {
    'Nigerian Army': Color(0xFF4CAF50), // Green
    'Navy': Color(0xFF1565C0), // Navy Blue
    'Air Force': Color(0xFF3F51B5), // Indigo
    'DSSC': Color(0xFF9C27B0), // Purple
    'NDA': Color(0xFF795548), // Brown
    'NSCDC': Color(0xFF607D8B), // Blue Grey
    'EFCC': Color(0xFFE91E63), // Pink
    'Fire Service': Color(0xFFF44336), // Red
    'Immigration': Color(0xFF009688), // Teal
    'Customs': Color(0xFF673AB7), // Deep Purple
    'FRSC': Color(0xFF2196F3), // Blue
    'Police (POLAC)': Color(0xFF212121), // Dark Grey
  };

  // Standard Font Sizes
  static const double fontSizeHeadingLarge = 24.0;
  static const double fontSizeHeadingMedium = 20.0;
  static const double fontSizeHeadingSmall = 18.0;
  static const double fontSizeTitle = 16.0;
  static const double fontSizeSubtitle = 14.0;
  static const double fontSizeBody = 14.0;
  static const double fontSizeCaption = 12.0;
  static const double fontSizeSmall = 10.0;

  // Standard Font Weights
  static const FontWeight fontWeightBold = FontWeight.w700;
  static const FontWeight fontWeightSemiBold = FontWeight.w600;
  static const FontWeight fontWeightMedium = FontWeight.w500;
  static const FontWeight fontWeightRegular = FontWeight.w400;
  static const FontWeight fontWeightLight = FontWeight.w300;

  // Standard Spacing
  static const double spacingXXSmall = 2.0;
  static const double spacingXSmall = 4.0;
  static const double spacingSmall = 8.0;
  static const double spacingMedium = 16.0;
  static const double spacingLarge = 24.0;
  static const double spacingXLarge = 32.0;
  static const double spacingXXLarge = 48.0;

  // Standard Border Radius
  static const double borderRadiusSmall = 4.0;
  static const double borderRadiusMedium = 8.0;
  static const double borderRadiusLarge = 12.0;
  static const double borderRadiusXLarge = 16.0;
  static const double borderRadiusXXLarge = 24.0;
  static const double borderRadiusCircular = 100.0;

  // Standard Elevation
  static const double elevationNone = 0.0;
  static const double elevationXSmall = 1.0;
  static const double elevationSmall = 2.0;
  static const double elevationMedium = 4.0;
  static const double elevationLarge = 8.0;
  static const double elevationXLarge = 16.0;

  // Get text theme with fallback for web
  static TextTheme getTextTheme(TextTheme baseTheme, bool isDark) {
    // Use system fonts for web to avoid loading issues
    if (kIsWeb) {
      return baseTheme.copyWith(
        displayLarge: TextStyle(
          color: isDark ? textPrimaryDark : textPrimaryLight,
          fontSize: fontSizeHeadingLarge,
          fontWeight: fontWeightBold,
          fontFamily: 'Roboto',
          letterSpacing: 0.5,
        ),
        displayMedium: TextStyle(
          color: isDark ? textPrimaryDark : textPrimaryLight,
          fontSize: fontSizeHeadingMedium,
          fontWeight: fontWeightBold,
          fontFamily: 'Roboto',
          letterSpacing: 0.5,
        ),
        displaySmall: TextStyle(
          color: isDark ? textPrimaryDark : textPrimaryLight,
          fontSize: fontSizeHeadingSmall,
          fontWeight: fontWeightBold,
          fontFamily: 'Roboto',
          letterSpacing: 0.5,
        ),
        headlineLarge: TextStyle(
          color: isDark ? textPrimaryDark : textPrimaryLight,
          fontSize: fontSizeHeadingLarge,
          fontWeight: fontWeightBold,
          fontFamily: 'Roboto',
          letterSpacing: 0.5,
        ),
        headlineMedium: TextStyle(
          color: isDark ? textPrimaryDark : textPrimaryLight,
          fontSize: fontSizeHeadingMedium,
          fontWeight: fontWeightBold,
          fontFamily: 'Roboto',
          letterSpacing: 0.5,
        ),
        headlineSmall: TextStyle(
          color: isDark ? textPrimaryDark : textPrimaryLight,
          fontSize: fontSizeHeadingSmall,
          fontWeight: fontWeightSemiBold,
          fontFamily: 'Roboto',
          letterSpacing: 0.5,
        ),
        titleLarge: TextStyle(
          color: isDark ? textPrimaryDark : textPrimaryLight,
          fontSize: fontSizeTitle,
          fontWeight: fontWeightSemiBold,
          fontFamily: 'Roboto',
          letterSpacing: 0.3,
        ),
        titleMedium: TextStyle(
          color: isDark ? textPrimaryDark : textPrimaryLight,
          fontSize: fontSizeSubtitle,
          fontWeight: fontWeightMedium,
          fontFamily: 'Roboto',
          letterSpacing: 0.3,
        ),
        titleSmall: TextStyle(
          color: isDark ? textPrimaryDark : textPrimaryLight,
          fontSize: fontSizeCaption,
          fontWeight: fontWeightMedium,
          fontFamily: 'Roboto',
          letterSpacing: 0.3,
        ),
        bodyLarge: TextStyle(
          color: isDark ? textPrimaryDark : textPrimaryLight,
          fontSize: fontSizeBody,
          fontWeight: fontWeightRegular,
          fontFamily: 'Roboto',
          letterSpacing: 0.2,
        ),
        bodyMedium: TextStyle(
          color: isDark ? textPrimaryDark : textPrimaryLight,
          fontSize: fontSizeBody,
          fontWeight: fontWeightRegular,
          fontFamily: 'Roboto',
          letterSpacing: 0.2,
        ),
        bodySmall: TextStyle(
          color: isDark ? textSecondaryDark : textSecondaryLight,
          fontSize: fontSizeCaption,
          fontWeight: fontWeightRegular,
          fontFamily: 'Roboto',
          letterSpacing: 0.2,
        ),
      );
    }

    // Use Google Fonts for non-web platforms
    try {
      return GoogleFonts.poppinsTextTheme(baseTheme).copyWith(
        displayLarge: GoogleFonts.poppins(
          color: isDark ? textPrimaryDark : textPrimaryLight,
          fontSize: fontSizeHeadingLarge,
          fontWeight: fontWeightBold,
          letterSpacing: 0.5,
        ),
        displayMedium: GoogleFonts.poppins(
          color: isDark ? textPrimaryDark : textPrimaryLight,
          fontSize: fontSizeHeadingMedium,
          fontWeight: fontWeightBold,
          letterSpacing: 0.5,
        ),
        displaySmall: GoogleFonts.poppins(
          color: isDark ? textPrimaryDark : textPrimaryLight,
          fontSize: fontSizeHeadingSmall,
          fontWeight: fontWeightBold,
          letterSpacing: 0.5,
        ),
        headlineLarge: GoogleFonts.poppins(
          color: isDark ? textPrimaryDark : textPrimaryLight,
          fontSize: fontSizeHeadingLarge,
          fontWeight: fontWeightBold,
          letterSpacing: 0.5,
        ),
        headlineMedium: GoogleFonts.poppins(
          color: isDark ? textPrimaryDark : textPrimaryLight,
          fontSize: fontSizeHeadingMedium,
          fontWeight: fontWeightBold,
          letterSpacing: 0.5,
        ),
        headlineSmall: GoogleFonts.poppins(
          color: isDark ? textPrimaryDark : textPrimaryLight,
          fontSize: fontSizeHeadingSmall,
          fontWeight: fontWeightSemiBold,
          letterSpacing: 0.5,
        ),
        titleLarge: GoogleFonts.poppins(
          color: isDark ? textPrimaryDark : textPrimaryLight,
          fontSize: fontSizeTitle,
          fontWeight: fontWeightSemiBold,
          letterSpacing: 0.3,
        ),
        titleMedium: GoogleFonts.poppins(
          color: isDark ? textPrimaryDark : textPrimaryLight,
          fontSize: fontSizeSubtitle,
          fontWeight: fontWeightMedium,
          letterSpacing: 0.3,
        ),
        titleSmall: GoogleFonts.poppins(
          color: isDark ? textPrimaryDark : textPrimaryLight,
          fontSize: fontSizeCaption,
          fontWeight: fontWeightMedium,
          letterSpacing: 0.3,
        ),
        bodyLarge: GoogleFonts.poppins(
          color: isDark ? textPrimaryDark : textPrimaryLight,
          fontSize: fontSizeBody,
          fontWeight: fontWeightRegular,
          letterSpacing: 0.2,
        ),
        bodyMedium: GoogleFonts.poppins(
          color: isDark ? textPrimaryDark : textPrimaryLight,
          fontSize: fontSizeBody,
          fontWeight: fontWeightRegular,
          letterSpacing: 0.2,
        ),
        bodySmall: GoogleFonts.poppins(
          color: isDark ? textSecondaryDark : textSecondaryLight,
          fontSize: fontSizeCaption,
          fontWeight: fontWeightRegular,
          letterSpacing: 0.2,
        ),
        labelLarge: GoogleFonts.poppins(
          color: isDark ? textPrimaryDark : textPrimaryLight,
          fontSize: fontSizeSubtitle,
          fontWeight: fontWeightMedium,
          letterSpacing: 0.3,
        ),
        labelMedium: GoogleFonts.poppins(
          color: isDark ? textPrimaryDark : textPrimaryLight,
          fontSize: fontSizeCaption,
          fontWeight: fontWeightMedium,
          letterSpacing: 0.3,
        ),
        labelSmall: GoogleFonts.poppins(
          color: isDark ? textSecondaryDark : textSecondaryLight,
          fontSize: fontSizeSmall,
          fontWeight: fontWeightRegular,
          letterSpacing: 0.2,
        ),
      );
    } catch (e) {
      // Fallback to system fonts if Google Fonts fails
      return baseTheme.copyWith(
        displayLarge: TextStyle(
          color: isDark ? textPrimaryDark : textPrimaryLight,
          fontSize: fontSizeHeadingLarge,
          fontWeight: fontWeightBold,
          letterSpacing: 0.5,
        ),
        displayMedium: TextStyle(
          color: isDark ? textPrimaryDark : textPrimaryLight,
          fontSize: fontSizeHeadingMedium,
          fontWeight: fontWeightBold,
          letterSpacing: 0.5,
        ),
        displaySmall: TextStyle(
          color: isDark ? textPrimaryDark : textPrimaryLight,
          fontSize: fontSizeHeadingSmall,
          fontWeight: fontWeightBold,
          letterSpacing: 0.5,
        ),
        headlineLarge: TextStyle(
          color: isDark ? textPrimaryDark : textPrimaryLight,
          fontSize: fontSizeHeadingLarge,
          fontWeight: fontWeightBold,
          letterSpacing: 0.5,
        ),
        headlineMedium: TextStyle(
          color: isDark ? textPrimaryDark : textPrimaryLight,
          fontSize: fontSizeHeadingMedium,
          fontWeight: fontWeightBold,
          letterSpacing: 0.5,
        ),
        headlineSmall: TextStyle(
          color: isDark ? textPrimaryDark : textPrimaryLight,
          fontSize: fontSizeHeadingSmall,
          fontWeight: fontWeightSemiBold,
          letterSpacing: 0.5,
        ),
        titleLarge: TextStyle(
          color: isDark ? textPrimaryDark : textPrimaryLight,
          fontSize: fontSizeTitle,
          fontWeight: fontWeightSemiBold,
          letterSpacing: 0.3,
        ),
        titleMedium: TextStyle(
          color: isDark ? textPrimaryDark : textPrimaryLight,
          fontSize: fontSizeSubtitle,
          fontWeight: fontWeightMedium,
          letterSpacing: 0.3,
        ),
        titleSmall: TextStyle(
          color: isDark ? textPrimaryDark : textPrimaryLight,
          fontSize: fontSizeCaption,
          fontWeight: fontWeightMedium,
          letterSpacing: 0.3,
        ),
        bodyLarge: TextStyle(
          color: isDark ? textPrimaryDark : textPrimaryLight,
          fontSize: fontSizeBody,
          fontWeight: fontWeightRegular,
          letterSpacing: 0.2,
        ),
        bodyMedium: TextStyle(
          color: isDark ? textPrimaryDark : textPrimaryLight,
          fontSize: fontSizeBody,
          fontWeight: fontWeightRegular,
          letterSpacing: 0.2,
        ),
        bodySmall: TextStyle(
          color: isDark ? textSecondaryDark : textSecondaryLight,
          fontSize: fontSizeCaption,
          fontWeight: fontWeightRegular,
          letterSpacing: 0.2,
        ),
        labelLarge: TextStyle(
          color: isDark ? textPrimaryDark : textPrimaryLight,
          fontSize: fontSizeSubtitle,
          fontWeight: fontWeightMedium,
          letterSpacing: 0.3,
        ),
        labelMedium: TextStyle(
          color: isDark ? textPrimaryDark : textPrimaryLight,
          fontSize: fontSizeCaption,
          fontWeight: fontWeightMedium,
          letterSpacing: 0.3,
        ),
        labelSmall: TextStyle(
          color: isDark ? textSecondaryDark : textSecondaryLight,
          fontSize: fontSizeSmall,
          fontWeight: fontWeightRegular,
          letterSpacing: 0.2,
        ),
      );
    }
  }

  // Light Theme
  static final ThemeData lightTheme = ThemeData(
    useMaterial3: true,
    colorScheme: ColorScheme.light(
      primary: primaryColor,
      secondary: secondaryColor,
      tertiary: accentColor,
      error: errorColor,
      surface: Colors.white,
    ),
    scaffoldBackgroundColor: backgroundLight,
    appBarTheme: const AppBarTheme(
      backgroundColor: primaryColor,
      foregroundColor: Colors.white,
      elevation: 0,
    ),
    textTheme: getTextTheme(ThemeData.light().textTheme, false),
    elevatedButtonTheme: ElevatedButtonThemeData(
      style: ElevatedButton.styleFrom(
        backgroundColor: primaryColor,
        foregroundColor: Colors.white,
        elevation: 2,
        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      ),
    ),
    outlinedButtonTheme: OutlinedButtonThemeData(
      style: OutlinedButton.styleFrom(
        foregroundColor: primaryColor,
        side: const BorderSide(color: primaryColor),
        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      ),
    ),
    textButtonTheme: TextButtonThemeData(
      style: TextButton.styleFrom(foregroundColor: primaryColor),
    ),
    inputDecorationTheme: InputDecorationTheme(
      filled: true,
      fillColor: Colors.white,
      contentPadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 16),
      border: OutlineInputBorder(
        borderRadius: BorderRadius.circular(12),
        borderSide: const BorderSide(color: Colors.grey),
      ),
      enabledBorder: OutlineInputBorder(
        borderRadius: BorderRadius.circular(12),
        borderSide: const BorderSide(color: Colors.grey),
      ),
      focusedBorder: OutlineInputBorder(
        borderRadius: BorderRadius.circular(12),
        borderSide: const BorderSide(color: primaryColor, width: 2),
      ),
      errorBorder: OutlineInputBorder(
        borderRadius: BorderRadius.circular(12),
        borderSide: const BorderSide(color: errorColor),
      ),
      labelStyle: const TextStyle(color: textSecondaryLight),
      hintStyle: const TextStyle(color: textSecondaryLight),
    ),
    cardTheme: CardTheme(
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
    ),
    bottomNavigationBarTheme: const BottomNavigationBarThemeData(
      backgroundColor: Colors.white,
      selectedItemColor: primaryColor,
      unselectedItemColor: Colors.black54,
      selectedLabelStyle: TextStyle(fontWeight: FontWeight.bold, fontSize: 12),
      unselectedLabelStyle: TextStyle(
        fontWeight: FontWeight.normal,
        fontSize: 12,
      ),
      type: BottomNavigationBarType.fixed,
      elevation: 8,
    ),
    tabBarTheme: const TabBarTheme(
      labelColor: primaryColor,
      unselectedLabelColor: textSecondaryLight,
      indicatorColor: primaryColor,
    ),
    dividerTheme: const DividerThemeData(color: Colors.grey, thickness: 0.5),
    checkboxTheme: CheckboxThemeData(
      fillColor: WidgetStateProperty.resolveWith<Color>((states) {
        if (states.contains(WidgetState.selected)) {
          return primaryColor;
        }
        return Colors.grey;
      }),
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(4)),
    ),
    radioTheme: RadioThemeData(
      fillColor: WidgetStateProperty.resolveWith<Color>((states) {
        if (states.contains(WidgetState.selected)) {
          return primaryColor;
        }
        return Colors.grey;
      }),
    ),
    switchTheme: SwitchThemeData(
      thumbColor: WidgetStateProperty.resolveWith<Color>((states) {
        if (states.contains(WidgetState.selected)) {
          return primaryColor;
        }
        return Colors.grey;
      }),
      trackColor: WidgetStateProperty.resolveWith<Color>((states) {
        if (states.contains(WidgetState.selected)) {
          return primaryLightColor;
        }
        return Colors.grey.withAlpha(128);
      }),
    ),
    progressIndicatorTheme: const ProgressIndicatorThemeData(
      color: primaryColor,
    ),
    floatingActionButtonTheme: const FloatingActionButtonThemeData(
      backgroundColor: primaryColor,
      foregroundColor: Colors.white,
    ),
    snackBarTheme: SnackBarThemeData(
      backgroundColor: Colors.grey[800],
      contentTextStyle: const TextStyle(color: Colors.white),
      behavior: SnackBarBehavior.floating,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(8)),
    ),
  );

  // Dark Theme
  static final ThemeData darkTheme = ThemeData(
    useMaterial3: true,
    colorScheme: ColorScheme.dark(
      primary: primaryColor,
      secondary: secondaryColor,
      tertiary: accentColor,
      error: errorColor,
      surface: const Color(0xFF1E1E1E),
    ),
    scaffoldBackgroundColor: backgroundDark,
    appBarTheme: const AppBarTheme(
      backgroundColor: Color(0xFF1E1E1E),
      foregroundColor: Colors.white,
      elevation: 0,
    ),
    textTheme: getTextTheme(ThemeData.dark().textTheme, true),
    elevatedButtonTheme: ElevatedButtonThemeData(
      style: ElevatedButton.styleFrom(
        backgroundColor: primaryColor,
        foregroundColor: Colors.white,
        elevation: 2,
        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      ),
    ),
    outlinedButtonTheme: OutlinedButtonThemeData(
      style: OutlinedButton.styleFrom(
        foregroundColor: primaryColor,
        side: const BorderSide(color: primaryColor),
        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      ),
    ),
    textButtonTheme: TextButtonThemeData(
      style: TextButton.styleFrom(foregroundColor: primaryColor),
    ),
    inputDecorationTheme: InputDecorationTheme(
      filled: true,
      fillColor: const Color(0xFF2C2C2C),
      contentPadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 16),
      border: OutlineInputBorder(
        borderRadius: BorderRadius.circular(12),
        borderSide: const BorderSide(color: Colors.grey),
      ),
      enabledBorder: OutlineInputBorder(
        borderRadius: BorderRadius.circular(12),
        borderSide: const BorderSide(color: Colors.grey),
      ),
      focusedBorder: OutlineInputBorder(
        borderRadius: BorderRadius.circular(12),
        borderSide: const BorderSide(color: primaryColor, width: 2),
      ),
      errorBorder: OutlineInputBorder(
        borderRadius: BorderRadius.circular(12),
        borderSide: const BorderSide(color: errorColor),
      ),
      labelStyle: const TextStyle(color: textSecondaryDark),
      hintStyle: const TextStyle(color: textSecondaryDark),
    ),
    cardTheme: CardTheme(
      color: const Color(0xFF2C2C2C),
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
    ),
    bottomNavigationBarTheme: const BottomNavigationBarThemeData(
      backgroundColor: Color(0xFF1E1E1E),
      selectedItemColor: primaryLightColor,
      unselectedItemColor: Colors.white70,
      selectedLabelStyle: TextStyle(fontWeight: FontWeight.bold, fontSize: 12),
      unselectedLabelStyle: TextStyle(
        fontWeight: FontWeight.normal,
        fontSize: 12,
      ),
      type: BottomNavigationBarType.fixed,
      elevation: 8,
    ),
    tabBarTheme: const TabBarTheme(
      labelColor: primaryLightColor,
      unselectedLabelColor: textSecondaryDark,
      indicatorColor: primaryLightColor,
    ),
    dividerTheme: const DividerThemeData(color: Colors.grey, thickness: 0.5),
    checkboxTheme: CheckboxThemeData(
      fillColor: WidgetStateProperty.resolveWith<Color>((states) {
        if (states.contains(WidgetState.selected)) {
          return primaryColor;
        }
        return Colors.grey;
      }),
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(4)),
    ),
    radioTheme: RadioThemeData(
      fillColor: WidgetStateProperty.resolveWith<Color>((states) {
        if (states.contains(WidgetState.selected)) {
          return primaryColor;
        }
        return Colors.grey;
      }),
    ),
    switchTheme: SwitchThemeData(
      thumbColor: WidgetStateProperty.resolveWith<Color>((states) {
        if (states.contains(WidgetState.selected)) {
          return primaryColor;
        }
        return Colors.grey;
      }),
      trackColor: WidgetStateProperty.resolveWith<Color>((states) {
        if (states.contains(WidgetState.selected)) {
          return primaryLightColor;
        }
        return Colors.grey.withAlpha(128);
      }),
    ),
    progressIndicatorTheme: const ProgressIndicatorThemeData(
      color: primaryColor,
    ),
    floatingActionButtonTheme: const FloatingActionButtonThemeData(
      backgroundColor: primaryColor,
      foregroundColor: Colors.white,
    ),
    snackBarTheme: SnackBarThemeData(
      backgroundColor: Colors.grey[900],
      contentTextStyle: const TextStyle(color: Colors.white),
      behavior: SnackBarBehavior.floating,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(8)),
    ),
  );
}
