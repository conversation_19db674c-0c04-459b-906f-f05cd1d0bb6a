import 'package:flutter/material.dart';
import 'package:fit_4_force/core/utils/navigation_service.dart';
import 'package:fit_4_force/features/fitness/models/workout_model.dart';
import 'package:fit_4_force/features/fitness/services/workout_service.dart';

class WorkoutCategoriesScreen extends StatefulWidget {
  const WorkoutCategoriesScreen({super.key});

  @override
  State<WorkoutCategoriesScreen> createState() =>
      _WorkoutCategoriesScreenState();
}

class _WorkoutCategoriesScreenState extends State<WorkoutCategoriesScreen> {
  final WorkoutService _workoutService = WorkoutService();
  late List<WorkoutCategoryModel> _categories;

  @override
  void initState() {
    super.initState();
    _categories = _workoutService.getAllCategories();
  }

  void _navigateToCategoryDetail(WorkoutCategoryModel category) {
    NavigationService().navigateTo('/category-detail', arguments: category.id);
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(title: const Text('Workout Categories')),
      body: _buildCategoriesList(),
    );
  }

  Widget _buildCategoriesList() {
    return GridView.builder(
      padding: const EdgeInsets.all(16),
      gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: 2,
        childAspectRatio: 0.8,
        crossAxisSpacing: 16,
        mainAxisSpacing: 16,
      ),
      itemCount: _categories.length,
      itemBuilder: (context, index) {
        final category = _categories[index];
        return _buildCategoryCard(category);
      },
    );
  }

  Widget _buildCategoryCard(WorkoutCategoryModel category) {
    return GestureDetector(
      onTap: () => _navigateToCategoryDetail(category),
      child: Container(
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(16),
          boxShadow: [
            BoxShadow(
              color: Colors.grey.withValues(
                alpha: (0.1 * 255).round().toDouble(),
              ),
              blurRadius: 10,
              offset: const Offset(0, 4),
            ),
          ],
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            Expanded(
              flex: 3,
              child: Container(
                decoration: BoxDecoration(
                  color: category.color.withValues(
                    alpha: (0.2 * 255).round().toDouble(),
                  ),
                  borderRadius: const BorderRadius.vertical(
                    top: Radius.circular(16),
                  ),
                ),
                child: Center(
                  child: Icon(category.icon, size: 48, color: category.color),
                ),
              ),
            ),
            Expanded(
              flex: 2,
              child: Padding(
                padding: const EdgeInsets.all(12),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Text(
                      category.name,
                      style: Theme.of(context).textTheme.titleMedium?.copyWith(
                        fontWeight: FontWeight.bold,
                        color: Colors.black87, // Ensure text is visible
                      ),
                      maxLines: 1,
                      overflow: TextOverflow.ellipsis,
                    ),
                    const SizedBox(height: 4),
                    Text(
                      '${category.workoutCount} workouts',
                      style: TextStyle(
                        fontSize: 12,
                        color: Colors.black54, // Ensure text is visible
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
