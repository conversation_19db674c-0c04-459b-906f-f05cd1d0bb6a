import 'dart:io';
import 'package:logger/logger.dart';
import 'package:path/path.dart' as path;
import 'package:uuid/uuid.dart';
import 'package:supabase_flutter/supabase_flutter.dart';

/// Service for handling file storage operations with Supabase
class StorageService {
  final Logger _logger = Logger();
  final Uuid _uuid = const Uuid();

  // Get Supabase storage client
  SupabaseStorageClient get _storage => Supabase.instance.client.storage;

  /// Uploads a file to Supabase Storage
  ///
  /// [file] - The file to upload
  /// [folder] - The folder path in Supabase Storage
  /// [fileName] - Optional custom file name, if not provided a UUID will be generated
  /// [onProgress] - Optional callback for upload progress
  Future<String?> uploadFile({
    required File file,
    required String folder,
    String? fileName,
    Function(double)? onProgress,
  }) async {
    try {
      // Generate a unique file name if not provided
      final String fileExtension = path.extension(file.path);
      final String uniqueFileName = '${fileName ?? _uuid.v4()}$fileExtension';
      final String storagePath = '$folder/$uniqueFileName';

      // Read file as bytes
      final bytes = await file.readAsBytes();

      // Create bucket if it doesn't exist
      final bucketName = 'files';
      try {
        await _storage.createBucket(bucketName);
      } catch (e) {
        // Bucket might already exist, ignore error
        _logger.i('Bucket might already exist: $e');
      }

      // Call progress callback with 50% completion (reading file)
      if (onProgress != null) {
        onProgress(0.5);
      }

      // Upload file to Supabase storage
      await _storage
          .from(bucketName)
          .uploadBinary(
            storagePath,
            bytes,
            fileOptions: FileOptions(contentType: 'application/octet-stream'),
          );

      // Get public URL
      final downloadUrl = _storage.from(bucketName).getPublicUrl(storagePath);

      // Call progress callback with 100% completion
      if (onProgress != null) {
        onProgress(1.0);
      }

      _logger.i('File uploaded successfully to $storagePath');
      return downloadUrl;
    } catch (e) {
      _logger.e('Error uploading file: $e');
      return null;
    }
  }

  /// Uploads a profile image to Supabase Storage
  ///
  /// [file] - The image file to upload
  /// [userId] - The user ID to associate with the image
  /// [onProgress] - Optional callback for upload progress
  Future<String?> uploadProfileImage({
    required File file,
    required String userId,
    Function(double)? onProgress,
  }) async {
    return uploadFile(
      file: file,
      folder: 'profile_images',
      fileName: userId,
      onProgress: onProgress,
    );
  }

  /// Deletes a file from Supabase Storage by URL
  Future<bool> deleteFileByUrl(String fileUrl) async {
    try {
      // Extract file path from URL
      final uri = Uri.parse(fileUrl);
      final pathSegments = uri.pathSegments;

      // The last segment should be the file name
      final fileName = pathSegments.last;

      // Extract folder path if available
      String? folderPath;
      if (pathSegments.length > 2) {
        folderPath = pathSegments[pathSegments.length - 2];
      }

      // The bucket name is typically the segment before the file path
      final bucketName = 'files';

      // Construct the storage path
      final storagePath =
          folderPath != null ? '$folderPath/$fileName' : fileName;

      // Delete file from Supabase storage
      await _storage.from(bucketName).remove([storagePath]);

      _logger.i('File deleted successfully: $storagePath');
      return true;
    } catch (e) {
      _logger.e('Error deleting file: $e');
      return false;
    }
  }
}
