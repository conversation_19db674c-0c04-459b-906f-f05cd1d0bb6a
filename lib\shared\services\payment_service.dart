import 'package:flutter/material.dart';
// Temporarily disabled due to compatibility issues
// import 'package:flutter_paystack/flutter_paystack.dart';
import 'package:fit_4_force/core/config/app_config.dart';
import 'package:fit_4_force/shared/services/auth_service.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'base_service.dart';
import 'package:logger/logger.dart';

class PaymentService extends BaseService {
  final PaystackPlugin _paystack = PaystackPlugin();
  final AuthService _authService;
  final Logger _logger = Logger();
  bool _initialized = false;

  @override
  CollectionReference<Map<String, dynamic>> get collection =>
      firestore.collection('payments');

  PaymentService(this._authService) {
    _initializePaystack();
  }

  Future<void> _initializePaystack() async {
    if (!_initialized) {
      await _paystack.initialize(publicKey: AppConfig.paystackPublicKey);
      _initialized = true;
    }
  }

  Future<bool> processPayment({
    required BuildContext context,
    required String email,
    required String fullName,
    required double amount,
    Function(String)? onSuccess,
    Function(String)? onError,
  }) async {
    try {
      // Ensure Paystack is initialized
      if (!_initialized) {
        await _initializePaystack();
      }

      // Generate a unique reference for this transaction
      final reference = _generateReference();

      // Create a charge
      final charge =
          Charge()
            ..amount =
                (amount * 100)
                    .toInt() // Convert to kobo
            ..email = email
            ..reference = reference
            ..putCustomField('fullName', fullName)
            ..putCustomField('userId', currentUserId ?? 'guest');

      // Check if context is still valid
      if (!context.mounted) {
        if (onError != null) {
          onError('Context is no longer valid');
        }
        return false;
      }

      // Show the Paystack checkout UI
      final response = await _paystack.checkout(
        context,
        charge: charge,
        method: CheckoutMethod.card,
        fullscreen: true,
      );

      if (response.status && response.reference != null) {
        // Payment was successful
        await _handleSuccessfulPayment(response.reference!);
        if (onSuccess != null) {
          onSuccess(response.reference!);
        }
        return true;
      } else {
        // Payment failed
        if (onError != null) {
          onError(response.message);
        }
        return false;
      }
    } catch (e) {
      _logger.e('Paystack error: $e');
      if (onError != null) {
        onError(e.toString());
      }
      return false;
    }
  }

  Future<void> _handleSuccessfulPayment(String reference) async {
    if (currentUserId == null) return;

    try {
      // Set premium status for 30 days
      final expiryDate = DateTime.now().add(const Duration(days: 30));
      await _authService.updatePremiumStatus(currentUserId!, true, expiryDate);

      // Create a payment record
      await collection.add({
        'userId': currentUserId,
        'reference': reference,
        'amount': AppConfig.premiumSubscriptionPrice,
        'status': 'success',
        'type': 'subscription',
        'createdAt': FieldValue.serverTimestamp(),
      });
    } catch (e) {
      _logger.e('Error processing successful payment: $e');
    }
  }

  String _generateReference() {
    return 'FIT4FORCE_${DateTime.now().millisecondsSinceEpoch}';
  }

  Future<bool> verifyTransaction(String reference) async {
    try {
      // Check if the transaction exists in our database
      final querySnapshot =
          await collection
              .where('reference', isEqualTo: reference)
              .limit(1)
              .get();

      return querySnapshot.docs.isNotEmpty;
    } catch (e) {
      _logger.e('Error verifying transaction: $e');
      return false;
    }
  }
}
