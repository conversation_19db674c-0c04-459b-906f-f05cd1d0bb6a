import 'package:flutter/material.dart';
import 'package:fit_4_force/core/config/app_routes.dart';
import 'package:fit_4_force/core/theme/app_theme.dart';
import 'package:fit_4_force/core/theme/app_ui.dart';
import 'package:fit_4_force/core/theme/app_text.dart';
import 'package:fit_4_force/core/theme/app_gradients.dart';
import 'package:fit_4_force/core/utils/responsive_utils.dart';
import 'package:fit_4_force/core/widgets/responsive_widgets.dart';
import 'package:fit_4_force/shared/models/user_model.dart';

/// Extension on Color class to provide additional functionality
extension ColorExtensions on Color {
  /// Create a new color with the specified alpha value
  /// This is a more precise alternative to withOpacity() which can lose precision
  /// @param alpha The alpha value (0-255)
  Color withValues({int? red, int? green, int? blue, int? alpha}) {
    return Color.fromARGB(
      alpha ?? a.toInt(),
      red ?? r.toInt(),
      green ?? g.toInt(),
      blue ?? b.toInt(),
    );
  }
}

class DashboardScreen extends StatelessWidget {
  final UserModel user;
  final Function(int)? onTabChange;

  const DashboardScreen({super.key, required this.user, this.onTabChange});

  @override
  Widget build(BuildContext context) {
    final isLandscape = ResponsiveUtils.isLandscape(context);
    final padding = ResponsiveUtils.getResponsivePadding(
      context,
      mobile: const EdgeInsets.all(12),
      tablet: const EdgeInsets.all(16),
      desktop: const EdgeInsets.all(20),
    );
    final spacing = ResponsiveUtils.getResponsiveSpacing(
      context,
      mobile: 16.0,
      tablet: 20.0,
      desktop: 24.0,
    );

    return SingleChildScrollView(
      padding: padding,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildHeader(context),
          SizedBox(height: spacing),
          _buildMotivationalQuote(context),
          SizedBox(height: spacing),
          if (isLandscape)
            _buildLandscapeLayout(context, spacing)
          else
            _buildPortraitLayout(context, spacing),
        ],
      ),
    );
  }

  Widget _buildPortraitLayout(BuildContext context, double spacing) {
    return Column(
      children: [
        _buildProgressSection(context),
        SizedBox(height: spacing),
        _buildQuickActions(context),
        SizedBox(height: spacing),
        _buildUpcomingEvents(context),
        SizedBox(height: spacing),
        _buildRecommendedContent(context),
      ],
    );
  }

  Widget _buildLandscapeLayout(BuildContext context, double spacing) {
    return Column(
      children: [
        // Progress and Quick Actions side by side in landscape
        Row(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Expanded(flex: 1, child: _buildProgressSection(context)),
            SizedBox(width: spacing),
            Expanded(flex: 1, child: _buildQuickActions(context)),
          ],
        ),
        SizedBox(height: spacing),
        _buildUpcomingEvents(context),
        SizedBox(height: spacing),
        _buildRecommendedContent(context),
      ],
    );
  }

  Widget _buildHeader(BuildContext context) {
    return Container(
      padding: EdgeInsets.all(AppTheme.spacingLarge),
      decoration: BoxDecoration(
        gradient: AppGradients.blue,
        borderRadius: BorderRadius.circular(AppTheme.borderRadiusXLarge),
        boxShadow: [
          BoxShadow(
            color: const Color(0xFF4A80F0).withValues(alpha: 0.4 * 255),
            blurRadius: 15,
            offset: const Offset(0, 8),
            spreadRadius: 2,
          ),
          BoxShadow(
            color: Colors.white.withValues(alpha: 0.05 * 255),
            blurRadius: 2,
            offset: const Offset(0, -1),
            spreadRadius: 0,
          ),
        ],
        border: Border.all(
          color: Colors.white.withValues(alpha: 0.1 * 255),
          width: 1.5,
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              // 3D-like profile avatar
              Container(
                height: 70,
                width: 70,
                decoration: BoxDecoration(
                  shape: BoxShape.circle,
                  gradient: LinearGradient(
                    colors: [
                      Colors.white.withValues(alpha: 0.3 * 255),
                      Colors.white.withValues(alpha: 0.1 * 255),
                    ],
                    begin: Alignment.topLeft,
                    end: Alignment.bottomRight,
                  ),
                  boxShadow: [
                    BoxShadow(
                      color: Colors.black.withValues(alpha: 0.2 * 255),
                      blurRadius: 10,
                      offset: const Offset(0, 5),
                      spreadRadius: 1,
                    ),
                    BoxShadow(
                      color: Colors.white.withValues(alpha: 0.1 * 255),
                      blurRadius: 0,
                      offset: const Offset(0, 0),
                      spreadRadius: -1,
                    ),
                  ],
                  border: Border.all(
                    color: Colors.white.withValues(alpha: 0.2 * 255),
                    width: 2,
                  ),
                ),
                child: Center(
                  child: Text(
                    user.fullName.isNotEmpty
                        ? user.fullName[0].toUpperCase()
                        : 'U',
                    style: const TextStyle(
                      fontSize: 28,
                      fontWeight: FontWeight.bold,
                      color: Colors.white,
                      shadows: [
                        Shadow(
                          color: Colors.black54,
                          blurRadius: 5,
                          offset: Offset(0, 2),
                        ),
                      ],
                    ),
                  ),
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Welcome back,',
                      style: AppText.bodyMedium(
                        context,
                        color: Colors.white,
                        fontWeight: AppTheme.fontWeightMedium,
                      ),
                    ),
                    Text(
                      user.fullName,
                      style: AppText.headingSmall(
                        context,
                        color: Colors.white,
                        fontWeight: AppTheme.fontWeightBold,
                      ).copyWith(
                        shadows: const [
                          Shadow(
                            color: Colors.black38,
                            blurRadius: 3,
                            offset: Offset(0, 1),
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
              ),
              if (!user.isPremium)
                Container(
                  padding: const EdgeInsets.symmetric(
                    horizontal: 12,
                    vertical: 8,
                  ),
                  decoration: BoxDecoration(
                    gradient: LinearGradient(
                      colors: [
                        AppTheme.premiumColor,
                        Color.lerp(AppTheme.premiumColor, Colors.white, 0.3)!,
                      ],
                      begin: Alignment.topLeft,
                      end: Alignment.bottomRight,
                    ),
                    borderRadius: BorderRadius.circular(20),
                    boxShadow: [
                      BoxShadow(
                        color: AppTheme.premiumColor.withValues(
                          alpha: 0.5 * 255,
                        ),
                        blurRadius: 8,
                        offset: const Offset(0, 3),
                        spreadRadius: 0,
                      ),
                    ],
                  ),
                  child: GestureDetector(
                    onTap:
                        () =>
                            Navigator.of(context).pushNamed(AppRoutes.premium),
                    child: Row(
                      mainAxisSize: MainAxisSize.min,
                      children: const [
                        Icon(
                          Icons.workspace_premium,
                          color: Colors.white,
                          size: 18,
                        ),
                        SizedBox(width: 6),
                        Text(
                          'UPGRADE',
                          style: TextStyle(
                            color: Colors.white,
                            fontWeight: FontWeight.bold,
                            fontSize: 12,
                            letterSpacing: 1,
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
            ],
          ),
          const SizedBox(height: 24),
          // Journey progress section with improved visuals
          Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: Colors.white.withValues(alpha: 0.15 * 255),
              borderRadius: BorderRadius.circular(16),
              border: Border.all(
                color: Colors.white.withValues(alpha: 0.2 * 255),
                width: 1,
              ),
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    Container(
                      padding: const EdgeInsets.all(8),
                      decoration: BoxDecoration(
                        color: Colors.white.withValues(alpha: 0.2 * 255),
                        shape: BoxShape.circle,
                      ),
                      child: const Icon(
                        Icons.trending_up,
                        color: Colors.white,
                        size: 16,
                      ),
                    ),
                    const SizedBox(width: 10),
                    Text(
                      'Your ${user.targetAgency} Journey',
                      style: const TextStyle(
                        fontSize: 17,
                        color: Colors.white,
                        fontWeight: FontWeight.w700,
                        letterSpacing: 0.5,
                        shadows: [
                          Shadow(
                            color: Colors.black38,
                            blurRadius: 2,
                            offset: Offset(0, 1),
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 12),
                // Animated progress indicator
                Stack(
                  children: [
                    // Background track
                    Container(
                      height: 10,
                      decoration: BoxDecoration(
                        color: Colors.white.withValues(alpha: 0.2 * 255),
                        borderRadius: BorderRadius.circular(10),
                      ),
                    ),
                    // Progress fill with gradient
                    LayoutBuilder(
                      builder: (context, constraints) {
                        return Container(
                          height: 10,
                          width: constraints.maxWidth * 0.65,
                          decoration: BoxDecoration(
                            gradient: LinearGradient(
                              colors: [
                                Colors.white,
                                Colors.white.withValues(alpha: 0.7 * 255),
                              ],
                              begin: Alignment.centerLeft,
                              end: Alignment.centerRight,
                            ),
                            borderRadius: BorderRadius.circular(10),
                            boxShadow: [
                              BoxShadow(
                                color: Colors.white.withValues(
                                  alpha: 0.5 * 255,
                                ),
                                blurRadius: 4,
                                offset: const Offset(0, 0),
                                spreadRadius: 0,
                              ),
                            ],
                          ),
                        );
                      },
                    ),
                  ],
                ),
                const SizedBox(height: 12),
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Row(
                      children: [
                        const Icon(
                          Icons.check_circle,
                          color: Colors.white,
                          size: 16,
                        ),
                        const SizedBox(width: 6),
                        Text(
                          '65% Complete',
                          style: const TextStyle(
                            fontSize: 14,
                            fontWeight: FontWeight.w600,
                            color: Colors.white,
                            letterSpacing: 0.3,
                            shadows: [
                              Shadow(
                                color: Colors.black26,
                                blurRadius: 2,
                                offset: Offset(0, 1),
                              ),
                            ],
                          ),
                        ),
                      ],
                    ),
                    Row(
                      children: [
                        const Icon(
                          Icons.calendar_today,
                          color: Colors.white,
                          size: 14,
                        ),
                        const SizedBox(width: 6),
                        Text(
                          '35 days to exam',
                          style: const TextStyle(
                            fontSize: 14,
                            fontWeight: FontWeight.w600,
                            color: Colors.white,
                            letterSpacing: 0.3,
                            shadows: [
                              Shadow(
                                color: Colors.black26,
                                blurRadius: 2,
                                offset: Offset(0, 1),
                              ),
                            ],
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildMotivationalQuote(BuildContext context) {
    // List of motivational quotes for military aspirants
    final List<Map<String, String>> quotes = [
      {
        'quote':
            'The more you sweat in training, the less you bleed in combat.',
        'author': 'Navy SEAL saying',
      },
      {
        'quote':
            'Success is not final, failure is not fatal: it is the courage to continue that counts.',
        'author': 'Winston Churchill',
      },
      {
        'quote': 'The only easy day was yesterday.',
        'author': 'Navy SEAL motto',
      },
      {
        'quote': 'Pain is weakness leaving the body.',
        'author': 'Marine Corps saying',
      },
      {
        'quote': 'Discipline is the bridge between goals and accomplishment.',
        'author': 'Jim Rohn',
      },
    ];

    // Select a random quote
    final quote = quotes[DateTime.now().day % quotes.length];

    return Container(
      padding: EdgeInsets.all(AppTheme.spacingLarge),
      decoration: BoxDecoration(
        gradient: AppGradients.dark,
        borderRadius: BorderRadius.circular(AppTheme.borderRadiusXLarge),
        boxShadow: AppUI.shadowLarge,
        border: Border.all(
          color: Colors.white.withValues(alpha: 0.1 * 255),
          width: 1,
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Container(
                padding: const EdgeInsets.all(10),
                decoration: BoxDecoration(
                  color: Colors.white.withValues(alpha: 0.1 * 255),
                  shape: BoxShape.circle,
                ),
                child: const Icon(
                  Icons.format_quote,
                  color: Colors.white,
                  size: 20,
                ),
              ),
              const SizedBox(width: 12),
              Text(
                'Daily Motivation',
                style: AppText.titleLarge(
                  context,
                  color: Colors.white,
                  fontWeight: AppTheme.fontWeightBold,
                ).copyWith(
                  shadows: const [
                    Shadow(
                      color: Colors.black38,
                      blurRadius: 2,
                      offset: Offset(0, 1),
                    ),
                  ],
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          Text(
            '"${quote['quote']}"',
            style: AppText.bodyLarge(
              context,
              color: Colors.white,
              fontWeight: AppTheme.fontWeightSemiBold,
            ).copyWith(
              height: 1.4,
              fontStyle: FontStyle.italic,
              shadows: const [
                Shadow(
                  color: Colors.black38,
                  blurRadius: 3,
                  offset: Offset(0, 1),
                ),
              ],
            ),
          ),
          const SizedBox(height: 12),
          Align(
            alignment: Alignment.centerRight,
            child: Text(
              '— ${quote['author']}',
              style: AppText.bodyMedium(
                context,
                color: Colors.white,
                fontWeight: AppTheme.fontWeightMedium,
              ).copyWith(
                letterSpacing: 0.5,
                shadows: const [
                  Shadow(
                    color: Colors.black26,
                    blurRadius: 2,
                    offset: Offset(0, 1),
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildProgressSection(BuildContext context) {
    final spacing = ResponsiveUtils.getResponsiveSpacing(context);
    final isSmallScreen = ResponsiveUtils.isSmallPhone(context);

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        ResponsiveText(
          'Your Progress',
          mobileFontSize: isSmallScreen ? 18.0 : 20.0,
          tabletFontSize: 22.0,
          desktopFontSize: 24.0,
          fontWeight: AppTheme.fontWeightBold,
        ),
        SizedBox(height: spacing),
        LayoutBuilder(
          builder: (context, constraints) {
            // Stack vertically on very small screens
            if (constraints.maxWidth < 300) {
              return Column(
                children: [
                  _buildProgressCard(
                    context,
                    title: 'Fitness',
                    progress: 0.75,
                    color: Colors.green,
                    icon: Icons.fitness_center,
                  ),
                  SizedBox(height: spacing / 2),
                  _buildProgressCard(
                    context,
                    title: 'Academics',
                    progress: 0.60,
                    color: AppTheme.primaryColor,
                    icon: Icons.school,
                  ),
                ],
              );
            }
            // Side by side for larger screens
            return Row(
              children: [
                Expanded(
                  child: _buildProgressCard(
                    context,
                    title: 'Fitness',
                    progress: 0.75,
                    color: Colors.green,
                    icon: Icons.fitness_center,
                  ),
                ),
                SizedBox(width: spacing),
                Expanded(
                  child: _buildProgressCard(
                    context,
                    title: 'Academics',
                    progress: 0.60,
                    color: AppTheme.primaryColor,
                    icon: Icons.school,
                  ),
                ),
              ],
            );
          },
        ),
      ],
    );
  }

  Widget _buildProgressCard(
    BuildContext context, {
    required String title,
    required double progress,
    required Color color,
    required IconData icon,
  }) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [
            Colors.white,
            Color.lerp(
              Colors.white,
              Color.lerp(color, Colors.white, 0.3)!,
              0.15,
            )!,
          ],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          // Main shadow
          BoxShadow(
            color: Colors.grey.withValues(alpha: 0.2 * 255),
            blurRadius: 12,
            offset: const Offset(0, 6),
            spreadRadius: 1,
          ),
          // Highlight shadow for 3D effect
          BoxShadow(
            color: Colors.white.withValues(alpha: 0.7 * 255),
            blurRadius: 3,
            offset: const Offset(-1, -1),
            spreadRadius: 0,
          ),
        ],
        border: Border.all(
          color: Colors.grey.withValues(alpha: 0.08 * 255),
          width: 1,
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              // 3D-like icon container
              Container(
                padding: const EdgeInsets.all(10),
                decoration: BoxDecoration(
                  gradient: LinearGradient(
                    colors: [
                      color.withValues(alpha: 150.0),
                      color.withValues(alpha: 100.0),
                    ],
                    begin: Alignment.topLeft,
                    end: Alignment.bottomRight,
                  ),
                  shape: BoxShape.circle,
                  boxShadow: [
                    BoxShadow(
                      color: color.withValues(alpha: 76.0),
                      blurRadius: 8,
                      offset: const Offset(0, 3),
                      spreadRadius: 0,
                    ),
                  ],
                ),
                child: Icon(icon, color: Colors.white, size: 22),
              ),
              const SizedBox(width: 12),
              Text(
                title,
                style: Theme.of(context).textTheme.titleMedium?.copyWith(
                  fontWeight: FontWeight.w600,
                  letterSpacing: 0.2,
                ),
              ),
            ],
          ),
          const SizedBox(height: 20),
          // Modern progress indicator
          Stack(
            children: [
              // Background track
              Container(
                height: 10,
                decoration: BoxDecoration(
                  color: Colors.grey.shade200,
                  borderRadius: BorderRadius.circular(10),
                ),
              ),
              // Progress fill with gradient
              LayoutBuilder(
                builder: (context, constraints) {
                  return Container(
                    height: 10,
                    width: constraints.maxWidth * progress,
                    decoration: BoxDecoration(
                      gradient: LinearGradient(
                        colors: [color, Color.lerp(color, Colors.white, 0.3)!],
                        begin: Alignment.centerLeft,
                        end: Alignment.centerRight,
                      ),
                      borderRadius: BorderRadius.circular(10),
                      boxShadow: [
                        BoxShadow(
                          color: color.withValues(alpha: 0.3 * 255),
                          blurRadius: 4,
                          offset: const Offset(0, 1),
                          spreadRadius: 0,
                        ),
                      ],
                    ),
                  );
                },
              ),
            ],
          ),
          const SizedBox(height: 10),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                '${(progress * 100).toInt()}%',
                style: TextStyle(
                  fontSize: 14,
                  fontWeight: FontWeight.bold,
                  color: color,
                  letterSpacing: 0.5,
                ),
              ),
              Text(
                'Goal: 100%',
                style: TextStyle(fontSize: 12, color: Colors.grey.shade600),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildQuickActions(BuildContext context) {
    final spacing = ResponsiveUtils.getResponsiveSpacing(context);
    final isSmallScreen = ResponsiveUtils.isSmallPhone(context);
    final isTablet = ResponsiveUtils.isTablet(context);

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            Container(
              padding: EdgeInsets.all(isSmallScreen ? 6 : 8),
              decoration: BoxDecoration(
                color: const Color(0xFF4A80F0).withValues(alpha: 0.1 * 255),
                borderRadius: BorderRadius.circular(8),
              ),
              child: Icon(
                Icons.bolt,
                color: const Color(0xFF4A80F0),
                size: isSmallScreen ? 18 : 20,
              ),
            ),
            SizedBox(width: spacing / 2),
            ResponsiveText(
              'Quick Actions',
              mobileFontSize: isSmallScreen ? 16.0 : 18.0,
              tabletFontSize: 20.0,
              desktopFontSize: 22.0,
              fontWeight: FontWeight.bold,
            ),
          ],
        ),
        SizedBox(height: spacing),
        // Responsive grid container
        Container(
          padding: EdgeInsets.all(isSmallScreen ? 12 : 20),
          decoration: BoxDecoration(
            gradient: const LinearGradient(
              colors: [Colors.white, Color(0xFFF8FAFF)],
              begin: Alignment.topCenter,
              end: Alignment.bottomCenter,
            ),
            borderRadius: BorderRadius.circular(isSmallScreen ? 16 : 24),
            boxShadow: [
              BoxShadow(
                color: Colors.grey.withValues(alpha: 0.15 * 255),
                blurRadius: 20,
                offset: const Offset(0, 10),
                spreadRadius: 0,
              ),
              const BoxShadow(
                color: Colors.white,
                blurRadius: 0,
                offset: Offset(0, 0),
                spreadRadius: -1,
              ),
            ],
            border: Border.all(
              color: Colors.grey.withValues(alpha: 0.08 * 255),
              width: 1,
            ),
          ),
          child: LayoutBuilder(
            builder: (context, constraints) {
              // Determine grid layout based on screen size
              int crossAxisCount = 2;
              double childAspectRatio = 1.1;

              if (isTablet) {
                crossAxisCount = 4;
                childAspectRatio = 0.9;
              } else if (constraints.maxWidth < 300) {
                crossAxisCount = 1;
                childAspectRatio = 2.5;
              }

              return GridView.count(
                shrinkWrap: true,
                physics: const NeverScrollableScrollPhysics(),
                crossAxisCount: crossAxisCount,
                mainAxisSpacing: isSmallScreen ? 12 : 20,
                crossAxisSpacing: isSmallScreen ? 12 : 20,
                childAspectRatio: childAspectRatio,
                children: [
                  _buildActionCard(
                    context,
                    title: 'Daily Quiz',
                    subtitle: 'Test your knowledge',
                    icon: Icons.quiz,
                    color: AppTheme.primaryColor,
                    onTap: () {
                      if (onTabChange != null) {
                        onTabChange!(1);
                      }
                    },
                  ),
                  _buildActionCard(
                    context,
                    title: 'Today\'s Workout',
                    subtitle: 'Stay fit & ready',
                    icon: Icons.fitness_center,
                    color: Colors.green,
                    onTap: () {
                      Navigator.of(
                        context,
                      ).pushNamed(AppRoutes.workoutCategories);
                    },
                  ),
                  _buildActionCard(
                    context,
                    title: 'Study Materials',
                    subtitle: 'Prepare for exams',
                    icon: Icons.book,
                    color: Colors.orange,
                    onTap: () {
                      if (onTabChange != null) {
                        onTabChange!(1);
                      }
                    },
                  ),
                  _buildActionCard(
                    context,
                    title: 'Community',
                    subtitle: 'Connect with peers',
                    icon: Icons.forum,
                    color: Colors.purple,
                    onTap: () {
                      Navigator.of(context).pushNamed(AppRoutes.community);
                    },
                  ),
                ],
              );
            },
          ),
        ),
      ],
    );
  }

  Widget _buildActionCard(
    BuildContext context, {
    required String title,
    String? subtitle,
    required IconData icon,
    required Color color,
    required VoidCallback onTap,
  }) {
    final isSmallScreen = ResponsiveUtils.isSmallPhone(context);
    final isTablet = ResponsiveUtils.isTablet(context);

    return GestureDetector(
      onTap: onTap,
      child: Container(
        padding: EdgeInsets.all(isSmallScreen ? 12 : 16),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(isSmallScreen ? 16 : 20),
          boxShadow: [
            BoxShadow(
              color: color.withValues(alpha: 0.1 * 255),
              blurRadius: 15,
              offset: const Offset(0, 8),
              spreadRadius: 1,
            ),
            const BoxShadow(
              color: Colors.white,
              blurRadius: 0,
              offset: Offset(0, 0),
              spreadRadius: -1,
            ),
          ],
          border: Border.all(
            color: color.withValues(alpha: 0.1 * 255),
            width: 1.5,
          ),
        ),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            // Responsive icon container
            Container(
              padding: EdgeInsets.all(isSmallScreen ? 12 : 16),
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  colors: [
                    color.withValues(alpha: 150.0),
                    color.withValues(alpha: 100.0),
                  ],
                  begin: Alignment.topLeft,
                  end: Alignment.bottomRight,
                ),
                shape: BoxShape.circle,
                boxShadow: [
                  BoxShadow(
                    color: color.withValues(alpha: 102.0),
                    blurRadius: 12,
                    offset: const Offset(0, 6),
                    spreadRadius: 0,
                  ),
                  const BoxShadow(
                    color: Colors.white,
                    blurRadius: 0,
                    offset: Offset(0, 0),
                    spreadRadius: -1,
                  ),
                ],
              ),
              child: Icon(
                icon,
                color: Colors.white,
                size: isSmallScreen ? 24 : (isTablet ? 28 : 32),
              ),
            ),
            SizedBox(height: isSmallScreen ? 8 : 12),
            ResponsiveText(
              title,
              mobileFontSize: isSmallScreen ? 12.0 : 14.0,
              tabletFontSize: 15.0,
              desktopFontSize: 16.0,
              fontWeight: FontWeight.w600,
              color: Color.lerp(color, Colors.black87, 0.5),
              textAlign: TextAlign.center,
              maxLines: 2,
              overflow: TextOverflow.ellipsis,
            ),
            if (subtitle != null) ...[
              SizedBox(height: isSmallScreen ? 2 : 4),
              ResponsiveText(
                subtitle,
                mobileFontSize: isSmallScreen ? 10.0 : 11.0,
                tabletFontSize: 12.0,
                desktopFontSize: 12.0,
                color: Colors.grey.shade600,
                fontWeight: FontWeight.w500,
                textAlign: TextAlign.center,
                maxLines: 2,
                overflow: TextOverflow.ellipsis,
              ),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildUpcomingEvents(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            Container(
              padding: const EdgeInsets.all(8),
              decoration: BoxDecoration(
                color: const Color(0xFF4A80F0).withValues(alpha: 0.1 * 255),
                borderRadius: BorderRadius.circular(8),
              ),
              child: const Icon(
                Icons.event_available,
                color: Color(0xFF4A80F0),
                size: 20,
              ),
            ),
            const SizedBox(width: 10),
            Text(
              'Upcoming Events',
              style: Theme.of(context).textTheme.titleLarge?.copyWith(
                fontWeight: FontWeight.bold,
                letterSpacing: 0.3,
              ),
            ),
          ],
        ),
        const SizedBox(height: 20),
        // Timeline-like events container
        Container(
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(24),
            boxShadow: [
              BoxShadow(
                color: Colors.grey.withValues(alpha: 0.15 * 255),
                blurRadius: 20,
                offset: const Offset(0, 10),
                spreadRadius: 0,
              ),
            ],
          ),
          child: Column(
            children: [
              // Header with calendar icon
              Container(
                padding: const EdgeInsets.all(16),
                decoration: BoxDecoration(
                  gradient: LinearGradient(
                    colors: [const Color(0xFF4A80F0), const Color(0xFF1A56E0)],
                    begin: Alignment.topLeft,
                    end: Alignment.bottomRight,
                  ),
                  borderRadius: const BorderRadius.vertical(
                    top: Radius.circular(24),
                  ),
                ),
                child: Row(
                  children: [
                    Container(
                      padding: const EdgeInsets.all(10),
                      decoration: BoxDecoration(
                        color: Colors.white.withValues(alpha: 0.2 * 255),
                        shape: BoxShape.circle,
                      ),
                      child: const Icon(
                        Icons.calendar_month,
                        color: Colors.white,
                        size: 24,
                      ),
                    ),
                    const SizedBox(width: 12),
                    const Text(
                      'This Month\'s Schedule',
                      style: TextStyle(
                        color: Colors.white,
                        fontSize: 19,
                        fontWeight: FontWeight.bold,
                        letterSpacing: 0.5,
                        shadows: [
                          Shadow(
                            color: Colors.black38,
                            blurRadius: 3,
                            offset: Offset(0, 1),
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
              ),
              // Event items
              _buildEventItem(
                context,
                title: 'Physical Fitness Test',
                date: 'May 15, 2023 • 08:00 AM',
                description:
                    'Prepare for the upcoming physical fitness assessment',
                icon: Icons.fitness_center,
                color: Colors.green,
                isFirst: true,
              ),
              _buildEventItem(
                context,
                title: 'Mock Exam',
                date: 'May 20, 2023 • 10:00 AM',
                description:
                    'Practice test to prepare for the final examination',
                icon: Icons.school,
                color: AppTheme.primaryColor,
              ),
              _buildEventItem(
                context,
                title: 'Community Webinar',
                date: 'May 25, 2023 • 04:00 PM',
                description: 'Join the live session with successful candidates',
                icon: Icons.video_call,
                color: Colors.orange,
                isLast: true,
              ),
            ],
          ),
        ),
        // View all button
        Padding(
          padding: const EdgeInsets.only(top: 16, left: 8, right: 8),
          child: GestureDetector(
            onTap: () {
              // Navigate to full calendar/events screen
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(
                  content: Text('Full calendar view coming soon!'),
                ),
              );
            },
            child: Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Text(
                  'View Full Calendar',
                  style: TextStyle(
                    color: const Color(0xFF4A80F0),
                    fontWeight: FontWeight.w600,
                    fontSize: 14,
                  ),
                ),
                const SizedBox(width: 4),
                const Icon(
                  Icons.arrow_forward,
                  color: Color(0xFF4A80F0),
                  size: 16,
                ),
              ],
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildEventItem(
    BuildContext context, {
    required String title,
    required String date,
    required String description,
    required IconData icon,
    required Color color,
    bool isFirst = false,
    bool isLast = false,
  }) {
    return Container(
      padding: const EdgeInsets.symmetric(vertical: 16, horizontal: 20),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.vertical(
          top: isFirst ? const Radius.circular(0) : Radius.zero,
          bottom: isLast ? const Radius.circular(24) : Radius.zero,
        ),
      ),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Timeline indicator with icon
          Column(
            children: [
              // 3D-like icon container
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  gradient: LinearGradient(
                    colors: [
                      color.withValues(alpha: 0.2 * 255),
                      color.withValues(alpha: 0.1 * 255),
                    ],
                    begin: Alignment.topLeft,
                    end: Alignment.bottomRight,
                  ),
                  borderRadius: BorderRadius.circular(14),
                  boxShadow: [
                    BoxShadow(
                      color: color.withValues(alpha: 0.3 * 255),
                      blurRadius: 10,
                      offset: const Offset(0, 4),
                      spreadRadius: 0,
                    ),
                    BoxShadow(
                      color: Colors.white,
                      blurRadius: 0,
                      offset: const Offset(0, 0),
                      spreadRadius: -1,
                    ),
                  ],
                  border: Border.all(
                    color: color.withValues(alpha: 0.1 * 255),
                    width: 1,
                  ),
                ),
                child: Icon(icon, color: color, size: 24),
              ),
              // Timeline connector
              if (!isLast)
                Container(
                  width: 2,
                  height: 40,
                  margin: const EdgeInsets.only(top: 8),
                  decoration: BoxDecoration(
                    gradient: LinearGradient(
                      colors: [
                        color.withValues(alpha: 0.5 * 255),
                        color.withValues(alpha: 0.1 * 255),
                      ],
                      begin: Alignment.topCenter,
                      end: Alignment.bottomCenter,
                    ),
                  ),
                ),
            ],
          ),
          const SizedBox(width: 16),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  title,
                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.w700,
                    letterSpacing: 0.3,
                    fontSize: 17,
                    color: Colors.black87,
                  ),
                ),
                const SizedBox(height: 8),
                // Date badge with 3D effect
                Container(
                  padding: const EdgeInsets.symmetric(
                    horizontal: 10,
                    vertical: 6,
                  ),
                  decoration: BoxDecoration(
                    gradient: LinearGradient(
                      colors: [
                        color.withValues(alpha: 0.15 * 255),
                        color.withValues(alpha: 0.05 * 255),
                      ],
                      begin: Alignment.topLeft,
                      end: Alignment.bottomRight,
                    ),
                    borderRadius: BorderRadius.circular(30),
                    boxShadow: [
                      BoxShadow(
                        color: color.withValues(alpha: 0.1 * 255),
                        blurRadius: 4,
                        offset: const Offset(0, 2),
                        spreadRadius: 0,
                      ),
                    ],
                  ),
                  child: Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Icon(Icons.access_time, size: 14, color: color),
                      const SizedBox(width: 6),
                      Text(
                        date,
                        style: TextStyle(
                          fontSize: 12,
                          fontWeight: FontWeight.w600,
                          color: color,
                          letterSpacing: 0.3,
                        ),
                      ),
                    ],
                  ),
                ),
                const SizedBox(height: 10),
                Text(
                  description,
                  style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                    height: 1.4,
                    fontSize: 14,
                    color: Colors.grey.shade800,
                    fontWeight: FontWeight.w500,
                  ),
                ),
                const SizedBox(height: 8),
                // Action button
                Align(
                  alignment: Alignment.centerRight,
                  child: TextButton(
                    onPressed: () {
                      // Show event details
                      ScaffoldMessenger.of(context).showSnackBar(
                        SnackBar(content: Text('$title details coming soon!')),
                      );
                    },
                    style: TextButton.styleFrom(
                      padding: const EdgeInsets.symmetric(
                        horizontal: 12,
                        vertical: 6,
                      ),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(20),
                      ),
                    ),
                    child: Row(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        Text(
                          'Details',
                          style: TextStyle(
                            color: color,
                            fontWeight: FontWeight.w600,
                            fontSize: 12,
                          ),
                        ),
                        const SizedBox(width: 4),
                        Icon(Icons.arrow_forward, color: color, size: 14),
                      ],
                    ),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildRecommendedContent(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            Container(
              padding: const EdgeInsets.all(8),
              decoration: BoxDecoration(
                color: const Color(0xFF4A80F0).withValues(alpha: 0.1 * 255),
                borderRadius: BorderRadius.circular(8),
              ),
              child: const Icon(
                Icons.recommend,
                color: Color(0xFF4A80F0),
                size: 20,
              ),
            ),
            const SizedBox(width: 10),
            Text(
              'Recommended for You',
              style: Theme.of(context).textTheme.titleLarge?.copyWith(
                fontWeight: FontWeight.bold,
                letterSpacing: 0.3,
              ),
            ),
          ],
        ),
        const SizedBox(height: 20),
        // Modern 3D-like carousel
        Container(
          height: 260,
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(24),
            boxShadow: [
              BoxShadow(
                color: Colors.grey.withValues(alpha: 0.15 * 255),
                blurRadius: 20,
                offset: const Offset(0, 10),
                spreadRadius: 0,
              ),
            ],
          ),
          child: Column(
            children: [
              // Header
              Container(
                padding: const EdgeInsets.all(16),
                decoration: const BoxDecoration(
                  color: Color(0xFFF8FAFF),
                  borderRadius: BorderRadius.vertical(top: Radius.circular(24)),
                ),
                child: Row(
                  children: [
                    const Icon(
                      Icons.auto_awesome,
                      color: Color(0xFF4A80F0),
                      size: 20,
                    ),
                    const SizedBox(width: 8),
                    Text(
                      'Personalized Content',
                      style: TextStyle(
                        color: const Color(0xFF4A80F0),
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                        letterSpacing: 0.3,
                      ),
                    ),
                    const Spacer(),
                    TextButton(
                      onPressed: () {
                        // Navigate to all content
                        ScaffoldMessenger.of(context).showSnackBar(
                          const SnackBar(
                            content: Text('All content view coming soon!'),
                          ),
                        );
                      },
                      child: const Text('View All'),
                    ),
                  ],
                ),
              ),
              // Content cards
              Expanded(
                child: ListView(
                  padding: const EdgeInsets.all(16),
                  scrollDirection: Axis.horizontal,
                  children: [
                    _buildRecommendationCard(
                      context,
                      title: 'Push-up Techniques',
                      category: 'Fitness',
                      description:
                          'Master the perfect form for maximum results',
                      imageUrl: 'assets/images/content/pushups.jpg',
                      color: Colors.green,
                      onTap: () {
                        // Navigate to fitness content
                        Navigator.of(
                          context,
                        ).pushNamed(AppRoutes.workoutCategories);
                      },
                    ),
                    _buildRecommendationCard(
                      context,
                      title: 'Military History',
                      category: 'Academics',
                      description:
                          'Learn about the rich history of the Nigerian military',
                      imageUrl: 'assets/images/content/history.jpg',
                      color: AppTheme.primaryColor,
                      onTap: () {
                        // Navigate to academics content
                        ScaffoldMessenger.of(context).showSnackBar(
                          const SnackBar(
                            content: Text(
                              'Military History content coming soon!',
                            ),
                          ),
                        );
                      },
                    ),
                    _buildRecommendationCard(
                      context,
                      title: 'Interview Tips',
                      category: 'Career',
                      description:
                          'Prepare for your military interview with confidence',
                      imageUrl: 'assets/images/content/interview.jpg',
                      color: Colors.orange,
                      onTap: () {
                        // Navigate to career content
                        ScaffoldMessenger.of(context).showSnackBar(
                          const SnackBar(
                            content: Text(
                              'Interview Tips content coming soon!',
                            ),
                          ),
                        );
                      },
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
        // Bottom padding for scroll
        const SizedBox(height: 20),
      ],
    );
  }

  Widget _buildRecommendationCard(
    BuildContext context, {
    required String title,
    required String category,
    String? description,
    required String imageUrl,
    required Color color,
    required VoidCallback onTap,
  }) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        width: 280,
        height: 300,
        margin: const EdgeInsets.only(right: 20),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(20),
          boxShadow: [
            BoxShadow(
              color: color.withValues(alpha: 0.1 * 255),
              blurRadius: 15,
              offset: const Offset(0, 8),
              spreadRadius: 0,
            ),
            BoxShadow(
              color: Colors.white,
              blurRadius: 0,
              offset: const Offset(0, 0),
              spreadRadius: -1,
            ),
          ],
          border: Border.all(
            color: color.withValues(alpha: 0.1 * 255),
            width: 1,
          ),
        ),
        child: SingleChildScrollView(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Header container
              Container(
                height: 120,
                decoration: BoxDecoration(
                  gradient: LinearGradient(
                    colors: [
                      color.withValues(alpha: 0.2 * 255),
                      color.withValues(alpha: 0.1 * 255),
                    ],
                    begin: Alignment.topLeft,
                    end: Alignment.bottomRight,
                  ),
                  borderRadius: const BorderRadius.vertical(
                    top: Radius.circular(20),
                  ),
                ),
                child: Stack(
                  children: [
                    // Background pattern
                    Positioned(
                      right: -20,
                      bottom: -20,
                      child: Icon(
                        _getCategoryIcon(category),
                        size: 100,
                        color: color.withValues(alpha: 0.2 * 255),
                      ),
                    ),
                    // Content info
                    Padding(
                      padding: const EdgeInsets.all(16),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          // Category badge
                          Container(
                            padding: const EdgeInsets.symmetric(
                              horizontal: 12,
                              vertical: 6,
                            ),
                            decoration: BoxDecoration(
                              color: Colors.white.withValues(alpha: 0.9 * 255),
                              borderRadius: BorderRadius.circular(30),
                              boxShadow: [
                                BoxShadow(
                                  color: color.withValues(alpha: 0.2 * 255),
                                  blurRadius: 8,
                                  offset: const Offset(0, 4),
                                  spreadRadius: 0,
                                ),
                              ],
                            ),
                            child: Row(
                              mainAxisSize: MainAxisSize.min,
                              children: [
                                Icon(
                                  _getCategoryIcon(category),
                                  color: color,
                                  size: 14,
                                ),
                                const SizedBox(width: 6),
                                Text(
                                  category,
                                  style: TextStyle(
                                    fontSize: 12,
                                    fontWeight: FontWeight.w600,
                                    color: color,
                                    letterSpacing: 0.5,
                                  ),
                                ),
                              ],
                            ),
                          ),
                          // Title with shadow for readability
                          Text(
                            title,
                            style: const TextStyle(
                              fontSize: 20,
                              fontWeight: FontWeight.bold,
                              color: Colors.white,
                              letterSpacing: 0.5,
                              shadows: [
                                Shadow(
                                  color: Colors.black54,
                                  blurRadius: 3,
                                  offset: Offset(0, 1),
                                ),
                                Shadow(
                                  color: Colors.black38,
                                  blurRadius: 10,
                                  offset: Offset(0, 3),
                                ),
                              ],
                            ),
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
              ),
              // Card content
              Padding(
                padding: const EdgeInsets.all(16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    if (description != null) ...[
                      Text(
                        description,
                        style: TextStyle(
                          fontSize: 14,
                          color: Colors.grey.shade800,
                          fontWeight: FontWeight.w500,
                          height: 1.4,
                          letterSpacing: 0.2,
                        ),
                        maxLines: 2,
                        overflow: TextOverflow.ellipsis,
                      ),
                      const SizedBox(height: 12),
                    ],
                    // Action button
                    Container(
                      width: double.infinity,
                      padding: const EdgeInsets.symmetric(vertical: 10),
                      decoration: BoxDecoration(
                        gradient: LinearGradient(
                          colors: [
                            color,
                            Color.lerp(color, Colors.white, 0.3)!,
                          ],
                          begin: Alignment.centerLeft,
                          end: Alignment.centerRight,
                        ),
                        borderRadius: BorderRadius.circular(30),
                        boxShadow: [
                          BoxShadow(
                            color: color.withValues(alpha: 0.3 * 255),
                            blurRadius: 8,
                            offset: const Offset(0, 4),
                            spreadRadius: 0,
                          ),
                        ],
                      ),
                      child: const Row(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Icon(
                            Icons.play_circle_filled,
                            color: Colors.white,
                            size: 18,
                          ),
                          SizedBox(width: 8),
                          Text(
                            'Start Learning',
                            style: TextStyle(
                              color: Colors.white,
                              fontWeight: FontWeight.bold,
                              fontSize: 15,
                              letterSpacing: 0.5,
                              shadows: [
                                Shadow(
                                  color: Colors.black26,
                                  blurRadius: 2,
                                  offset: Offset(0, 1),
                                ),
                              ],
                            ),
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  // Helper method to get icon based on category
  IconData _getCategoryIcon(String category) {
    switch (category.toLowerCase()) {
      case 'fitness':
        return Icons.fitness_center;
      case 'academics':
        return Icons.school;
      case 'career':
        return Icons.work;
      default:
        return Icons.article;
    }
  }
}
