import 'package:flutter/material.dart';
import 'package:fit_4_force/core/utils/text_accessibility.dart';
import 'package:fit_4_force/core/theme/app_theme.dart';

/// A text widget that automatically ensures accessibility and readability
class AccessibleText extends StatelessWidget {
  final String text;
  final Color? backgroundColor;
  final Color? preferredColor;
  final double? fontSize;
  final FontWeight? fontWeight;
  final TextAlign? textAlign;
  final int? maxLines;
  final TextOverflow? overflow;
  final bool forceHighContrast;
  final bool addShadow;
  final TextStyle? style;

  const AccessibleText(
    this.text, {
    super.key,
    this.backgroundColor,
    this.preferredColor,
    this.fontSize,
    this.fontWeight,
    this.textAlign,
    this.maxLines,
    this.overflow,
    this.forceHighContrast = false,
    this.addShadow = false,
    this.style,
  });

  /// Create text for gradient backgrounds
  const AccessibleText.onGradient(
    this.text, {
    super.key,
    required List<Color> gradientColors,
    this.fontSize,
    this.fontWeight,
    this.textAlign,
    this.maxLines,
    this.overflow,
    this.forceHighContrast = false,
    this.addShadow = true,
    this.style,
  }) : backgroundColor = null,
       preferredColor = null;

  /// Create text for primary color backgrounds
  const AccessibleText.onPrimary(
    this.text, {
    super.key,
    this.fontSize,
    this.fontWeight,
    this.textAlign,
    this.maxLines,
    this.overflow,
    this.forceHighContrast = false,
    this.addShadow = false,
    this.style,
  }) : backgroundColor = AppTheme.primaryColor,
       preferredColor = AppTheme.textOnPrimary;

  /// Create text for secondary color backgrounds
  const AccessibleText.onSecondary(
    this.text, {
    super.key,
    this.fontSize,
    this.fontWeight,
    this.textAlign,
    this.maxLines,
    this.overflow,
    this.forceHighContrast = false,
    this.addShadow = false,
    this.style,
  }) : backgroundColor = AppTheme.secondaryColor,
       preferredColor = AppTheme.textOnSecondary;

  /// Create text for success color backgrounds
  const AccessibleText.onSuccess(
    this.text, {
    super.key,
    this.fontSize,
    this.fontWeight,
    this.textAlign,
    this.maxLines,
    this.overflow,
    this.forceHighContrast = false,
    this.addShadow = false,
    this.style,
  }) : backgroundColor = AppTheme.successColor,
       preferredColor = AppTheme.textOnSuccess;

  /// Create text for warning color backgrounds
  const AccessibleText.onWarning(
    this.text, {
    super.key,
    this.fontSize,
    this.fontWeight,
    this.textAlign,
    this.maxLines,
    this.overflow,
    this.forceHighContrast = false,
    this.addShadow = false,
    this.style,
  }) : backgroundColor = AppTheme.warningColor,
       preferredColor = AppTheme.textOnWarning;

  /// Create text for error color backgrounds
  const AccessibleText.onError(
    this.text, {
    super.key,
    this.fontSize,
    this.fontWeight,
    this.textAlign,
    this.maxLines,
    this.overflow,
    this.forceHighContrast = false,
    this.addShadow = false,
    this.style,
  }) : backgroundColor = AppTheme.errorColor,
       preferredColor = AppTheme.textOnError;

  /// Create text for premium color backgrounds
  const AccessibleText.onPremium(
    this.text, {
    super.key,
    this.fontSize,
    this.fontWeight,
    this.textAlign,
    this.maxLines,
    this.overflow,
    this.forceHighContrast = false,
    this.addShadow = false,
    this.style,
  }) : backgroundColor = AppTheme.premiumColor,
       preferredColor = AppTheme.textOnPremium;

  @override
  Widget build(BuildContext context) {
    Color textColor;
    
    if (backgroundColor != null) {
      textColor = TextAccessibility.getAccessibleTextColor(
        backgroundColor: backgroundColor!,
        preferredColor: preferredColor,
        forceHighContrast: forceHighContrast,
      );
    } else {
      // Default to theme text color
      textColor = preferredColor ?? Theme.of(context).textTheme.bodyLarge?.color ?? Colors.black;
    }

    final textStyle = TextStyle(
      color: textColor,
      fontSize: fontSize,
      fontWeight: fontWeight,
      shadows: addShadow ? [
        Shadow(
          color: textColor == Colors.white ? Colors.black54 : Colors.white54,
          blurRadius: 2.0,
          offset: const Offset(0, 1),
        ),
      ] : null,
    );

    return Text(
      text,
      style: style?.merge(textStyle) ?? textStyle,
      textAlign: textAlign,
      maxLines: maxLines,
      overflow: overflow,
    );
  }
}

/// A container with accessible text that automatically adjusts text color
class AccessibleContainer extends StatelessWidget {
  final Widget child;
  final Color? backgroundColor;
  final List<Color>? gradientColors;
  final EdgeInsetsGeometry? padding;
  final EdgeInsetsGeometry? margin;
  final BorderRadius? borderRadius;
  final List<BoxShadow>? boxShadow;
  final Border? border;
  final double? width;
  final double? height;

  const AccessibleContainer({
    super.key,
    required this.child,
    this.backgroundColor,
    this.gradientColors,
    this.padding,
    this.margin,
    this.borderRadius,
    this.boxShadow,
    this.border,
    this.width,
    this.height,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      width: width,
      height: height,
      padding: padding,
      margin: margin,
      decoration: BoxDecoration(
        color: gradientColors == null ? backgroundColor : null,
        gradient: gradientColors != null
            ? LinearGradient(
                colors: gradientColors!,
                begin: Alignment.topLeft,
                end: Alignment.bottomRight,
              )
            : null,
        borderRadius: borderRadius,
        boxShadow: boxShadow,
        border: border,
      ),
      child: child,
    );
  }
}

/// A card with accessible text
class AccessibleCard extends StatelessWidget {
  final Widget child;
  final Color? backgroundColor;
  final List<Color>? gradientColors;
  final EdgeInsetsGeometry? padding;
  final EdgeInsetsGeometry? margin;
  final BorderRadius? borderRadius;
  final double? elevation;
  final VoidCallback? onTap;

  const AccessibleCard({
    super.key,
    required this.child,
    this.backgroundColor,
    this.gradientColors,
    this.padding,
    this.margin,
    this.borderRadius,
    this.elevation,
    this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    final card = Card(
      color: gradientColors == null ? backgroundColor : null,
      elevation: elevation ?? 2,
      margin: margin,
      shape: RoundedRectangleBorder(
        borderRadius: borderRadius ?? BorderRadius.circular(12),
      ),
      child: gradientColors != null
          ? Container(
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  colors: gradientColors!,
                  begin: Alignment.topLeft,
                  end: Alignment.bottomRight,
                ),
                borderRadius: borderRadius ?? BorderRadius.circular(12),
              ),
              child: Padding(
                padding: padding ?? const EdgeInsets.all(16),
                child: child,
              ),
            )
          : Padding(
              padding: padding ?? const EdgeInsets.all(16),
              child: child,
            ),
    );

    if (onTap != null) {
      return InkWell(
        onTap: onTap,
        borderRadius: borderRadius ?? BorderRadius.circular(12),
        child: card,
      );
    }

    return card;
  }
}
