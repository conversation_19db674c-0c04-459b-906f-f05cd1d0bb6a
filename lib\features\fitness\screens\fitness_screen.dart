import 'package:flutter/material.dart';
import 'package:fit_4_force/core/theme/app_theme.dart';
import 'package:fit_4_force/core/utils/navigation_service.dart';
import 'package:fit_4_force/core/utils/responsive_utils.dart';
import 'package:fit_4_force/core/widgets/responsive_widgets.dart';
import 'package:fit_4_force/shared/models/user_model.dart';
import 'package:fit_4_force/features/fitness/models/workout_model.dart';
import 'package:fit_4_force/features/fitness/services/workout_service.dart';
import 'package:fit_4_force/features/fitness/screens/recovery_screen.dart';
import 'package:fit_4_force/features/fitness/screens/challenges_screen.dart';
import 'package:fit_4_force/features/fitness/screens/nutrition_screen.dart';
import 'package:fit_4_force/features/fitness/screens/custom_workout_screen.dart';
import 'package:fit_4_force/features/fitness/screens/workout_day_screen.dart';

class FitnessScreen extends StatefulWidget {
  final UserModel user;

  const FitnessScreen({super.key, required this.user});

  @override
  State<FitnessScreen> createState() => _FitnessScreenState();
}

class _FitnessScreenState extends State<FitnessScreen>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;
  final WorkoutService _workoutService = WorkoutService();

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 3, vsync: this);
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  void _showWorkoutExercises(
    BuildContext context,
    String categoryName,
    List<String> exercises,
    Color color,
    IconData icon,
  ) {
    // Limit exercises for non-premium users
    final isPremium = widget.user.isPremium;
    final displayExercises = isPremium ? exercises : exercises.take(1).toList();

    showDialog(
      context: context,
      builder: (context) {
        return Dialog(
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(20),
          ),
          child: Container(
            padding: const EdgeInsets.all(20),
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(20),
              color: Colors.white,
              boxShadow: [
                BoxShadow(
                  color: color.withValues(alpha: 50.0),
                  blurRadius: 10,
                  spreadRadius: 1,
                ),
              ],
            ),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                // Header
                Row(
                  children: [
                    Container(
                      padding: const EdgeInsets.all(12),
                      decoration: BoxDecoration(
                        gradient: LinearGradient(
                          colors: [
                            color.withValues(alpha: 200.0),
                            color.withValues(alpha: 150.0),
                          ],
                          begin: Alignment.topLeft,
                          end: Alignment.bottomRight,
                        ),
                        shape: BoxShape.circle,
                        boxShadow: [
                          BoxShadow(
                            color: color.withValues(alpha: 100.0),
                            blurRadius: 8,
                            offset: const Offset(0, 3),
                          ),
                        ],
                      ),
                      child: Icon(icon, color: Colors.white, size: 28),
                    ),
                    const SizedBox(width: 16),
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            categoryName,
                            style: const TextStyle(
                              fontWeight: FontWeight.bold,
                              fontSize: 22,
                              color: Colors.black87,
                            ),
                          ),
                          const SizedBox(height: 4),
                          Row(
                            children: [
                              Text(
                                '${displayExercises.length}/${exercises.length} exercises',
                                style: TextStyle(
                                  fontSize: 14,
                                  color: color,
                                  fontWeight: FontWeight.w500,
                                ),
                              ),
                              if (!isPremium) ...[
                                const SizedBox(width: 8),
                                Container(
                                  padding: const EdgeInsets.symmetric(
                                    horizontal: 6,
                                    vertical: 2,
                                  ),
                                  decoration: BoxDecoration(
                                    color: AppTheme.premiumColor,
                                    borderRadius: BorderRadius.circular(4),
                                  ),
                                  child: const Text(
                                    'LIMITED',
                                    style: TextStyle(
                                      color: Colors.white,
                                      fontSize: 10,
                                      fontWeight: FontWeight.bold,
                                    ),
                                  ),
                                ),
                              ],
                            ],
                          ),
                        ],
                      ),
                    ),
                    IconButton(
                      icon: const Icon(Icons.close, color: Colors.black54),
                      onPressed: () => Navigator.pop(context),
                    ),
                  ],
                ),

                const SizedBox(height: 16),
                const Divider(),
                const SizedBox(height: 8),

                // Description
                Container(
                  padding: const EdgeInsets.all(12),
                  decoration: BoxDecoration(
                    color: color.withValues(alpha: 20.0),
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Column(
                    children: [
                      const Text(
                        'These exercises are designed to help you achieve your fitness goals. Tap on any exercise to learn more about proper form and technique.',
                        style: TextStyle(
                          fontSize: 14,
                          color: Colors.black87,
                          height: 1.4,
                        ),
                      ),
                      if (!isPremium) ...[
                        const SizedBox(height: 8),
                        Container(
                          padding: const EdgeInsets.all(8),
                          decoration: BoxDecoration(
                            color: AppTheme.premiumColor.withAlpha(30),
                            borderRadius: BorderRadius.circular(8),
                            border: Border.all(
                              color: AppTheme.premiumColor.withAlpha(100),
                              width: 1,
                            ),
                          ),
                          child: Row(
                            children: [
                              const Icon(
                                Icons.workspace_premium,
                                color: AppTheme.premiumColor,
                                size: 16,
                              ),
                              const SizedBox(width: 8),
                              Expanded(
                                child: Text(
                                  'Upgrade to Premium to access all ${exercises.length} exercises in this category',
                                  style: const TextStyle(
                                    fontSize: 12,
                                    color: AppTheme.premiumDarkColor,
                                    fontWeight: FontWeight.w500,
                                  ),
                                ),
                              ),
                            ],
                          ),
                        ),
                      ],
                    ],
                  ),
                ),

                const SizedBox(height: 16),

                // Exercise List
                Flexible(
                  child: ConstrainedBox(
                    constraints: BoxConstraints(
                      maxHeight: MediaQuery.of(context).size.height * 0.5,
                    ),
                    child: ListView.separated(
                      shrinkWrap: true,
                      itemCount: displayExercises.length + (isPremium ? 0 : 1),
                      separatorBuilder:
                          (context, index) => const Divider(height: 1),
                      itemBuilder: (context, index) {
                        // Show premium upgrade button at the end for non-premium users
                        if (!isPremium && index == displayExercises.length) {
                          return ListTile(
                            contentPadding: const EdgeInsets.symmetric(
                              horizontal: 8,
                              vertical: 8,
                            ),
                            tileColor: AppTheme.premiumColor.withAlpha(15),
                            title: const Text(
                              'Unlock All Exercises',
                              style: TextStyle(
                                fontWeight: FontWeight.bold,
                                fontSize: 16,
                                color: AppTheme.premiumColor,
                              ),
                            ),
                            leading: Container(
                              width: 40,
                              height: 40,
                              decoration: BoxDecoration(
                                color: AppTheme.premiumColor.withAlpha(30),
                                shape: BoxShape.circle,
                                border: Border.all(
                                  color: AppTheme.premiumColor,
                                  width: 1,
                                ),
                              ),
                              child: const Icon(
                                Icons.workspace_premium,
                                color: AppTheme.premiumColor,
                                size: 20,
                              ),
                            ),
                            trailing: const Icon(
                              Icons.arrow_forward_ios,
                              color: AppTheme.premiumColor,
                              size: 16,
                            ),
                            onTap: () {
                              Navigator.pop(context);
                              Navigator.of(context).pushNamed('/premium');
                            },
                          );
                        }

                        return ListTile(
                          contentPadding: const EdgeInsets.symmetric(
                            horizontal: 8,
                            vertical: 4,
                          ),
                          leading: Container(
                            width: 40,
                            height: 40,
                            decoration: BoxDecoration(
                              gradient: LinearGradient(
                                colors: [
                                  color.withValues(alpha: 100.0),
                                  color.withValues(alpha: 50.0),
                                ],
                                begin: Alignment.topLeft,
                                end: Alignment.bottomRight,
                              ),
                              shape: BoxShape.circle,
                            ),
                            child: Center(
                              child: Text(
                                '${index + 1}',
                                style: const TextStyle(
                                  color: Colors.white,
                                  fontWeight: FontWeight.bold,
                                  fontSize: 16,
                                ),
                              ),
                            ),
                          ),
                          title: Text(
                            displayExercises[index],
                            style: const TextStyle(
                              fontWeight: FontWeight.w500,
                              fontSize: 16,
                              color: Colors.black87,
                            ),
                          ),
                          trailing: const Icon(
                            Icons.info_outline,
                            color: Colors.black45,
                            size: 20,
                          ),
                          onTap: () {
                            // In the future, this could navigate to a detailed exercise screen
                            ScaffoldMessenger.of(context).showSnackBar(
                              SnackBar(
                                content: Text(
                                  '${displayExercises[index]} details coming soon!',
                                ),
                                duration: const Duration(seconds: 2),
                              ),
                            );
                          },
                        );
                      },
                    ),
                  ),
                ),

                const SizedBox(height: 16),

                // Action Buttons
                Row(
                  mainAxisAlignment: MainAxisAlignment.end,
                  children: [
                    if (!isPremium)
                      OutlinedButton.icon(
                        icon: const Icon(Icons.workspace_premium),
                        label: const Text('UPGRADE'),
                        style: OutlinedButton.styleFrom(
                          foregroundColor: AppTheme.premiumColor,
                          side: const BorderSide(color: AppTheme.premiumColor),
                          padding: const EdgeInsets.symmetric(
                            horizontal: 16,
                            vertical: 12,
                          ),
                        ),
                        onPressed: () {
                          Navigator.pop(context);
                          Navigator.of(context).pushNamed('/premium');
                        },
                      )
                    else
                      OutlinedButton.icon(
                        icon: const Icon(Icons.bookmark_outline),
                        label: const Text('SAVE'),
                        style: OutlinedButton.styleFrom(
                          foregroundColor: color,
                          side: BorderSide(color: color),
                          padding: const EdgeInsets.symmetric(
                            horizontal: 16,
                            vertical: 12,
                          ),
                        ),
                        onPressed: () {
                          Navigator.pop(context);
                          ScaffoldMessenger.of(context).showSnackBar(
                            SnackBar(
                              content: Text('$categoryName workout saved!'),
                              duration: const Duration(seconds: 2),
                            ),
                          );
                        },
                      ),
                    const SizedBox(width: 12),
                    ElevatedButton.icon(
                      icon: const Icon(Icons.play_arrow),
                      label: const Text('START WORKOUT'),
                      style: ElevatedButton.styleFrom(
                        backgroundColor: color,
                        foregroundColor: Colors.white,
                        padding: const EdgeInsets.symmetric(
                          horizontal: 16,
                          vertical: 12,
                        ),
                      ),
                      onPressed: () {
                        Navigator.pop(context);
                        // This could start a workout with these exercises in the future
                        ScaffoldMessenger.of(context).showSnackBar(
                          SnackBar(
                            content: Text(
                              'Starting $categoryName workout coming soon!',
                            ),
                            duration: const Duration(seconds: 2),
                          ),
                        );
                      },
                    ),
                  ],
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  void _showSearchDialog() {
    final searchController = TextEditingController();
    List<WorkoutModel> searchResults = [];

    showDialog(
      context: context,
      builder: (context) {
        return StatefulBuilder(
          builder: (context, setState) {
            return AlertDialog(
              title: const Text('Search Workouts'),
              content: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  TextField(
                    controller: searchController,
                    decoration: const InputDecoration(
                      hintText: 'Search by name, category, etc.',
                      prefixIcon: Icon(Icons.search),
                      border: OutlineInputBorder(),
                    ),
                    onChanged: (value) {
                      if (value.length >= 2) {
                        setState(() {
                          searchResults = _workoutService.searchWorkouts(value);
                        });
                      } else {
                        setState(() {
                          searchResults = [];
                        });
                      }
                    },
                  ),
                  const SizedBox(height: 16),
                  if (searchResults.isNotEmpty)
                    Expanded(
                      child: ListView.builder(
                        shrinkWrap: true,
                        itemCount: searchResults.length,
                        itemBuilder: (context, index) {
                          final workout = searchResults[index];
                          return ListTile(
                            leading: Container(
                              padding: const EdgeInsets.all(8),
                              decoration: BoxDecoration(
                                color: workout.color.withValues(
                                  alpha: (0.1 * 255).round().toDouble(),
                                ),
                                shape: BoxShape.circle,
                              ),
                              child: Icon(workout.icon, color: workout.color),
                            ),
                            title: Text(workout.name),
                            subtitle: Text(workout.category),
                            onTap: () {
                              Navigator.pop(context);
                              NavigationService().navigateTo(
                                '/workout-detail',
                                arguments: workout.id,
                              );
                            },
                          );
                        },
                      ),
                    )
                  else if (searchController.text.length >= 2)
                    const Text('No workouts found'),
                ],
              ),
              actions: [
                TextButton(
                  onPressed: () => Navigator.pop(context),
                  child: const Text('CLOSE'),
                ),
              ],
            );
          },
        );
      },
    );
  }

  @override
  Widget build(BuildContext context) {
    final layoutType = ResponsiveUtils.getLayoutType(context);
    final supportsMultiPane = ResponsiveUtils.supportsMultiPane(context);

    return Scaffold(
      body:
          supportsMultiPane && layoutType != LayoutType.mobilePortrait
              ? _buildMultiPaneLayout(context)
              : _buildSinglePaneLayout(context),
      floatingActionButton: FloatingActionButton(
        backgroundColor: AppTheme.primaryColor,
        child: const Icon(Icons.add),
        onPressed: () {
          NavigationService().navigateTo('/create-workout');
        },
      ),
    );
  }

  Widget _buildSinglePaneLayout(BuildContext context) {
    return Column(
      children: [
        _buildHeader(),
        _buildTabBar(),
        Expanded(
          child: TabBarView(
            controller: _tabController,
            children: [
              _buildWorkoutsTab(),
              _buildProgressTab(),
              _buildPlansTab(),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildMultiPaneLayout(BuildContext context) {
    return ResponsiveMultiPaneLayout(
      primaryPane: Column(
        children: [
          _buildHeader(),
          _buildTabBar(),
          Expanded(
            child: TabBarView(
              controller: _tabController,
              children: [
                _buildWorkoutsTab(),
                _buildProgressTab(),
                _buildPlansTab(),
              ],
            ),
          ),
        ],
      ),
      secondaryPane: _buildSidePanel(context),
      primaryFlex: 2,
      secondaryFlex: 1,
    );
  }

  Widget _buildSidePanel(BuildContext context) {
    return Container(
      padding: ResponsiveUtils.getResponsivePadding(context),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          ResponsiveText(
            'Quick Actions',
            mobileFontSize: 18.0,
            tabletFontSize: 20.0,
            desktopFontSize: 22.0,
            fontWeight: FontWeight.bold,
          ),
          SizedBox(height: ResponsiveUtils.getResponsiveSpacing(context)),
          _buildQuickActionCard(
            'Nutrition Guide',
            Icons.restaurant,
            Colors.orange,
            () {
              Navigator.of(context).push(
                MaterialPageRoute(
                  builder: (context) => NutritionScreen(user: widget.user),
                ),
              );
            },
          ),
          SizedBox(height: ResponsiveUtils.getResponsiveSpacing(context) / 2),
          _buildQuickActionCard(
            'Challenges',
            Icons.emoji_events,
            Colors.purple,
            () {
              Navigator.of(context).push(
                MaterialPageRoute(
                  builder: (context) => ChallengesScreen(user: widget.user),
                ),
              );
            },
          ),
          SizedBox(height: ResponsiveUtils.getResponsiveSpacing(context) / 2),
          _buildQuickActionCard('Recovery', Icons.spa, Colors.green, () {
            Navigator.of(context).push(
              MaterialPageRoute(
                builder: (context) => RecoveryScreen(user: widget.user),
              ),
            );
          }),
        ],
      ),
    );
  }

  Widget _buildQuickActionCard(
    String title,
    IconData icon,
    Color color,
    VoidCallback onTap,
  ) {
    return ResponsiveCard(
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(
          ResponsiveUtils.getResponsiveBorderRadius(context),
        ),
        child: Padding(
          padding: const EdgeInsets.all(12.0),
          child: Row(
            children: [
              Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: color.withValues(alpha: 0.1 * 255),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Icon(icon, color: color, size: 20),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: ResponsiveText(
                  title,
                  mobileFontSize: 14.0,
                  tabletFontSize: 15.0,
                  desktopFontSize: 16.0,
                  fontWeight: FontWeight.w600,
                ),
              ),
              Icon(Icons.arrow_forward_ios, size: 14, color: Colors.grey[600]),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildHeader() {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [
            const Color(0xFF4A80F0), // Vibrant blue
            const Color(0xFF1A56E0), // Deep blue
          ],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        boxShadow: [
          BoxShadow(
            color: const Color(0xFF4A80F0).withValues(alpha: 0.4 * 255),
            blurRadius: 15,
            offset: const Offset(0, 8),
            spreadRadius: 2,
          ),
        ],
      ),
      child: SafeArea(
        child: Column(
          children: [
            Row(
              children: [
                Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      children: [
                        Text(
                          'Fitness Training',
                          style: const TextStyle(
                            fontSize: 24,
                            fontWeight: FontWeight.bold,
                            color: Colors.white,
                            letterSpacing: 0.5,
                          ),
                        ),
                        if (!widget.user.isPremium) ...[
                          const SizedBox(width: 8),
                          Container(
                            padding: const EdgeInsets.symmetric(
                              horizontal: 8,
                              vertical: 4,
                            ),
                            decoration: BoxDecoration(
                              color: Colors.white.withAlpha(40),
                              borderRadius: BorderRadius.circular(12),
                              border: Border.all(
                                color: Colors.white.withAlpha(100),
                                width: 1,
                              ),
                            ),
                            child: Row(
                              mainAxisSize: MainAxisSize.min,
                              children: [
                                const Icon(
                                  Icons.fitness_center,
                                  color: Colors.white,
                                  size: 12,
                                ),
                                const SizedBox(width: 4),
                                const Text(
                                  'LIMITED',
                                  style: TextStyle(
                                    color: Colors.white,
                                    fontSize: 10,
                                    fontWeight: FontWeight.bold,
                                  ),
                                ),
                              ],
                            ),
                          ),
                        ],
                      ],
                    ),
                    const SizedBox(height: 4),
                    Text(
                      'Welcome, ${widget.user.fullName.split(' ')[0]}',
                      style: const TextStyle(
                        fontSize: 14,
                        color: Colors.white,
                        letterSpacing: 0.3,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ],
                ),
                const Spacer(),
                Container(
                  decoration: BoxDecoration(
                    color: Colors.white.withValues(alpha: 0.2 * 255),
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: IconButton(
                    icon: const Icon(Icons.search, color: Colors.white),
                    onPressed: () {
                      _showSearchDialog();
                    },
                  ),
                ),
                const SizedBox(width: 12),
                Container(
                  decoration: BoxDecoration(
                    color: Colors.white.withValues(alpha: 0.2 * 255),
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: IconButton(
                    icon: const Icon(
                      Icons.notifications_outlined,
                      color: Colors.white,
                    ),
                    onPressed: () {
                      NavigationService().navigateTo('/notifications');
                    },
                  ),
                ),
              ],
            ),
            const SizedBox(height: 20),
            Row(
              children: [
                _buildStatCard('Workouts', '24'),
                _buildStatCard('Calories', '12,500'),
                _buildStatCard('Hours', '18.5'),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildStatCard(String title, String value) {
    return Expanded(
      child: Container(
        margin: const EdgeInsets.symmetric(horizontal: 6),
        padding: const EdgeInsets.symmetric(vertical: 16, horizontal: 8),
        decoration: BoxDecoration(
          color: Colors.white.withAlpha(40),
          borderRadius: BorderRadius.circular(16),
          border: Border.all(color: Colors.white, width: 1),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withAlpha(40),
              blurRadius: 8,
              offset: const Offset(0, 4),
            ),
          ],
        ),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Text(
              value,
              style: const TextStyle(
                fontSize: 22,
                fontWeight: FontWeight.bold,
                color: Colors.white,
                letterSpacing: 0.5,
                shadows: [
                  Shadow(
                    color: Colors.black45,
                    blurRadius: 2,
                    offset: Offset(0, 1),
                  ),
                ],
              ),
            ),
            const SizedBox(height: 6),
            Container(
              padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 2),
              decoration: BoxDecoration(
                color: Colors.black.withAlpha(40),
                borderRadius: BorderRadius.circular(10),
              ),
              child: Text(
                title,
                style: const TextStyle(
                  fontSize: 12,
                  fontWeight: FontWeight.w600,
                  color: Colors.white,
                  letterSpacing: 0.3,
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildTabBar() {
    return Container(
      decoration: BoxDecoration(
        color: Colors.white,
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withValues(
              alpha: (0.1 * 255).round().toDouble(),
            ),
            blurRadius: 6,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: TabBar(
        controller: _tabController,
        labelColor: const Color(0xFF4A80F0),
        unselectedLabelColor: AppTheme.textSecondaryLight,
        indicatorColor: const Color(0xFF4A80F0),
        indicatorWeight: 3,
        labelStyle: const TextStyle(
          fontWeight: FontWeight.bold,
          fontSize: 14,
          letterSpacing: 0.5,
        ),
        unselectedLabelStyle: const TextStyle(
          fontWeight: FontWeight.w500,
          fontSize: 14,
        ),
        tabs: const [
          Tab(text: 'WORKOUTS'),
          Tab(text: 'PROGRESS'),
          Tab(text: 'PLANS'),
        ],
      ),
    );
  }

  Widget _buildWorkoutsTab() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Today\'s Workout',
            style: Theme.of(
              context,
            ).textTheme.titleLarge?.copyWith(fontWeight: FontWeight.bold),
          ),
          const SizedBox(height: 16),
          _buildTodayWorkoutCard(),
          const SizedBox(height: 24),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                'Workout Categories',
                style: Theme.of(
                  context,
                ).textTheme.titleLarge?.copyWith(fontWeight: FontWeight.bold),
              ),
              TextButton(
                onPressed: () {
                  NavigationService().navigateTo('/workout-categories');
                },
                child: Text(
                  'See All',
                  style: TextStyle(
                    color: AppTheme.primaryColor,
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          _buildCategoriesGrid(),
          const SizedBox(height: 24),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                'Recent Workouts',
                style: Theme.of(
                  context,
                ).textTheme.titleLarge?.copyWith(fontWeight: FontWeight.bold),
              ),
              TextButton(
                onPressed: () {
                  NavigationService().navigateTo('/workout-history');
                },
                child: Text(
                  'See All',
                  style: TextStyle(
                    color: AppTheme.primaryColor,
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          _buildRecentWorkoutsList(),
        ],
      ),
    );
  }

  Widget _buildTodayWorkoutCard() {
    return Container(
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [const Color(0xFF4A80F0), const Color(0xFF1A56E0)],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        borderRadius: BorderRadius.circular(20),
        boxShadow: [
          BoxShadow(
            color: const Color(
              0xFF4A80F0,
            ).withValues(alpha: (0.3 * 255).round().toDouble()),
            blurRadius: 15,
            offset: const Offset(0, 8),
            spreadRadius: 2,
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Padding(
            padding: const EdgeInsets.all(24),
            child: Row(
              children: [
                Container(
                  padding: const EdgeInsets.all(14),
                  decoration: BoxDecoration(
                    color: Colors.white.withAlpha(50),
                    shape: BoxShape.circle,
                    border: Border.all(color: Colors.white, width: 1),
                  ),
                  child: const Icon(
                    Icons.fitness_center,
                    color: Colors.white,
                    size: 28,
                  ),
                ),
                const SizedBox(width: 20),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: const [
                      Text(
                        'Full Body Workout',
                        style: TextStyle(
                          fontSize: 22,
                          fontWeight: FontWeight.bold,
                          color: Colors.white,
                          letterSpacing: 0.5,
                        ),
                      ),
                      SizedBox(height: 6),
                      Text(
                        'Military Fitness • 45 min',
                        style: TextStyle(
                          fontSize: 15,
                          color: Colors.white,
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                    ],
                  ),
                ),
                Material(
                  color: Colors.white,
                  elevation: 4,
                  borderRadius: BorderRadius.circular(16),
                  child: InkWell(
                    onTap: () {
                      // Play workout action
                      ScaffoldMessenger.of(context).showSnackBar(
                        const SnackBar(
                          content: Text('Starting workout...'),
                          duration: Duration(seconds: 2),
                        ),
                      );
                    },
                    borderRadius: BorderRadius.circular(16),
                    child: Container(
                      padding: const EdgeInsets.all(12),
                      decoration: BoxDecoration(
                        borderRadius: BorderRadius.circular(16),
                      ),
                      child: const Icon(
                        Icons.play_arrow,
                        color: Color(0xFF4A80F0),
                        size: 28,
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ),
          Container(
            padding: const EdgeInsets.symmetric(vertical: 24, horizontal: 20),
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: const BorderRadius.vertical(
                bottom: Radius.circular(20),
              ),
              boxShadow: [
                BoxShadow(
                  color: Colors.grey.withValues(
                    alpha: (0.1 * 255).round().toDouble(),
                  ),
                  blurRadius: 8,
                  offset: const Offset(0, -4),
                ),
              ],
            ),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceAround,
              children: [
                _buildWorkoutStat('8', 'Exercises'),
                _buildWorkoutStat('350', 'Calories'),
                _buildWorkoutStat('45', 'Minutes'),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildWorkoutStat(String value, String label) {
    return Column(
      children: [
        Text(
          value,
          style: const TextStyle(
            fontSize: 20,
            fontWeight: FontWeight.bold,
            color: Color(0xFF333333),
            letterSpacing: 0.5,
          ),
        ),
        const SizedBox(height: 6),
        Container(
          padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 2),
          decoration: BoxDecoration(
            color: const Color(0xFF4A80F0).withAlpha(20),
            borderRadius: BorderRadius.circular(10),
            border: Border.all(
              color: const Color(0xFF4A80F0).withAlpha(50),
              width: 1,
            ),
          ),
          child: Text(
            label,
            style: const TextStyle(
              fontSize: 12,
              fontWeight: FontWeight.w600,
              color: Color(0xFF4A80F0),
              letterSpacing: 0.2,
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildCategoriesGrid() {
    final categories = [
      {
        'name': 'Fat Loss / HIIT',
        'icon': Icons.local_fire_department,
        'color': const Color(0xFFFF5252),
        'exercises': [
          'Jumping Jacks',
          'Mountain Climbers',
          'Burpees',
          'High Knees',
          'Skater Jumps',
          'Jump Squats',
          'Plank to Push-Up',
          'Sprint Intervals',
          'Kettlebell Swings',
          'Battle Rope Exercises',
          'Box Jumps',
          'Jumping Lunges',
          'Tuck Jumps',
        ],
      },
      {
        'name': 'Strength Building',
        'icon': Icons.fitness_center,
        'color': const Color(0xFF4CAF50),
        'exercises': [
          'Push-Ups (Standard, Wide, Diamond)',
          'Pull-Ups / Chin-Ups',
          'Squats (Bodyweight, Weighted, Pistol)',
          'Lunges (Forward, Reverse, Walking)',
          'Deadlifts (If gym available)',
          'Dumbbell Presses (Flat, Incline, Shoulder)',
          'Resistance Band Rows',
          'Bench Press',
          'Military Press',
          'Barbell Rows',
          'Dips',
          'Bulgarian Split Squats',
          'Kettlebell Turkish Get-ups',
        ],
      },
      {
        'name': 'Military Fitness',
        'icon': Icons.military_tech,
        'color': const Color(0xFF2196F3),
        'exercises': [
          'Bear Crawls',
          'Sandbag Carries',
          'Tire Flips (if available)',
          'Wall Climbs / Box Jumps',
          'Rope Pulls / Sled Drags',
          '1.5-mile run for time',
          'Military sit-up & push-up tests (Timed)',
          'Fireman Carries',
          'Loaded Marches (Rucking)',
          'Obstacle Course Training',
          'Shuttle Runs',
          'Buddy Drags',
          'Combat Conditioning Circuits',
        ],
      },
      {
        'name': 'Core & Flexibility',
        'icon': Icons.accessibility_new,
        'color': const Color(0xFF9C27B0),
        'exercises': [
          'Plank (Front, Side, Reach)',
          'Russian Twists',
          'Bicycle Crunches',
          'Leg Raises',
          'Hip Flexor Stretch',
          'Dynamic Hamstring Stretch',
          'Shoulder Rolls',
          'Cobra Stretch',
          'Dead Bugs',
          'Hollow Body Hold',
          'Bird Dogs',
          'Hanging Leg Raises',
          'Windshield Wipers',
          'Yoga Flow Sequences',
          'Foam Rolling',
        ],
      },
    ];

    return GridView.builder(
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: 2,
        childAspectRatio: 1.5,
        crossAxisSpacing: 16,
        mainAxisSpacing: 16,
      ),
      itemCount: categories.length,
      itemBuilder: (context, index) {
        final category = categories[index];
        final color = category['color'] as Color;

        return Container(
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(16),
            boxShadow: [
              BoxShadow(
                color: Colors.grey.withValues(
                  alpha: (0.15 * 255).round().toDouble(),
                ),
                blurRadius: 10,
                offset: const Offset(0, 5),
                spreadRadius: 0,
              ),
              // Highlight shadow for 3D effect
              BoxShadow(
                color: Colors.white,
                blurRadius: 3,
                offset: const Offset(-1, -1),
                spreadRadius: 0,
              ),
            ],
            border: Border.all(
              color: color.withValues(alpha: (0.1 * 255).round().toDouble()),
              width: 1,
            ),
          ),
          child: Material(
            color: Colors.transparent,
            child: InkWell(
              borderRadius: BorderRadius.circular(16),
              onTap: () {
                // Navigate to the workout day screen with different workout IDs
                String workoutId;
                switch (index) {
                  case 0:
                    workoutId = 'hiit_workout';
                    break;
                  case 1:
                    workoutId = 'strength_workout';
                    break;
                  case 2:
                    workoutId = 'military_workout';
                    break;
                  case 3:
                    workoutId = 'core_flexibility_workout';
                    break;
                  default:
                    workoutId = 'workout1';
                }

                Navigator.push(
                  context,
                  MaterialPageRoute(
                    builder:
                        (context) => WorkoutDayScreen(
                          workoutId: workoutId,
                          dayTitle: 'Day 1: ${category['name']}',
                        ),
                  ),
                );
              },
              child: Padding(
                padding: const EdgeInsets.all(16),
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Container(
                      padding: const EdgeInsets.all(12),
                      decoration: BoxDecoration(
                        color: color,
                        shape: BoxShape.circle,
                        boxShadow: [
                          BoxShadow(
                            color: color.withAlpha(128),
                            blurRadius: 8,
                            offset: const Offset(0, 3),
                            spreadRadius: 0,
                          ),
                        ],
                      ),
                      child: Icon(
                        category['icon'] as IconData,
                        color: Colors.white,
                        size: 24,
                      ),
                    ),
                    const SizedBox(height: 12),
                    Container(
                      padding: const EdgeInsets.symmetric(
                        horizontal: 8,
                        vertical: 4,
                      ),
                      decoration: BoxDecoration(
                        color: color.withValues(alpha: 30.0),
                        borderRadius: BorderRadius.circular(8),
                      ),
                      child: Text(
                        category['name'] as String,
                        style: TextStyle(
                          fontWeight: FontWeight.bold,
                          fontSize: 16,
                          color: Colors.black87, // Changed to ensure visibility
                          letterSpacing: 0.2,
                        ),
                        textAlign: TextAlign.center,
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ),
        );
      },
    );
  }

  Widget _buildRecentWorkoutsList() {
    final workouts = [
      {
        'name': 'Upper Body Strength',
        'duration': '30 min',
        'date': 'Yesterday',
        'icon': Icons.fitness_center,
        'color': const Color(0xFF4A80F0),
      },
      {
        'name': '5K Run',
        'duration': '25 min',
        'date': '2 days ago',
        'icon': Icons.directions_run,
        'color': const Color(0xFF4CAF50),
      },
      {
        'name': 'Core Workout',
        'duration': '20 min',
        'date': '3 days ago',
        'icon': Icons.accessibility_new,
        'color': const Color(0xFFF57C00),
      },
    ];

    return ListView.separated(
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      itemCount: workouts.length,
      separatorBuilder: (context, index) => const SizedBox(height: 16),
      itemBuilder: (context, index) {
        final workout = workouts[index];
        final color = workout['color'] as Color;

        return Container(
          decoration: BoxDecoration(
            gradient: LinearGradient(
              colors: [
                Colors.white,
                Color.lerp(Colors.white, color, 0.05) ?? Colors.white,
              ],
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
            ),
            borderRadius: BorderRadius.circular(16),
            boxShadow: [
              BoxShadow(
                color: Colors.grey.withValues(
                  alpha: (0.15 * 255).round().toDouble(),
                ),
                blurRadius: 10,
                offset: const Offset(0, 5),
                spreadRadius: 0,
              ),
              // Highlight shadow for 3D effect
              BoxShadow(
                color: Colors.white,
                blurRadius: 3,
                offset: const Offset(-1, -1),
                spreadRadius: 0,
              ),
            ],
            border: Border.all(
              color: color.withValues(alpha: (0.1 * 255).round().toDouble()),
              width: 1,
            ),
          ),
          child: Padding(
            padding: const EdgeInsets.all(16),
            child: Row(
              children: [
                Container(
                  padding: const EdgeInsets.all(12),
                  decoration: BoxDecoration(
                    color: color.withAlpha(40),
                    shape: BoxShape.circle,
                    border: Border.all(color: color, width: 1.5),
                  ),
                  child: Icon(
                    workout['icon'] as IconData,
                    color: color,
                    size: 24,
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        workout['name'] as String,
                        style: TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.bold,
                          color: AppTheme.textPrimaryDark,
                          letterSpacing: 0.2,
                        ),
                      ),
                      const SizedBox(height: 4),
                      Text(
                        '${workout['duration']} • ${workout['date']}',
                        style: TextStyle(
                          fontSize: 13,
                          color: AppTheme.textSecondaryLight,
                          fontWeight: FontWeight.w500,
                          letterSpacing: 0.2,
                        ),
                      ),
                    ],
                  ),
                ),
                Material(
                  color: Colors.transparent,
                  child: InkWell(
                    onTap: () {
                      // Play workout action
                      ScaffoldMessenger.of(context).showSnackBar(
                        SnackBar(
                          content: Text(
                            'Starting ${workout['name']} workout...',
                          ),
                          duration: const Duration(seconds: 2),
                        ),
                      );
                    },
                    borderRadius: BorderRadius.circular(20),
                    child: Container(
                      padding: const EdgeInsets.all(10),
                      decoration: BoxDecoration(
                        color: color.withAlpha(30),
                        shape: BoxShape.circle,
                        border: Border.all(color: color, width: 1),
                      ),
                      child: Icon(Icons.play_arrow, color: color, size: 24),
                    ),
                  ),
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  Widget _buildProgressTab() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Fitness Metrics',
            style: Theme.of(
              context,
            ).textTheme.titleLarge?.copyWith(fontWeight: FontWeight.bold),
          ),
          const SizedBox(height: 16),
          _buildMetricsGrid(),
          const SizedBox(height: 24),
          Text(
            'Weekly Activity',
            style: Theme.of(
              context,
            ).textTheme.titleLarge?.copyWith(fontWeight: FontWeight.bold),
          ),
          const SizedBox(height: 16),
          _buildWeeklyActivityChart(),
          const SizedBox(height: 24),
          Text(
            'Physical Test Readiness',
            style: Theme.of(
              context,
            ).textTheme.titleLarge?.copyWith(fontWeight: FontWeight.bold),
          ),
          const SizedBox(height: 16),
          _buildTestReadinessCard(),
        ],
      ),
    );
  }

  Widget _buildMetricsGrid() {
    final metrics = [
      {
        'name': 'Push-ups',
        'value': '45',
        'unit': 'reps',
        'change': '+5',
        'isPositive': true,
      },
      {
        'name': 'Sit-ups',
        'value': '50',
        'unit': 'reps',
        'change': '+8',
        'isPositive': true,
      },
      {
        'name': 'Run Time',
        'value': '12:30',
        'unit': 'min',
        'change': '-0:45',
        'isPositive': true,
      },
      {
        'name': 'Weight',
        'value': '75',
        'unit': 'kg',
        'change': '-2',
        'isPositive': true,
      },
    ];

    return GridView.builder(
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: 2,
        childAspectRatio: 1.5,
        crossAxisSpacing: 16,
        mainAxisSpacing: 16,
      ),
      itemCount: metrics.length,
      itemBuilder: (context, index) {
        final metric = metrics[index];
        return Container(
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(16),
            boxShadow: [
              BoxShadow(
                color: Colors.grey.withValues(alpha: 0.1 * 255),
                blurRadius: 10,
                offset: const Offset(0, 4),
              ),
            ],
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Text(
                metric['name'] as String,
                style: TextStyle(
                  fontSize: 14,
                  color: AppTheme.textSecondaryLight,
                ),
              ),
              const SizedBox(height: 8),
              Row(
                children: [
                  Text(
                    '${metric['value']} ${metric['unit']}',
                    style: TextStyle(
                      fontSize: 20,
                      fontWeight: FontWeight.bold,
                      color: AppTheme.textPrimaryLight,
                    ),
                  ),
                  const Spacer(),
                  Container(
                    padding: const EdgeInsets.symmetric(
                      horizontal: 8,
                      vertical: 4,
                    ),
                    decoration: BoxDecoration(
                      color:
                          (metric['isPositive'] as bool)
                              ? Colors.green.withValues(alpha: 0.1 * 255)
                              : Colors.red.withValues(alpha: 0.1 * 255),
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: Text(
                      metric['change'] as String,
                      style: TextStyle(
                        fontSize: 12,
                        fontWeight: FontWeight.bold,
                        color:
                            (metric['isPositive'] as bool)
                                ? Colors.green
                                : Colors.red,
                      ),
                    ),
                  ),
                ],
              ),
            ],
          ),
        );
      },
    );
  }

  Widget _buildWeeklyActivityChart() {
    // Placeholder for chart
    return Container(
      height: 200,
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withValues(alpha: 0.1 * 255),
            blurRadius: 10,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Center(
        child: Text(
          'Weekly Activity Chart',
          style: TextStyle(color: AppTheme.textSecondaryLight),
        ),
      ),
    );
  }

  Widget _buildTestReadinessCard() {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withValues(alpha: 0.1 * 255),
            blurRadius: 10,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Column(
        children: [
          Row(
            children: [
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: AppTheme.primaryColor.withValues(alpha: 0.1 * 255),
                  shape: BoxShape.circle,
                ),
                child: Icon(
                  Icons.military_tech,
                  color: AppTheme.primaryColor,
                  size: 24,
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Physical Test Readiness',
                      style: Theme.of(context).textTheme.titleMedium?.copyWith(
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                    const SizedBox(height: 4),
                    Text(
                      'Based on your recent performance',
                      style: TextStyle(
                        fontSize: 14,
                        color: AppTheme.textSecondaryLight,
                      ),
                    ),
                  ],
                ),
              ),
              Container(
                padding: const EdgeInsets.symmetric(
                  horizontal: 12,
                  vertical: 6,
                ),
                decoration: BoxDecoration(
                  color: Colors.green.withValues(alpha: 0.1 * 255),
                  borderRadius: BorderRadius.circular(16),
                ),
                child: const Text(
                  '85%',
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                    color: Colors.green,
                  ),
                ),
              ),
            ],
          ),
          const SizedBox(height: 20),
          _buildTestReadinessItem('Push-ups', 0.9),
          const SizedBox(height: 12),
          _buildTestReadinessItem('Sit-ups', 0.85),
          const SizedBox(height: 12),
          _buildTestReadinessItem('2.4km Run', 0.75),
        ],
      ),
    );
  }

  Widget _buildTestReadinessItem(String name, double progress) {
    final color =
        progress >= 0.8
            ? Colors.green
            : progress >= 0.6
            ? Colors.orange
            : Colors.red;

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text(
              name,
              style: TextStyle(
                fontSize: 14,
                fontWeight: FontWeight.w500,
                color: AppTheme.textPrimaryLight,
              ),
            ),
            Text(
              '${(progress * 100).toInt()}%',
              style: TextStyle(
                fontSize: 14,
                fontWeight: FontWeight.bold,
                color: color,
              ),
            ),
          ],
        ),
        const SizedBox(height: 8),
        LinearProgressIndicator(
          value: progress,
          backgroundColor: Colors.grey.shade200,
          valueColor: AlwaysStoppedAnimation<Color>(color),
          borderRadius: BorderRadius.circular(10),
          minHeight: 8,
        ),
      ],
    );
  }

  Widget _buildPlansTab() {
    final plans = [
      {
        'name': 'Basic Training',
        'description': 'Prepare for military physical fitness test',
        'duration': '4 weeks',
        'level': 'Beginner',
        'color': Colors.blue,
      },
      {
        'name': 'Advanced Strength',
        'description': 'Build strength and endurance for military service',
        'duration': '6 weeks',
        'level': 'Intermediate',
        'color': Colors.orange,
      },
      {
        'name': 'Elite Performance',
        'description': 'Advanced training for special forces candidates',
        'duration': '8 weeks',
        'level': 'Advanced',
        'color': Colors.red,
      },
      {
        'name': 'Tactical Fitness',
        'description': 'Functional fitness for military operations',
        'duration': '6 weeks',
        'level': 'Intermediate',
        'color': Colors.purple,
      },
    ];

    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Training Plans',
            style: Theme.of(
              context,
            ).textTheme.titleLarge?.copyWith(fontWeight: FontWeight.bold),
          ),
          const SizedBox(height: 16),
          ListView.separated(
            shrinkWrap: true,
            physics: const NeverScrollableScrollPhysics(),
            itemCount: plans.length,
            separatorBuilder: (context, index) => const SizedBox(height: 16),
            itemBuilder: (context, index) {
              final plan = plans[index];
              return Container(
                decoration: BoxDecoration(
                  color: Colors.white,
                  borderRadius: BorderRadius.circular(16),
                  boxShadow: [
                    BoxShadow(
                      color: Colors.grey.withValues(alpha: 0.1 * 255),
                      blurRadius: 10,
                      offset: const Offset(0, 4),
                    ),
                  ],
                ),
                child: Material(
                  color: Colors.transparent,
                  child: InkWell(
                    borderRadius: BorderRadius.circular(16),
                    onTap: () {
                      NavigationService().navigateTo(
                        '/plan-detail',
                        arguments: plan['id'],
                      );
                    },
                    child: Padding(
                      padding: const EdgeInsets.all(16),
                      child: Row(
                        children: [
                          Container(
                            width: 60,
                            height: 60,
                            decoration: BoxDecoration(
                              color: (plan['color'] as Color).withValues(
                                alpha: 0.1 * 255,
                              ),
                              shape: BoxShape.circle,
                            ),
                            child: Center(
                              child: Icon(
                                Icons.fitness_center,
                                color: plan['color'] as Color,
                                size: 28,
                              ),
                            ),
                          ),
                          const SizedBox(width: 16),
                          Expanded(
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Text(
                                  plan['name'] as String,
                                  style: Theme.of(context).textTheme.titleMedium
                                      ?.copyWith(fontWeight: FontWeight.w600),
                                ),
                                const SizedBox(height: 4),
                                Text(
                                  plan['description'] as String,
                                  style: TextStyle(
                                    fontSize: 14,
                                    color: AppTheme.textSecondaryLight,
                                  ),
                                  maxLines: 2,
                                  overflow: TextOverflow.ellipsis,
                                ),
                                const SizedBox(height: 8),
                                Row(
                                  children: [
                                    _buildPlanTag(
                                      plan['duration'] as String,
                                      Icons.calendar_today,
                                    ),
                                    const SizedBox(width: 8),
                                    _buildPlanTag(
                                      plan['level'] as String,
                                      Icons.fitness_center,
                                    ),
                                  ],
                                ),
                              ],
                            ),
                          ),
                          Icon(
                            Icons.arrow_forward_ios,
                            color: AppTheme.textSecondaryLight,
                            size: 16,
                          ),
                        ],
                      ),
                    ),
                  ),
                ),
              );
            },
          ),
          const SizedBox(height: 24),
          Text(
            'Additional Features',
            style: Theme.of(
              context,
            ).textTheme.titleLarge?.copyWith(fontWeight: FontWeight.bold),
          ),
          const SizedBox(height: 16),
          _buildAdditionalFeaturesGrid(),
        ],
      ),
    );
  }

  Widget _buildAdditionalFeaturesGrid() {
    final features = [
      {
        'name': 'Custom Workout Plans',
        'description': 'Create and manage your own workout plans',
        'icon': Icons.fitness_center,
        'color': Colors.blue,
        'onTap': () {
          Navigator.push(
            context,
            MaterialPageRoute(
              builder: (context) => const CustomWorkoutScreen(),
            ),
          );
        },
      },
      {
        'name': 'Nutrition Guidance',
        'description': 'Personalized meal plans and nutrition tips',
        'icon': Icons.restaurant,
        'color': Colors.green,
        'onTap': () {
          Navigator.push(
            context,
            MaterialPageRoute(
              builder: (context) => NutritionScreen(user: widget.user),
            ),
          );
        },
      },
      {
        'name': 'Recovery Tracking',
        'description': 'Track and optimize your recovery process',
        'icon': Icons.self_improvement,
        'color': Colors.teal,
        'onTap': () {
          Navigator.push(
            context,
            MaterialPageRoute(
              builder: (context) => RecoveryScreen(user: widget.user),
            ),
          );
        },
      },
      {
        'name': 'Fitness Challenges',
        'description': 'Join challenges to test your limits',
        'icon': Icons.emoji_events,
        'color': Colors.amber,
        'onTap': () {
          Navigator.push(
            context,
            MaterialPageRoute(
              builder: (context) => ChallengesScreen(user: widget.user),
            ),
          );
        },
      },
    ];

    return GridView.builder(
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: 2,
        childAspectRatio: 1.0,
        crossAxisSpacing: 16,
        mainAxisSpacing: 16,
      ),
      itemCount: features.length,
      itemBuilder: (context, index) {
        final feature = features[index];
        final color = feature['color'] as Color;
        final onTap = feature['onTap'] as Function();

        return Container(
          decoration: BoxDecoration(
            gradient: LinearGradient(
              colors: [
                Colors.white,
                Color.lerp(Colors.white, color, 0.1) ?? Colors.white,
              ],
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
            ),
            borderRadius: BorderRadius.circular(16),
            boxShadow: [
              BoxShadow(
                color: Colors.grey.withValues(
                  alpha: (0.15 * 255).round().toDouble(),
                ),
                blurRadius: 10,
                offset: const Offset(0, 5),
                spreadRadius: 0,
              ),
              // Highlight shadow for 3D effect
              BoxShadow(
                color: Colors.white,
                blurRadius: 3,
                offset: const Offset(-1, -1),
                spreadRadius: 0,
              ),
            ],
            border: Border.all(
              color: color.withValues(alpha: (0.1 * 255).round().toDouble()),
              width: 1,
            ),
          ),
          child: Material(
            color: Colors.transparent,
            child: InkWell(
              borderRadius: BorderRadius.circular(16),
              onTap: onTap,
              child: Padding(
                padding: const EdgeInsets.all(16),
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Container(
                      padding: const EdgeInsets.all(12),
                      decoration: BoxDecoration(
                        gradient: LinearGradient(
                          colors: [
                            color.withValues(
                              alpha: (0.2 * 255).round().toDouble(),
                            ),
                            color.withValues(
                              alpha: (0.1 * 255).round().toDouble(),
                            ),
                          ],
                          begin: Alignment.topLeft,
                          end: Alignment.bottomRight,
                        ),
                        shape: BoxShape.circle,
                        boxShadow: [
                          BoxShadow(
                            color: color.withValues(
                              alpha: (0.2 * 255).round().toDouble(),
                            ),
                            blurRadius: 8,
                            offset: const Offset(0, 3),
                            spreadRadius: 0,
                          ),
                        ],
                      ),
                      child: Icon(
                        feature['icon'] as IconData,
                        color: color,
                        size: 24,
                      ),
                    ),
                    const SizedBox(height: 12),
                    Text(
                      feature['name'] as String,
                      style: TextStyle(
                        fontWeight: FontWeight.bold,
                        fontSize: 16,
                        color: AppTheme.textPrimaryDark,
                        letterSpacing: 0.2,
                      ),
                      textAlign: TextAlign.center,
                    ),
                    const SizedBox(height: 8),
                    Text(
                      feature['description'] as String,
                      style: TextStyle(
                        fontSize: 12,
                        color: AppTheme.textSecondaryLight,
                      ),
                      textAlign: TextAlign.center,
                      maxLines: 2,
                      overflow: TextOverflow.ellipsis,
                    ),
                  ],
                ),
              ),
            ),
          ),
        );
      },
    );
  }

  Widget _buildPlanTag(String text, IconData icon) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: Colors.grey.shade100,
        borderRadius: BorderRadius.circular(12),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(icon, size: 12, color: AppTheme.textSecondaryLight),
          const SizedBox(width: 4),
          Text(
            text,
            style: TextStyle(fontSize: 12, color: AppTheme.textSecondaryLight),
          ),
        ],
      ),
    );
  }
}
