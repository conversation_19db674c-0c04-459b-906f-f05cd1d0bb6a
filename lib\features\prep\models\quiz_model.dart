import 'package:flutter/material.dart';

/// Model representing a quiz question
class QuizQuestion {
  final String id;
  final String question;
  final List<String> options;
  final int correctOptionIndex;
  final String? explanation;
  final String? imageUrl;

  const QuizQuestion({
    required this.id,
    required this.question,
    required this.options,
    required this.correctOptionIndex,
    this.explanation,
    this.imageUrl,
  });

  // Create a copy of the question with modified properties
  QuizQuestion copyWith({
    String? id,
    String? question,
    List<String>? options,
    int? correctOptionIndex,
    String? explanation,
    String? imageUrl,
  }) {
    return QuizQuestion(
      id: id ?? this.id,
      question: question ?? this.question,
      options: options ?? this.options,
      correctOptionIndex: correctOptionIndex ?? this.correctOptionIndex,
      explanation: explanation ?? this.explanation,
      imageUrl: imageUrl ?? this.imageUrl,
    );
  }

  // Convert question to map
  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'question': question,
      'options': options,
      'correctOptionIndex': correctOptionIndex,
      'explanation': explanation,
      'imageUrl': imageUrl,
    };
  }

  // Create question from map
  factory QuizQuestion.fromMap(Map<String, dynamic> map) {
    return QuizQuestion(
      id: map['id'],
      question: map['question'],
      options: List<String>.from(map['options']),
      correctOptionIndex: map['correctOptionIndex'],
      explanation: map['explanation'],
      imageUrl: map['imageUrl'],
    );
  }
}

/// Model representing a quiz
class QuizModel {
  final String id;
  final String title;
  final String description;
  final String agency;
  final String category;
  final String difficulty; // 'easy', 'medium', 'hard'
  final List<QuizQuestion> questions;
  final int timeLimit; // in minutes
  final bool isPremium;
  final DateTime publishedDate;
  final IconData icon;
  final Color color;
  final int passingScore; // percentage

  const QuizModel({
    required this.id,
    required this.title,
    required this.description,
    required this.agency,
    required this.category,
    required this.difficulty,
    required this.questions,
    required this.timeLimit,
    required this.isPremium,
    required this.publishedDate,
    required this.icon,
    required this.color,
    required this.passingScore,
  });

  // Create a copy of the quiz with modified properties
  QuizModel copyWith({
    String? id,
    String? title,
    String? description,
    String? agency,
    String? category,
    String? difficulty,
    List<QuizQuestion>? questions,
    int? timeLimit,
    bool? isPremium,
    DateTime? publishedDate,
    IconData? icon,
    Color? color,
    int? passingScore,
  }) {
    return QuizModel(
      id: id ?? this.id,
      title: title ?? this.title,
      description: description ?? this.description,
      agency: agency ?? this.agency,
      category: category ?? this.category,
      difficulty: difficulty ?? this.difficulty,
      questions: questions ?? this.questions,
      timeLimit: timeLimit ?? this.timeLimit,
      isPremium: isPremium ?? this.isPremium,
      publishedDate: publishedDate ?? this.publishedDate,
      icon: icon ?? this.icon,
      color: color ?? this.color,
      passingScore: passingScore ?? this.passingScore,
    );
  }

  // Convert quiz to map
  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'title': title,
      'description': description,
      'agency': agency,
      'category': category,
      'difficulty': difficulty,
      'questions': questions.map((q) => q.toMap()).toList(),
      'timeLimit': timeLimit,
      'isPremium': isPremium,
      'publishedDate': publishedDate.toIso8601String(),
      'passingScore': passingScore,
    };
  }

  // Create quiz from map
  factory QuizModel.fromMap(Map<String, dynamic> map) {
    return QuizModel(
      id: map['id'],
      title: map['title'],
      description: map['description'],
      agency: map['agency'],
      category: map['category'],
      difficulty: map['difficulty'],
      questions: List<QuizQuestion>.from(
        map['questions']?.map((x) => QuizQuestion.fromMap(x)),
      ),
      timeLimit: map['timeLimit'],
      isPremium: map['isPremium'] ?? false,
      publishedDate: DateTime.parse(map['publishedDate']),
      icon: _getIconForCategory(map['category']),
      color: _getColorForDifficulty(map['difficulty']),
      passingScore: map['passingScore'] ?? 70,
    );
  }

  // Get icon based on category
  static IconData _getIconForCategory(String category) {
    switch (category) {
      case 'History':
        return Icons.history_edu;
      case 'Mathematics':
        return Icons.calculate;
      case 'English':
        return Icons.spellcheck;
      case 'Current Affairs':
        return Icons.newspaper;
      case 'General Knowledge':
        return Icons.psychology;
      case 'Physical Training':
        return Icons.fitness_center;
      default:
        return Icons.quiz;
    }
  }

  // Get color based on difficulty
  static Color _getColorForDifficulty(String difficulty) {
    switch (difficulty) {
      case 'easy':
        return Colors.green;
      case 'medium':
        return Colors.orange;
      case 'hard':
        return Colors.red;
      default:
        return Colors.blue;
    }
  }
}

/// Model representing a quiz result
class QuizResult {
  final String id;
  final String quizId;
  final String userId;
  final int score; // percentage
  final int timeSpent; // in seconds
  final DateTime completedAt;
  final List<int> userAnswers; // indices of selected options
  final bool passed;

  const QuizResult({
    required this.id,
    required this.quizId,
    required this.userId,
    required this.score,
    required this.timeSpent,
    required this.completedAt,
    required this.userAnswers,
    required this.passed,
  });

  // Convert result to map
  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'quizId': quizId,
      'userId': userId,
      'score': score,
      'timeSpent': timeSpent,
      'completedAt': completedAt.toIso8601String(),
      'userAnswers': userAnswers,
      'passed': passed,
    };
  }

  // Create result from map
  factory QuizResult.fromMap(Map<String, dynamic> map) {
    return QuizResult(
      id: map['id'],
      quizId: map['quizId'],
      userId: map['userId'],
      score: map['score'],
      timeSpent: map['timeSpent'],
      completedAt: DateTime.parse(map['completedAt']),
      userAnswers: List<int>.from(map['userAnswers']),
      passed: map['passed'],
    );
  }
}
