<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Contact Us - Fit4Force</title>
    <link rel="stylesheet" href="styles.css">
    <link rel="icon" type="image/x-icon" href="favicon.ico">
</head>
<body>
    <header>
        <div class="container">
            <div class="logo">
                <h1>🏋️ Fit4Force</h1>
                <p>Contact Us</p>
            </div>
        </div>
    </header>

    <main>
        <div class="container">
            <a href="index.html" class="back-link">Back to Legal Documents</a>
            
            <div class="document-header">
                <h1>Contact Us</h1>
                <div class="document-meta">
                    <p>Get in touch with the Fit4Force team</p>
                </div>
            </div>

            <div class="document-content">
                <h2>📧 Email Support</h2>
                <p>For general inquiries, technical support, or legal matters:</p>
                <p><strong>Email:</strong> <EMAIL></p>
                <p><strong>Response Time:</strong> We typically respond within 24-48 hours</p>

                <h2>📱 In-App Support</h2>
                <p>You can also reach us directly through the Fit4Force mobile app:</p>
                <ul>
                    <li>Open the app and go to Settings</li>
                    <li>Select "Help & Support"</li>
                    <li>Choose "Contact Support"</li>
                    <li>Submit your inquiry with details</li>
                </ul>

                <h2>🏢 Company Information</h2>
                <p><strong>Company:</strong> Nehemiah Technologies<br>
                <strong>App:</strong> Fit4Force</p>

                <h2>⏰ Support Hours</h2>
                <p><strong>Monday - Friday:</strong> 9:00 AM - 6:00 PM (WAT)<br>
                <strong>Saturday:</strong> 10:00 AM - 4:00 PM (WAT)<br>
                <strong>Sunday:</strong> Closed</p>

                <h2>🔍 Before You Contact Us</h2>
                <p>To help us assist you better, please:</p>
                <ul>
                    <li>Check our FAQ section in the app</li>
                    <li>Include your device type and app version</li>
                    <li>Describe the issue in detail</li>
                    <li>Include screenshots if relevant</li>
                </ul>

                <h2>📋 Types of Inquiries</h2>
                
                <h3>Technical Support</h3>
                <p>App crashes, login issues, payment problems, feature requests</p>

                <h3>Content Questions</h3>
                <p>Study materials, fitness programs, agency-specific content</p>

                <h3>Account Issues</h3>
                <p>Profile management, subscription changes, data concerns</p>

                <h3>Legal Matters</h3>
                <p>Privacy concerns, terms of service, intellectual property</p>

                <h3>Business Inquiries</h3>
                <p>Partnerships, content licensing, media requests</p>

                <h2>⚡ Emergency Contact</h2>
                <p>For urgent security issues or critical app problems:</p>
                <p><strong>Emergency Email:</strong> <EMAIL></p>
                <p><em>Please use this only for genuine emergencies</em></p>

                <div style="margin-top: 3rem; padding: 2rem; background: #f8fafc; border-radius: 8px; border-left: 4px solid var(--primary-color);">
                    <h3>💡 Quick Tip</h3>
                    <p>For the fastest response, use the in-app support feature. This automatically includes your device information and app version, helping us resolve your issue more quickly.</p>
                </div>
            </div>
        </div>
    </main>

    <footer>
        <div class="container">
            <p>&copy; 2025 Nehemiah Technologies. All rights reserved.</p>
            <p>Last updated: <span id="lastUpdated"></span></p>
        </div>
    </footer>

    <script>
        document.getElementById('lastUpdated').textContent = new Date().toLocaleDateString();
    </script>
</body>
</html>
