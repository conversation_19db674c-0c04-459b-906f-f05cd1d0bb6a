import 'package:flutter/material.dart';
import 'package:fit_4_force/core/theme/app_theme.dart';
import 'package:fit_4_force/features/community/models/success_story_model.dart';
import 'package:fit_4_force/shared/models/user_model.dart';
import 'package:fit_4_force/shared/widgets/base_button.dart';
import 'package:intl/intl.dart';

class SuccessStoriesScreen extends StatefulWidget {
  final UserModel user;

  const SuccessStoriesScreen({super.key, required this.user});

  @override
  State<SuccessStoriesScreen> createState() => _SuccessStoriesScreenState();
}

class _SuccessStoriesScreenState extends State<SuccessStoriesScreen> {
  final TextEditingController _searchController = TextEditingController();
  String _selectedAgency = 'All';
  final List<String> _agencies = [
    'All',
    'Nigerian Army',
    'Navy',
    'Air Force',
    'DSSC',
    'NDA',
    'NSCDC',
    'EFCC',
  ];

  // Mock data for success stories
  final List<SuccessStoryModel> _successStories = [
    SuccessStoryModel(
      id: '1',
      createdAt: DateTime.now().subtract(const Duration(days: 30)),
      userId: 'user1',
      userName: '<PERSON>e',
      userProfileImageUrl: null,
      title: 'My Journey to Becoming a Nigerian Army Officer',
      content:
          'After years of preparation and dedication, I finally achieved my dream of becoming an officer in the Nigerian Army. Here\'s my story and the lessons I learned along the way...',
      agency: 'Nigerian Army',
      position: 'Lieutenant',
      successDate: DateTime.now().subtract(const Duration(days: 90)),
      tags: ['Officer', 'Training', 'Perseverance'],
      likesCount: 156,
      commentsCount: 42,
      isLikedByCurrentUser: false,
      imageUrls: ['assets/images/content/success1.jpg'],
      icon: Icons.military_tech,
      color: Colors.green,
      isVerified: true,
    ),
    SuccessStoryModel(
      id: '2',
      createdAt: DateTime.now().subtract(const Duration(days: 45)),
      userId: 'user2',
      userName: 'Jane Smith',
      userProfileImageUrl: null,
      title: 'From Civilian to Navy Personnel: My Success Story',
      content:
          'I want to share my experience transitioning from civilian life to becoming a member of the Nigerian Navy. The challenges I faced and how I overcame them...',
      agency: 'Navy',
      position: 'Ensign',
      successDate: DateTime.now().subtract(const Duration(days: 120)),
      tags: ['Navy', 'Career Change', 'Training'],
      likesCount: 98,
      commentsCount: 27,
      isLikedByCurrentUser: true,
      imageUrls: ['assets/images/content/success2.jpg'],
      icon: Icons.sailing,
      color: Colors.blue,
      isVerified: true,
    ),
    SuccessStoryModel(
      id: '3',
      createdAt: DateTime.now().subtract(const Duration(days: 60)),
      userId: 'user3',
      userName: 'Michael Johnson',
      userProfileImageUrl: null,
      title: 'How I Passed the DSSC Selection Process',
      content:
          'The Direct Short Service Commission selection process is rigorous and competitive. Here\'s how I prepared and succeeded...',
      agency: 'DSSC',
      position: 'Officer Cadet',
      successDate: DateTime.now().subtract(const Duration(days: 150)),
      tags: ['DSSC', 'Selection', 'Preparation'],
      likesCount: 124,
      commentsCount: 35,
      isLikedByCurrentUser: false,
      imageUrls: ['assets/images/content/success3.jpg'],
      icon: Icons.school,
      color: Colors.purple,
      isVerified: false,
    ),
  ];

  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text(
          'Success Stories',
          style: TextStyle(fontWeight: FontWeight.bold),
        ),
        actions: [
          IconButton(
            icon: const Icon(Icons.search),
            onPressed: () {
              _showSearchDialog();
            },
          ),
          IconButton(
            icon: const Icon(Icons.filter_list),
            onPressed: () {
              _showFilterDialog();
            },
          ),
        ],
      ),
      body: _successStories.isEmpty
          ? _buildEmptyState()
          : ListView.builder(
              padding: const EdgeInsets.all(16),
              itemCount: _successStories.length,
              itemBuilder: (context, index) {
                return _buildSuccessStoryCard(_successStories[index]);
              },
            ),
      floatingActionButton: FloatingActionButton(
        backgroundColor: AppTheme.primaryColor,
        child: const Icon(Icons.add),
        onPressed: () {
          _showCreateStoryDialog();
        },
      ),
    );
  }

  Widget _buildEmptyState() {
    return Center(
      child: Padding(
        padding: const EdgeInsets.all(24.0),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.emoji_events,
              size: 80,
              color: Colors.grey.withValues(alpha: 0.3 * 255),
            ),
            const SizedBox(height: 16),
            const Text(
              'No Success Stories Yet',
              style: TextStyle(
                fontSize: 20,
                fontWeight: FontWeight.bold,
                color: Colors.black87,
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 8),
            Text(
              'Be the first to share your success story and inspire others!',
              style: TextStyle(
                fontSize: 16,
                color: Colors.grey.shade600,
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 24),
            BaseButton(
              text: 'Share Your Success',
              icon: Icons.add,
              backgroundColor: AppTheme.primaryColor,
              onPressed: () {
                _showCreateStoryDialog();
              },
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSuccessStoryCard(SuccessStoryModel story) {
    return Card(
      margin: const EdgeInsets.only(bottom: 16),
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(16),
      ),
      child: InkWell(
        onTap: () => _navigateToStoryDetail(story),
        borderRadius: BorderRadius.circular(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Story header
            Padding(
              padding: const EdgeInsets.all(16),
              child: Row(
                children: [
                  // User avatar
                  CircleAvatar(
                    radius: 24,
                    backgroundColor: story.color.withValues(alpha: 0.2 * 255),
                    backgroundImage: story.userProfileImageUrl != null
                        ? NetworkImage(story.userProfileImageUrl!)
                        : null,
                    child: story.userProfileImageUrl == null
                        ? Text(
                            story.userName.substring(0, 1).toUpperCase(),
                            style: TextStyle(
                              fontWeight: FontWeight.bold,
                              color: story.color,
                              fontSize: 18,
                            ),
                          )
                        : null,
                  ),
                  const SizedBox(width: 12),
                  // User info
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Row(
                          children: [
                            Text(
                              story.userName,
                              style: const TextStyle(
                                fontWeight: FontWeight.bold,
                                fontSize: 16,
                                color: Colors.black87,
                              ),
                            ),
                            if (story.isVerified)
                              Padding(
                                padding: const EdgeInsets.only(left: 4),
                                child: Icon(
                                  Icons.verified,
                                  size: 16,
                                  color: story.color,
                                ),
                              ),
                          ],
                        ),
                        const SizedBox(height: 2),
                        Text(
                          '${story.position} at ${story.agency}',
                          style: TextStyle(
                            fontSize: 14,
                            color: Colors.grey.shade600,
                          ),
                        ),
                      ],
                    ),
                  ),
                  // Date
                  Text(
                    DateFormat('MMM d, yyyy').format(story.createdAt),
                    style: TextStyle(
                      fontSize: 12,
                      color: Colors.grey.shade600,
                    ),
                  ),
                ],
              ),
            ),
            // Story image if available
            if (story.imageUrls != null && story.imageUrls!.isNotEmpty)
              Container(
                height: 200,
                width: double.infinity,
                decoration: BoxDecoration(
                  image: DecorationImage(
                    image: AssetImage(story.imageUrls!.first),
                    fit: BoxFit.cover,
                  ),
                ),
              ),
            // Story title and content
            Padding(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    story.title,
                    style: const TextStyle(
                      fontWeight: FontWeight.bold,
                      fontSize: 18,
                      color: Colors.black87,
                    ),
                  ),
                  const SizedBox(height: 8),
                  Text(
                    story.content,
                    style: const TextStyle(
                      fontSize: 14,
                      color: Colors.black87,
                    ),
                    maxLines: 3,
                    overflow: TextOverflow.ellipsis,
                  ),
                  const SizedBox(height: 12),
                  // Tags
                  Wrap(
                    spacing: 8,
                    runSpacing: 8,
                    children: story.tags.map((tag) {
                      return Container(
                        padding: const EdgeInsets.symmetric(
                          horizontal: 8,
                          vertical: 4,
                        ),
                        decoration: BoxDecoration(
                          color: story.color.withValues(alpha: 0.1 * 255),
                          borderRadius: BorderRadius.circular(12),
                        ),
                        child: Text(
                          '#$tag',
                          style: TextStyle(
                            fontSize: 12,
                            color: story.color,
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                      );
                    }).toList(),
                  ),
                  const SizedBox(height: 12),
                  // Story actions
                  Row(
                    children: [
                      // Like button
                      InkWell(
                        onTap: () {
                          _toggleLike(story);
                        },
                        borderRadius: BorderRadius.circular(8),
                        child: Padding(
                          padding: const EdgeInsets.all(8.0),
                          child: Row(
                            children: [
                              Icon(
                                story.isLikedByCurrentUser
                                    ? Icons.favorite
                                    : Icons.favorite_border,
                                size: 20,
                                color: story.isLikedByCurrentUser
                                    ? Colors.red
                                    : Colors.grey.shade600,
                              ),
                              const SizedBox(width: 4),
                              Text(
                                story.likesCount.toString(),
                                style: TextStyle(
                                  fontSize: 14,
                                  color: Colors.grey.shade600,
                                ),
                              ),
                            ],
                          ),
                        ),
                      ),
                      const SizedBox(width: 16),
                      // Comment button
                      InkWell(
                        onTap: () {
                          _navigateToStoryDetail(story);
                        },
                        borderRadius: BorderRadius.circular(8),
                        child: Padding(
                          padding: const EdgeInsets.all(8.0),
                          child: Row(
                            children: [
                              Icon(
                                Icons.comment_outlined,
                                size: 20,
                                color: Colors.grey.shade600,
                              ),
                              const SizedBox(width: 4),
                              Text(
                                story.commentsCount.toString(),
                                style: TextStyle(
                                  fontSize: 14,
                                  color: Colors.grey.shade600,
                                ),
                              ),
                            ],
                          ),
                        ),
                      ),
                      const Spacer(),
                      // Share button
                      IconButton(
                        icon: Icon(
                          Icons.share,
                          size: 20,
                          color: Colors.grey.shade600,
                        ),
                        onPressed: () {
                          _shareStory(story);
                        },
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  void _showSearchDialog() {
    showDialog(
      context: context,
      builder: (context) {
        return AlertDialog(
          title: const Text('Search Success Stories'),
          content: TextField(
            controller: _searchController,
            decoration: const InputDecoration(
              hintText: 'Enter keywords...',
              prefixIcon: Icon(Icons.search),
            ),
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.pop(context),
              child: const Text('CANCEL'),
            ),
            TextButton(
              onPressed: () {
                // Perform search
                Navigator.pop(context);
                // Update UI with search results
              },
              child: const Text('SEARCH'),
            ),
          ],
        );
      },
    );
  }

  void _showFilterDialog() {
    showDialog(
      context: context,
      builder: (context) {
        return StatefulBuilder(
          builder: (context, setState) {
            return AlertDialog(
              title: const Text('Filter Success Stories'),
              content: Column(
                mainAxisSize: MainAxisSize.min,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  const Text(
                    'Agency',
                    style: TextStyle(fontWeight: FontWeight.bold, fontSize: 16),
                  ),
                  const SizedBox(height: 8),
                  Wrap(
                    spacing: 8,
                    runSpacing: 8,
                    children: _agencies.map((agency) {
                      return ChoiceChip(
                        label: Text(agency),
                        selected: _selectedAgency == agency,
                        onSelected: (selected) {
                          setState(() {
                            _selectedAgency = agency;
                          });
                        },
                      );
                    }).toList(),
                  ),
                ],
              ),
              actions: [
                TextButton(
                  onPressed: () => Navigator.pop(context),
                  child: const Text('CANCEL'),
                ),
                TextButton(
                  onPressed: () {
                    // Apply filters
                    Navigator.pop(context);
                    // Update UI with filtered stories
                    this.setState(() {});
                  },
                  child: const Text('APPLY'),
                ),
              ],
            );
          },
        );
      },
    );
  }

  void _showCreateStoryDialog() {
    final titleController = TextEditingController();
    final contentController = TextEditingController();
    final positionController = TextEditingController();
    String selectedAgency = widget.user.targetAgency;
    final tagsController = TextEditingController();
    DateTime successDate = DateTime.now();

    showDialog(
      context: context,
      builder: (context) {
        return StatefulBuilder(
          builder: (context, setState) {
            return AlertDialog(
              title: const Text('Share Your Success Story'),
              content: SingleChildScrollView(
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    TextField(
                      controller: titleController,
                      decoration: const InputDecoration(
                        labelText: 'Title',
                        hintText: 'Enter a title for your success story',
                      ),
                    ),
                    const SizedBox(height: 16),
                    TextField(
                      controller: contentController,
                      decoration: const InputDecoration(
                        labelText: 'Your Story',
                        hintText: 'Share your journey and how you succeeded',
                      ),
                      maxLines: 5,
                    ),
                    const SizedBox(height: 16),
                    DropdownButtonFormField<String>(
                      value: selectedAgency,
                      decoration: const InputDecoration(
                        labelText: 'Agency',
                      ),
                      items: _agencies.skip(1).map((agency) {
                        return DropdownMenuItem<String>(
                          value: agency,
                          child: Text(agency),
                        );
                      }).toList(),
                      onChanged: (value) {
                        if (value != null) {
                          setState(() {
                            selectedAgency = value;
                          });
                        }
                      },
                    ),
                    const SizedBox(height: 16),
                    TextField(
                      controller: positionController,
                      decoration: const InputDecoration(
                        labelText: 'Position/Rank',
                        hintText: 'e.g. Lieutenant, Officer Cadet, etc.',
                      ),
                    ),
                    const SizedBox(height: 16),
                    TextField(
                      controller: tagsController,
                      decoration: const InputDecoration(
                        labelText: 'Tags (comma separated)',
                        hintText: 'e.g. Training, Perseverance, Career',
                      ),
                    ),
                    const SizedBox(height: 16),
                    Row(
                      children: [
                        const Text('Success Date:'),
                        const Spacer(),
                        TextButton(
                          onPressed: () async {
                            final date = await showDatePicker(
                              context: context,
                              initialDate: successDate,
                              firstDate: DateTime(2000),
                              lastDate: DateTime.now(),
                            );
                            if (date != null) {
                              setState(() {
                                successDate = date;
                              });
                            }
                          },
                          child: Text(
                            DateFormat('MMM d, yyyy').format(successDate),
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              ),
              actions: [
                TextButton(
                  onPressed: () => Navigator.pop(context),
                  child: const Text('CANCEL'),
                ),
                TextButton(
                  onPressed: () {
                    // Create success story
                    if (titleController.text.isNotEmpty &&
                        contentController.text.isNotEmpty &&
                        positionController.text.isNotEmpty) {
                      // Parse tags
                      final tags = tagsController.text
                          .split(',')
                          .map((e) => e.trim())
                          .where((e) => e.isNotEmpty)
                          .toList();

                      // Add story to the list
                      setState(() {
                        _successStories.insert(
                          0,
                          SuccessStoryModel(
                            id: DateTime.now().millisecondsSinceEpoch.toString(),
                            createdAt: DateTime.now(),
                            userId: widget.user.id,
                            userName: widget.user.fullName,
                            userProfileImageUrl: widget.user.profileImageUrl,
                            title: titleController.text,
                            content: contentController.text,
                            agency: selectedAgency,
                            position: positionController.text,
                            successDate: successDate,
                            tags: tags.isEmpty ? ['Success'] : tags,
                            likesCount: 0,
                            commentsCount: 0,
                            isLikedByCurrentUser: false,
                            icon: _getAgencyIcon(selectedAgency),
                            color: _getAgencyColor(selectedAgency),
                            isVerified: false,
                          ),
                        );
                      });
                      Navigator.pop(context);
                      ScaffoldMessenger.of(context).showSnackBar(
                        const SnackBar(
                          content: Text('Success story shared! It will be reviewed by our team.'),
                        ),
                      );
                    }
                  },
                  child: const Text('SHARE'),
                ),
              ],
            );
          },
        );
      },
    );
  }

  void _navigateToStoryDetail(SuccessStoryModel story) {
    // Navigate to story detail screen
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(content: Text('Viewing story: ${story.title}')),
    );
  }

  void _toggleLike(SuccessStoryModel story) {
    setState(() {
      final index = _successStories.indexWhere((s) => s.id == story.id);
      if (index != -1) {
        final updatedStory = story.copyWith(
          isLikedByCurrentUser: !story.isLikedByCurrentUser,
          likesCount: story.isLikedByCurrentUser
              ? story.likesCount - 1
              : story.likesCount + 1,
        );
        _successStories[index] = updatedStory;
      }
    });
  }

  void _shareStory(SuccessStoryModel story) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(content: Text('Sharing story: ${story.title}')),
    );
  }

  Color _getAgencyColor(String agency) {
    switch (agency) {
      case 'Nigerian Army':
        return Colors.green;
      case 'Navy':
        return Colors.blue;
      case 'Air Force':
        return Colors.indigo;
      case 'DSSC':
        return Colors.purple;
      case 'NDA':
        return Colors.red;
      case 'NSCDC':
        return Colors.orange;
      case 'EFCC':
        return Colors.teal;
      default:
        return Colors.grey;
    }
  }

  IconData _getAgencyIcon(String agency) {
    switch (agency) {
      case 'Nigerian Army':
        return Icons.military_tech;
      case 'Navy':
        return Icons.sailing;
      case 'Air Force':
        return Icons.airplanemode_active;
      case 'DSSC':
        return Icons.school;
      case 'NDA':
        return Icons.shield;
      case 'NSCDC':
        return Icons.security;
      case 'EFCC':
        return Icons.gavel;
      default:
        return Icons.emoji_events;
    }
  }
}
