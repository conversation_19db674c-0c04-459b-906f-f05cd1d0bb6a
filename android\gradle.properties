org.gradle.jvmargs=-Xmx4G -XX:MaxMetaspaceSize=2G -XX:ReservedCodeCacheSize=512m -XX:+HeapDumpOnOutOfMemoryError
android.useAndroidX=true
android.enableJetifier=true
org.gradle.daemon=true
org.gradle.parallel=true
org.gradle.caching=true
org.gradle.offline=false

# Override plugin SDK requirements
android.overridePathCheck=true

# Fix for dexing transforms issue (only keep non-deprecated options)
android.useFullClasspathForDexingTransform=true
android.defaults.buildfeatures.buildconfig=true
android.nonTransitiveRClass=false
android.nonFinalResIds=false

# Kotlin code style for this project: "official" or "obsolete":
kotlin.code.style=official
