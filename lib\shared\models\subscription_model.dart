import 'package:fit_4_force/shared/models/base_model.dart';

class SubscriptionModel extends BaseModel {
  final String userId;
  final String transactionReference;
  final DateTime startDate;
  final DateTime expiryDate;
  final double amount;
  final bool isActive;
  final String paymentMethod;
  final bool autoRenew;

  const SubscriptionModel({
    required super.id,
    required super.createdAt,
    super.updatedAt,
    required this.userId,
    required this.transactionReference,
    required this.startDate,
    required this.expiryDate,
    required this.amount,
    required this.isActive,
    required this.paymentMethod,
    required this.autoRenew,
  });

  @override
  List<Object?> get props => [
        ...super.props,
        userId,
        transactionReference,
        startDate,
        expiryDate,
        amount,
        isActive,
        paymentMethod,
        autoRenew,
      ];

  @override
  Map<String, dynamic> toJson() {
    return {
      ...super.toJson(),
      'userId': userId,
      'transactionReference': transactionReference,
      'startDate': startDate.toIso8601String(),
      'expiryDate': expiryDate.toIso8601String(),
      'amount': amount,
      'isActive': isActive,
      'paymentMethod': paymentMethod,
      'autoRenew': autoRenew,
    };
  }

  factory SubscriptionModel.fromJson(Map<String, dynamic> json) {
    return SubscriptionModel(
      id: json['id'] as String,
      createdAt: DateTime.parse(json['createdAt'] as String),
      updatedAt: json['updatedAt'] != null
          ? DateTime.parse(json['updatedAt'] as String)
          : null,
      userId: json['userId'] as String,
      transactionReference: json['transactionReference'] as String,
      startDate: DateTime.parse(json['startDate'] as String),
      expiryDate: DateTime.parse(json['expiryDate'] as String),
      amount: (json['amount'] as num).toDouble(),
      isActive: json['isActive'] as bool,
      paymentMethod: json['paymentMethod'] as String,
      autoRenew: json['autoRenew'] as bool,
    );
  }

  @override
  BaseModel copyWith({
    String? id,
    DateTime? createdAt,
    DateTime? updatedAt,
    String? userId,
    String? transactionReference,
    DateTime? startDate,
    DateTime? expiryDate,
    double? amount,
    bool? isActive,
    String? paymentMethod,
    bool? autoRenew,
  }) {
    return SubscriptionModel(
      id: id ?? this.id,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      userId: userId ?? this.userId,
      transactionReference: transactionReference ?? this.transactionReference,
      startDate: startDate ?? this.startDate,
      expiryDate: expiryDate ?? this.expiryDate,
      amount: amount ?? this.amount,
      isActive: isActive ?? this.isActive,
      paymentMethod: paymentMethod ?? this.paymentMethod,
      autoRenew: autoRenew ?? this.autoRenew,
    );
  }
}
