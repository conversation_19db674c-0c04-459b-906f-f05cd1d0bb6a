# GitHub Pages Configuration for Fit4Force Legal Documents

# Site settings
title: "Fit4Force Legal Documents"
description: "Legal documents and policies for the Fit4Force mobile application"
baseurl: ""
url: "https://[YOUR-GITHUB-USERNAME].github.io"

# Build settings
markdown: kramdown
highlighter: rouge
theme: minima

# Exclude files from processing
exclude:
  - README.md
  - Gemfile
  - Gemfile.lock
  - node_modules
  - vendor

# Include files
include:
  - _pages

# Collections
collections:
  pages:
    output: true
    permalink: /:name/

# Defaults
defaults:
  - scope:
      path: ""
      type: "pages"
    values:
      layout: "default"

# SEO settings
plugins:
  - jekyll-sitemap
  - jekyll-feed

# Custom variables
legal:
  company_name: "Fit4Force"
  contact_email: "[EMAIL]"
  last_updated: "2024"
