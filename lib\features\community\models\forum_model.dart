import 'package:flutter/material.dart';
import 'package:fit_4_force/shared/models/base_model.dart';

/// Model representing an agency-specific forum
class ForumModel extends BaseModel {
  final String name;
  final String description;
  final String agency;
  final int postsCount;
  final int membersCount;
  final List<String> topics;
  final IconData icon;
  final Color color;
  final bool isPinned;
  final bool isOfficial;

  const ForumModel({
    required super.id,
    required super.createdAt,
    super.updatedAt,
    required this.name,
    required this.description,
    required this.agency,
    required this.postsCount,
    required this.membersCount,
    required this.topics,
    required this.icon,
    required this.color,
    required this.isPinned,
    required this.isOfficial,
  });

  @override
  List<Object?> get props => [
        ...super.props,
        name,
        description,
        agency,
        postsCount,
        membersCount,
        topics,
        icon,
        color,
        isPinned,
        isOfficial,
      ];

  @override
  ForumModel copyWith({
    String? id,
    DateTime? createdAt,
    DateTime? updatedAt,
    String? name,
    String? description,
    String? agency,
    int? postsCount,
    int? membersCount,
    List<String>? topics,
    IconData? icon,
    Color? color,
    bool? isPinned,
    bool? isOfficial,
  }) {
    return ForumModel(
      id: id ?? this.id,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      name: name ?? this.name,
      description: description ?? this.description,
      agency: agency ?? this.agency,
      postsCount: postsCount ?? this.postsCount,
      membersCount: membersCount ?? this.membersCount,
      topics: topics ?? this.topics,
      icon: icon ?? this.icon,
      color: color ?? this.color,
      isPinned: isPinned ?? this.isPinned,
      isOfficial: isOfficial ?? this.isOfficial,
    );
  }
}
