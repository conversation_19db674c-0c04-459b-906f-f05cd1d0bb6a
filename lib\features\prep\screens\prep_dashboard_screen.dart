import 'package:flutter/material.dart';
import 'package:fit_4_force/core/theme/app_theme.dart';
import 'package:fit_4_force/core/config/app_routes.dart';
import 'package:fit_4_force/core/utils/navigation_service.dart';
import 'package:fit_4_force/core/utils/responsive_utils.dart';
import 'package:fit_4_force/core/widgets/responsive_widgets.dart';
import 'package:fit_4_force/shared/widgets/responsive_grid_view.dart' as shared;
import 'package:fit_4_force/features/prep/models/study_material_model.dart';
import 'package:fit_4_force/features/prep/models/quiz_model.dart';
import 'package:fit_4_force/features/prep/services/study_material_service.dart';
import 'package:fit_4_force/features/prep/services/enhanced_study_material_service.dart';
import 'package:fit_4_force/features/prep/services/quiz_service.dart';
import 'package:fit_4_force/features/prep/screens/agency_study_materials_screen.dart';
import 'package:fit_4_force/features/prep/utils/dummy_user.dart';
import 'package:fit_4_force/shared/models/user_model.dart';
import 'package:fit_4_force/shared/widgets/premium_badge.dart';

class PrepDashboardScreen extends StatefulWidget {
  final UserModel user;

  const PrepDashboardScreen({super.key, required this.user});

  @override
  State<PrepDashboardScreen> createState() => _PrepDashboardScreenState();
}

class _PrepDashboardScreenState extends State<PrepDashboardScreen> {
  final StudyMaterialService _studyMaterialService = StudyMaterialService();
  final EnhancedStudyMaterialService _enhancedService =
      EnhancedStudyMaterialService();
  final QuizService _quizService = QuizService();

  List<StudyMaterialCategory> _categories = [];
  List<StudyMaterialModel> _featuredMaterials = [];
  List<QuizModel> _featuredQuizzes = [];
  Map<String, dynamic>? _userAgency;
  bool _isLoading = true;

  @override
  void initState() {
    super.initState();
    _loadData();
  }

  Future<void> _loadData() async {
    setState(() => _isLoading = true);

    try {
      // Load agency-specific categories and materials
      final categories = await _enhancedService.getAgencyCategories();
      final featuredMaterials = await _enhancedService.getFeaturedMaterials();
      final userAgency = await _enhancedService.getUserAgency();

      // Load quizzes (keeping existing functionality)
      final featuredQuizzes = _quizService.getFeaturedQuizzes();

      setState(() {
        _categories = categories;
        _featuredMaterials = featuredMaterials;
        _featuredQuizzes = featuredQuizzes;
        _userAgency = userAgency;
        _isLoading = false;
      });

      // Log successful load for debugging
      print('✅ Prep dashboard loaded successfully:');
      print('   Categories: ${_categories.length}');
      print('   Featured Materials: ${_featuredMaterials.length}');
      print('   Featured Quizzes: ${_featuredQuizzes.length}');
      print('   User Agency: ${_userAgency?['name'] ?? 'Unknown'}');
    } catch (e) {
      print('❌ Error loading prep dashboard data: $e');

      // Fallback to static data if API fails
      setState(() {
        _categories = _studyMaterialService.getAllCategories();
        _featuredMaterials = _studyMaterialService.getFeaturedMaterials();
        _featuredQuizzes = _quizService.getFeaturedQuizzes();
        _isLoading = false;
      });

      // Show error message to user
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Using offline content. Check your connection.'),
            backgroundColor: Colors.orange,
            duration: Duration(seconds: 3),
          ),
        );
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    final layoutType = ResponsiveUtils.getLayoutType(context);
    final padding = ResponsiveUtils.getResponsivePadding(context);
    final spacing = ResponsiveUtils.getResponsiveSpacing(context);

    return Scaffold(
      body: SafeArea(
        child: ResponsiveMultiPaneLayout(
          primaryPane: _buildMainContent(context, padding, spacing),
          secondaryPane:
              layoutType == LayoutType.desktop ||
                      layoutType == LayoutType.tabletLandscape
                  ? _buildSidePanel(context)
                  : null,
          primaryFlex: 2,
          secondaryFlex: 1,
        ),
      ),
    );
  }

  Widget _buildMainContent(
    BuildContext context,
    EdgeInsets padding,
    double spacing,
  ) {
    return SingleChildScrollView(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildHeader(),
          Padding(
            padding: padding,
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                _buildSearchBar(),
                SizedBox(height: spacing),
                _buildCategoryGrid(),
                SizedBox(height: spacing),
                _buildFeaturedMaterials(),
                SizedBox(height: spacing),
                _buildFeaturedQuizzes(),
                SizedBox(height: spacing / 2),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSidePanel(BuildContext context) {
    return Container(
      padding: ResponsiveUtils.getResponsivePadding(context),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          ResponsiveText(
            'Quick Access',
            mobileFontSize: 18.0,
            tabletFontSize: 20.0,
            desktopFontSize: 22.0,
            fontWeight: FontWeight.bold,
          ),
          SizedBox(height: ResponsiveUtils.getResponsiveSpacing(context)),
          _buildQuickAccessCard(
            'Study Timer',
            Icons.timer,
            AppTheme.primaryColor,
            () => NavigationService().navigateTo(
              AppRoutes.pomodoroTimer,
              arguments: widget.user,
            ),
          ),
          SizedBox(height: ResponsiveUtils.getResponsiveSpacing(context) / 2),
          _buildQuickAccessCard(
            'Mock Exams',
            Icons.quiz,
            Colors.orange,
            () => NavigationService().navigateTo(
              AppRoutes.mockExams,
              arguments: widget.user,
            ),
          ),
          SizedBox(height: ResponsiveUtils.getResponsiveSpacing(context) / 2),
          _buildQuickAccessCard(
            'Progress',
            Icons.analytics,
            Colors.green,
            () => NavigationService().navigateTo(
              AppRoutes.prepProgressDashboard,
              arguments: widget.user,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildQuickAccessCard(
    String title,
    IconData icon,
    Color color,
    VoidCallback onTap,
  ) {
    return ResponsiveCard(
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(
          ResponsiveUtils.getResponsiveBorderRadius(context),
        ),
        child: Padding(
          padding: const EdgeInsets.all(12.0),
          child: Row(
            children: [
              Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: color.withValues(alpha: 0.1 * 255),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Icon(icon, color: color, size: 20),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: ResponsiveText(
                  title,
                  mobileFontSize: 14.0,
                  tabletFontSize: 15.0,
                  desktopFontSize: 16.0,
                  fontWeight: FontWeight.w600,
                ),
              ),
              Icon(Icons.arrow_forward_ios, size: 14, color: Colors.grey[600]),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildHeader() {
    return Padding(
      padding: const EdgeInsets.all(16.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'Exam Preparation',
                    style: Theme.of(context).textTheme.headlineMedium?.copyWith(
                      fontSize: 28,
                      fontWeight: FontWeight.bold,
                      color: AppTheme.textPrimaryDark,
                      letterSpacing: 0.5,
                      shadows: const [
                        Shadow(
                          color: Colors.black12,
                          blurRadius: 2,
                          offset: Offset(0, 1),
                        ),
                      ],
                    ),
                  ),
                  const SizedBox(height: 8),
                  Text(
                    'Study materials for ${widget.user.targetAgency}',
                    style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                      fontSize: 16,
                      fontWeight: FontWeight.w500,
                      color: AppTheme.textPrimaryDark,
                      letterSpacing: 0.3,
                    ),
                  ),
                ],
              ),
              Container(
                padding: const EdgeInsets.all(10),
                decoration: BoxDecoration(
                  color: AppTheme.primaryColor.withOpacity(0.1),
                  shape: BoxShape.circle,
                  boxShadow: [
                    BoxShadow(
                      color: AppTheme.primaryColor.withOpacity(0.2),
                      blurRadius: 8,
                      offset: const Offset(0, 3),
                      spreadRadius: 0,
                    ),
                  ],
                ),
                child: Icon(
                  Icons.menu_book,
                  color: AppTheme.primaryColor,
                  size: 24,
                ),
              ),
            ],
          ),
          const SizedBox(height: 20),
          // Study Timer Button
          GestureDetector(
            onTap: () {
              NavigationService().navigateTo(
                AppRoutes.pomodoroTimer,
                arguments: DummyUser.getDummyUser(),
              );
            },
            child: Container(
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  colors: [
                    AppTheme.primaryColor.withValues(alpha: 0.15 * 255),
                    AppTheme.primaryColor.withValues(alpha: 0.05 * 255),
                  ],
                  begin: Alignment.topLeft,
                  end: Alignment.bottomRight,
                ),
                borderRadius: BorderRadius.circular(16),
                boxShadow: [
                  BoxShadow(
                    color: Colors.grey.withValues(alpha: 0.15 * 255),
                    blurRadius: 10,
                    offset: const Offset(0, 5),
                    spreadRadius: 0,
                  ),
                ],
                border: Border.all(
                  color: AppTheme.primaryColor.withValues(alpha: 0.1 * 255),
                  width: 1,
                ),
              ),
              child: Row(
                children: [
                  Container(
                    padding: const EdgeInsets.all(12),
                    decoration: BoxDecoration(
                      color: AppTheme.primaryColor.withValues(alpha: 0.2 * 255),
                      shape: BoxShape.circle,
                      boxShadow: [
                        BoxShadow(
                          color: AppTheme.primaryColor.withValues(
                            alpha: 0.3 * 255,
                          ),
                          blurRadius: 8,
                          offset: const Offset(0, 3),
                          spreadRadius: 0,
                        ),
                      ],
                    ),
                    child: const Icon(
                      Icons.timer,
                      color: Colors.white,
                      size: 24,
                    ),
                  ),
                  const SizedBox(width: 16),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          'Pomodoro Study Timer',
                          style: Theme.of(
                            context,
                          ).textTheme.titleMedium?.copyWith(
                            fontWeight: FontWeight.bold,
                            color: Colors.white,
                            shadows: [
                              Shadow(
                                offset: Offset(0, 1),
                                blurRadius: 3.0,
                                color: Colors.black.withAlpha(100),
                              ),
                            ],
                          ),
                        ),
                        const SizedBox(height: 4),
                        Text(
                          'Focus better with timed study sessions',
                          style: Theme.of(
                            context,
                          ).textTheme.bodySmall?.copyWith(
                            color: Colors.white.withAlpha(220),
                            height: 1.3,
                            shadows: [
                              Shadow(
                                offset: Offset(0, 1),
                                blurRadius: 2.0,
                                color: Colors.black.withAlpha(80),
                              ),
                            ],
                          ),
                        ),
                      ],
                    ),
                  ),
                  Icon(
                    Icons.arrow_forward_ios,
                    color: AppTheme.primaryColor,
                    size: 16,
                  ),
                ],
              ),
            ),
          ),
          const SizedBox(height: 20),
          // Mock Exams Button
          GestureDetector(
            onTap: () {
              NavigationService().navigateTo(
                AppRoutes.mockExams,
                arguments: DummyUser.getDummyUser(),
              );
            },
            child: Container(
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  colors: [
                    Colors.orange.withValues(alpha: 0.15 * 255),
                    Colors.orange.withValues(alpha: 0.05 * 255),
                  ],
                  begin: Alignment.topLeft,
                  end: Alignment.bottomRight,
                ),
                borderRadius: BorderRadius.circular(16),
                boxShadow: [
                  BoxShadow(
                    color: Colors.grey.withValues(alpha: 0.15 * 255),
                    blurRadius: 10,
                    offset: const Offset(0, 5),
                    spreadRadius: 0,
                  ),
                ],
                border: Border.all(
                  color: Colors.orange.withValues(alpha: 0.1 * 255),
                  width: 1,
                ),
              ),
              child: Row(
                children: [
                  Container(
                    padding: const EdgeInsets.all(12),
                    decoration: BoxDecoration(
                      color: Colors.orange.withValues(alpha: 0.2 * 255),
                      shape: BoxShape.circle,
                      boxShadow: [
                        BoxShadow(
                          color: Colors.orange.withValues(alpha: 0.3 * 255),
                          blurRadius: 8,
                          offset: const Offset(0, 3),
                          spreadRadius: 0,
                        ),
                      ],
                    ),
                    child: const Icon(
                      Icons.quiz,
                      color: Colors.white,
                      size: 24,
                    ),
                  ),
                  const SizedBox(width: 16),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          'Mock Exams',
                          style: Theme.of(
                            context,
                          ).textTheme.titleMedium?.copyWith(
                            fontWeight: FontWeight.bold,
                            color: Colors.white,
                            shadows: [
                              Shadow(
                                offset: Offset(0, 1),
                                blurRadius: 3.0,
                                color: Colors.black.withAlpha(100),
                              ),
                            ],
                          ),
                        ),
                        const SizedBox(height: 4),
                        Text(
                          'Test your knowledge with realistic exam simulations',
                          style: Theme.of(
                            context,
                          ).textTheme.bodySmall?.copyWith(
                            color: Colors.white.withAlpha(220),
                            height: 1.3,
                            shadows: [
                              Shadow(
                                offset: Offset(0, 1),
                                blurRadius: 2.0,
                                color: Colors.black.withAlpha(80),
                              ),
                            ],
                          ),
                        ),
                      ],
                    ),
                  ),
                  const Icon(
                    Icons.arrow_forward_ios,
                    color: Colors.orange,
                    size: 16,
                  ),
                ],
              ),
            ),
          ),
          const SizedBox(height: 20),
          Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(16),
              boxShadow: [
                BoxShadow(
                  color: Colors.grey.withAlpha(40),
                  blurRadius: 8,
                  offset: const Offset(0, 3),
                  spreadRadius: 0,
                ),
              ],
              border: Border.all(color: Colors.grey.withAlpha(20), width: 1),
            ),
            child: Row(
              children: [
                Container(
                  padding: const EdgeInsets.all(10),
                  decoration: BoxDecoration(
                    color: Colors.white,
                    borderRadius: BorderRadius.circular(12),
                    boxShadow: [
                      BoxShadow(
                        color: Colors.grey.withAlpha(40),
                        blurRadius: 4,
                        offset: const Offset(0, 2),
                        spreadRadius: 0,
                      ),
                    ],
                    border: Border.all(
                      color: Colors.grey.withAlpha(20),
                      width: 1,
                    ),
                  ),
                  child: Icon(
                    Icons.lightbulb_outline,
                    color: AppTheme.primaryColor,
                    size: 20,
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'Study Tip',
                        style: Theme.of(
                          context,
                        ).textTheme.titleMedium?.copyWith(
                          fontWeight: FontWeight.bold,
                          color: AppTheme.primaryColor,
                          shadows: [
                            Shadow(
                              offset: Offset(0, 1),
                              blurRadius: 2.0,
                              color: Colors.black.withAlpha(50),
                            ),
                          ],
                        ),
                      ),
                      const SizedBox(height: 4),
                      Text(
                        'Regular practice with quizzes improves retention by 70%',
                        style: Theme.of(context).textTheme.bodySmall?.copyWith(
                          color: Colors.black87,
                          height: 1.3,
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSearchBar() {
    return Container(
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withOpacity(0.1),
            blurRadius: 10,
            offset: const Offset(0, 4),
            spreadRadius: 0,
          ),
        ],
        border: Border.all(color: Colors.grey.withOpacity(0.05), width: 1),
      ),
      child: TextField(
        decoration: InputDecoration(
          hintText: 'Search study materials and quizzes',
          prefixIcon: Icon(
            Icons.search,
            color: AppTheme.primaryColor.withOpacity(0.7),
          ),
          suffixIcon: Container(
            margin: const EdgeInsets.all(8),
            decoration: BoxDecoration(
              color: AppTheme.primaryColor.withOpacity(0.1),
              borderRadius: BorderRadius.circular(8),
            ),
            child: Icon(Icons.tune, color: AppTheme.primaryColor, size: 20),
          ),
          border: InputBorder.none,
          contentPadding: const EdgeInsets.symmetric(vertical: 16),
          hintStyle: TextStyle(
            color: AppTheme.textSecondaryLight,
            fontWeight: FontWeight.w400,
          ),
        ),
        onSubmitted: (value) {
          // Handle search
        },
      ),
    );
  }

  Widget _buildCategoryGrid() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text(
              'Categories',
              style: Theme.of(context).textTheme.titleLarge?.copyWith(
                fontWeight: FontWeight.bold,
                color: AppTheme.textPrimaryDark,
              ),
            ),
            Row(
              children: [
                // Progress Dashboard button
                ElevatedButton.icon(
                  onPressed: () {
                    NavigationService().navigateTo(
                      AppRoutes.prepProgressDashboard,
                      arguments: DummyUser.getDummyUser(),
                    );
                  },
                  icon: const Icon(Icons.bar_chart),
                  label: const Text('Progress'),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.green,
                    foregroundColor: Colors.white,
                    elevation: 2,
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(12),
                    ),
                  ),
                ),
                const SizedBox(width: 8),
                // Flashcards button
                ElevatedButton.icon(
                  onPressed: () {
                    NavigationService().navigateTo(
                      AppRoutes.flashcards,
                      arguments: DummyUser.getDummyUser(),
                    );
                  },
                  icon: const Icon(Icons.style),
                  label: const Text('Flashcards'),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: AppTheme.primaryColor,
                    foregroundColor: Colors.white,
                    elevation: 2,
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(12),
                    ),
                  ),
                ),
              ],
            ),
          ],
        ),
        const SizedBox(height: 16),
        shared.ResponsiveGridView(
          mobileColumns: 2,
          tabletColumns: 3,
          desktopColumns: 4,
          childAspectRatio: ResponsiveUtils.isMobile(context) ? 1.1 : 1.5,
          crossAxisSpacing: ResponsiveUtils.getResponsiveSpacing(context),
          mainAxisSpacing: ResponsiveUtils.getResponsiveSpacing(context),
          shrinkWrap: true,
          physics: const NeverScrollableScrollPhysics(),
          children:
              _categories
                  .map((category) => _buildCategoryCard(category))
                  .toList(),
        ),
      ],
    );
  }

  Widget _buildCategoryCard(StudyMaterialCategory category) {
    final isMobile = ResponsiveUtils.isMobile(context);
    final screenWidth = MediaQuery.of(context).size.width;
    final isSmallScreen = screenWidth < 400;

    return InkWell(
      onTap: () {
        // Navigate to agency-specific study materials screen
        Navigator.push(
          context,
          MaterialPageRoute(
            builder:
                (context) => AgencyStudyMaterialsScreen(
                  sectionName: category.name,
                  sectionId: category.id,
                ),
          ),
        );
      },
      borderRadius: BorderRadius.circular(isMobile ? 12 : 16),
      child: Container(
        padding: EdgeInsets.all(isMobile ? 12 : 16),
        decoration: BoxDecoration(
          gradient: LinearGradient(
            colors: [
              Colors.white,
              Color.lerp(Colors.white, category.color, 0.1) ?? Colors.white,
            ],
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
          ),
          borderRadius: BorderRadius.circular(isMobile ? 12 : 16),
          boxShadow: [
            // Main shadow
            BoxShadow(
              color: Colors.grey.withValues(alpha: 0.15 * 255),
              blurRadius: isMobile ? 6 : 10,
              offset: Offset(0, isMobile ? 3 : 5),
              spreadRadius: 0,
            ),
            // Highlight shadow for 3D effect
            BoxShadow(
              color: Colors.white,
              blurRadius: 2,
              offset: const Offset(-1, -1),
              spreadRadius: 0,
            ),
          ],
          border: Border.all(
            color: category.color.withValues(alpha: 0.1 * 255),
            width: 1,
          ),
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            // Enhanced icon container for mobile
            Container(
              padding: EdgeInsets.all(isMobile ? 8 : 10),
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  colors: [
                    category.color.withValues(alpha: 0.2 * 255),
                    category.color.withValues(alpha: 0.1 * 255),
                  ],
                  begin: Alignment.topLeft,
                  end: Alignment.bottomRight,
                ),
                shape: BoxShape.circle,
                boxShadow: [
                  BoxShadow(
                    color: category.color.withValues(alpha: 0.2 * 255),
                    blurRadius: isMobile ? 4 : 8,
                    offset: Offset(0, isMobile ? 2 : 3),
                    spreadRadius: 0,
                  ),
                ],
              ),
              child: Icon(
                category.icon,
                color: category.color,
                size: isMobile ? (isSmallScreen ? 20 : 22) : 24,
              ),
            ),
            SizedBox(height: isMobile ? 8 : 12),
            // Enhanced text with better contrast
            Text(
              category.name,
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.bold,
                color: Colors.black87, // Better contrast
                letterSpacing: 0.2,
                fontSize: isMobile ? (isSmallScreen ? 13 : 14) : 16,
              ),
              maxLines: 2,
              overflow: TextOverflow.ellipsis,
            ),
            SizedBox(height: isMobile ? 2 : 4),
            Text(
              '${category.materialCount} materials',
              style: Theme.of(context).textTheme.bodySmall?.copyWith(
                color: Colors.black54, // Better contrast
                fontWeight: FontWeight.w500,
                fontSize: isMobile ? (isSmallScreen ? 11 : 12) : 13,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildFeaturedMaterials() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text(
              'Featured Materials',
              style: Theme.of(context).textTheme.titleLarge?.copyWith(
                fontWeight: FontWeight.bold,
                color: AppTheme.textPrimaryDark,
              ),
            ),
            TextButton(
              onPressed: () {
                // Navigate to all materials
              },
              child: Text(
                'See All',
                style: TextStyle(
                  color: AppTheme.primaryColor,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ),
          ],
        ),
        const SizedBox(height: 16),
        SizedBox(
          height: ResponsiveUtils.isMobile(context) ? 180 : 200,
          child: ListView.builder(
            scrollDirection: Axis.horizontal,
            padding: EdgeInsets.symmetric(
              horizontal: ResponsiveUtils.isMobile(context) ? 8.0 : 0.0,
            ),
            itemCount: _featuredMaterials.length,
            itemBuilder: (context, index) {
              final material = _featuredMaterials[index];
              return _buildMaterialCard(material);
            },
          ),
        ),
      ],
    );
  }

  Widget _buildMaterialCard(StudyMaterialModel material) {
    final isMobile = ResponsiveUtils.isMobile(context);
    final cardWidth = isMobile ? 240.0 : 280.0;

    return Container(
      width: cardWidth,
      margin: EdgeInsets.only(right: isMobile ? 12 : 16),
      child: InkWell(
        onTap: () {
          // Navigate to material detail
        },
        borderRadius: BorderRadius.circular(isMobile ? 12 : 16),
        child: Container(
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(isMobile ? 12 : 16),
            boxShadow: [
              BoxShadow(
                color: Colors.grey.withValues(alpha: 0.15 * 255),
                blurRadius: isMobile ? 6 : 10,
                offset: Offset(0, isMobile ? 3 : 5),
                spreadRadius: 0,
              ),
              // Highlight shadow for 3D effect
              BoxShadow(
                color: Colors.white,
                blurRadius: 2,
                offset: const Offset(-1, -1),
                spreadRadius: 0,
              ),
            ],
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Container(
                height: 110,
                decoration: BoxDecoration(
                  gradient: LinearGradient(
                    colors: [
                      material.color.withOpacity(0.2),
                      material.color.withOpacity(0.1),
                    ],
                    begin: Alignment.topLeft,
                    end: Alignment.bottomRight,
                  ),
                  borderRadius: const BorderRadius.only(
                    topLeft: Radius.circular(16),
                    topRight: Radius.circular(16),
                  ),
                ),
                child: Stack(
                  children: [
                    // Background pattern
                    Positioned(
                      right: -20,
                      bottom: -20,
                      child: Icon(
                        material.icon,
                        size: 100,
                        color: material.color.withOpacity(0.1),
                      ),
                    ),
                    // Main icon
                    Center(
                      child: Container(
                        padding: const EdgeInsets.all(12),
                        decoration: BoxDecoration(
                          color: Colors.white.withOpacity(0.8),
                          shape: BoxShape.circle,
                          boxShadow: [
                            BoxShadow(
                              color: material.color.withOpacity(0.3),
                              blurRadius: 8,
                              offset: const Offset(0, 3),
                              spreadRadius: 0,
                            ),
                          ],
                        ),
                        child: Icon(
                          material.icon,
                          size: 36,
                          color: material.color,
                        ),
                      ),
                    ),
                    // Premium badge
                    if (material.isPremium)
                      const Positioned(
                        top: 10,
                        right: 10,
                        child: PremiumBadge(),
                      ),
                  ],
                ),
              ),
              Padding(
                padding: const EdgeInsets.all(16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      material.title,
                      style: Theme.of(context).textTheme.titleMedium?.copyWith(
                        fontWeight: FontWeight.bold,
                        letterSpacing: 0.2,
                      ),
                      maxLines: 1,
                      overflow: TextOverflow.ellipsis,
                    ),
                    const SizedBox(height: 6),
                    Text(
                      material.description,
                      style: Theme.of(context).textTheme.bodySmall?.copyWith(
                        color: AppTheme.textSecondaryLight,
                        height: 1.3,
                      ),
                      maxLines: 2,
                      overflow: TextOverflow.ellipsis,
                    ),
                    const SizedBox(height: 10),
                    Row(
                      children: [
                        Icon(
                          Icons.access_time,
                          size: 14,
                          color: AppTheme.textSecondaryLight,
                        ),
                        const SizedBox(width: 4),
                        Text(
                          '${material.estimatedReadTime} min read',
                          style: TextStyle(
                            fontSize: 12,
                            color: AppTheme.textSecondaryLight,
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildFeaturedQuizzes() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text(
              'Featured Quizzes',
              style: Theme.of(context).textTheme.titleLarge?.copyWith(
                fontWeight: FontWeight.bold,
                color: AppTheme.textPrimaryDark,
              ),
            ),
            TextButton(
              onPressed: () {
                // Navigate to all quizzes
              },
              child: Text(
                'See All',
                style: TextStyle(
                  color: AppTheme.primaryColor,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ),
          ],
        ),
        const SizedBox(height: 16),
        ListView.builder(
          shrinkWrap: true,
          physics: const NeverScrollableScrollPhysics(),
          itemCount: _featuredQuizzes.length,
          itemBuilder: (context, index) {
            final quiz = _featuredQuizzes[index];
            return _buildQuizCard(quiz);
          },
        ),
      ],
    );
  }

  Widget _buildQuizCard(QuizModel quiz) {
    return Container(
      margin: const EdgeInsets.only(bottom: 16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withOpacity(0.15),
            blurRadius: 10,
            offset: const Offset(0, 5),
            spreadRadius: 0,
          ),
          // Highlight shadow for 3D effect
          BoxShadow(
            color: Colors.white,
            blurRadius: 3,
            offset: const Offset(-1, -1),
            spreadRadius: 0,
          ),
        ],
      ),
      child: InkWell(
        onTap: () {
          // Navigate to quiz detail
        },
        borderRadius: BorderRadius.circular(16),
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Row(
            children: [
              // 3D-like icon container
              Container(
                width: 70,
                height: 70,
                decoration: BoxDecoration(
                  gradient: LinearGradient(
                    colors: [
                      quiz.color.withOpacity(0.2),
                      quiz.color.withOpacity(0.1),
                    ],
                    begin: Alignment.topLeft,
                    end: Alignment.bottomRight,
                  ),
                  borderRadius: BorderRadius.circular(16),
                  boxShadow: [
                    BoxShadow(
                      color: quiz.color.withOpacity(0.2),
                      blurRadius: 8,
                      offset: const Offset(0, 3),
                      spreadRadius: 0,
                    ),
                  ],
                ),
                child: Stack(
                  children: [
                    // Background pattern
                    Positioned(
                      right: -10,
                      bottom: -10,
                      child: Icon(
                        quiz.icon,
                        size: 50,
                        color: quiz.color.withOpacity(0.2),
                      ),
                    ),
                    // Main icon
                    Center(child: Icon(quiz.icon, size: 32, color: quiz.color)),
                  ],
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      children: [
                        Expanded(
                          child: Text(
                            quiz.title,
                            style: Theme.of(
                              context,
                            ).textTheme.titleMedium?.copyWith(
                              fontSize: 16,
                              fontWeight: FontWeight.bold,
                              letterSpacing: 0.3,
                              color: Colors.black87,
                            ),
                            maxLines: 1,
                            overflow: TextOverflow.ellipsis,
                          ),
                        ),
                        if (quiz.isPremium) const PremiumBadge(),
                      ],
                    ),
                    const SizedBox(height: 6),
                    Text(
                      quiz.description,
                      style: Theme.of(context).textTheme.bodySmall?.copyWith(
                        fontSize: 13,
                        fontWeight: FontWeight.w500,
                        color: Colors.black54,
                        height: 1.3,
                        letterSpacing: 0.2,
                      ),
                      maxLines: 2,
                      overflow: TextOverflow.ellipsis,
                    ),
                    const SizedBox(height: 10),
                    Row(
                      children: [
                        _buildQuizInfoChip(
                          Icons.timer_outlined,
                          '${quiz.timeLimit} min',
                        ),
                        const SizedBox(width: 8),
                        _buildQuizInfoChip(
                          Icons.quiz_outlined,
                          '${quiz.questions.length} questions',
                        ),
                        const SizedBox(width: 8),
                        _buildDifficultyChip(quiz.difficulty),
                      ],
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildQuizInfoChip(IconData icon, String label) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: Colors.grey[100],
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withOpacity(0.1),
            blurRadius: 4,
            offset: const Offset(0, 2),
            spreadRadius: 0,
          ),
        ],
        border: Border.all(color: Colors.grey.withOpacity(0.1), width: 1),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(icon, size: 14, color: AppTheme.primaryColor.withOpacity(0.7)),
          const SizedBox(width: 4),
          Text(
            label,
            style: TextStyle(
              fontSize: 12,
              color: AppTheme.textPrimaryDark,
              fontWeight: FontWeight.w500,
              letterSpacing: 0.2,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildDifficultyChip(String difficulty) {
    Color color;
    String icon;

    switch (difficulty) {
      case 'easy':
        color = Colors.green;
        icon = '👶';
        break;
      case 'medium':
        color = Colors.orange;
        icon = '👨';
        break;
      case 'hard':
        color = Colors.red;
        icon = '🔥';
        break;
      default:
        color = Colors.blue;
        icon = '📚';
    }

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 10, vertical: 4),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [color.withOpacity(0.2), color.withOpacity(0.1)],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: color.withOpacity(0.1),
            blurRadius: 4,
            offset: const Offset(0, 2),
            spreadRadius: 0,
          ),
        ],
        border: Border.all(color: color.withOpacity(0.2), width: 1),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Text(icon, style: const TextStyle(fontSize: 12)),
          const SizedBox(width: 4),
          Text(
            difficulty.capitalize(),
            style: TextStyle(
              fontSize: 12,
              color: color,
              fontWeight: FontWeight.bold,
              letterSpacing: 0.3,
            ),
          ),
        ],
      ),
    );
  }
}

extension StringExtension on String {
  String capitalize() {
    return "${this[0].toUpperCase()}${substring(1)}";
  }
}
