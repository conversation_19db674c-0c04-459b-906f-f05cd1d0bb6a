// No imports needed

class UserStats {
  final String userId;
  final double fitnessScore;
  final int totalWorkoutsCompleted;
  final int streakDays;
  final DateTime lastWorkoutDate;
  final Map<String, double>
  progressMetrics; // e.g., 'pushUps': 30, 'sitUps': 40
  final List<WorkoutLog> recentWorkouts;

  UserStats({
    required this.userId,
    required this.fitnessScore,
    required this.totalWorkoutsCompleted,
    required this.streakDays,
    required this.lastWorkoutDate,
    required this.progressMetrics,
    required this.recentWorkouts,
  });

  // Get fitness level based on score
  String getFitnessLevel() {
    if (fitnessScore > 80) return 'Advanced';
    if (fitnessScore > 60) return 'Intermediate';
    return 'Beginner';
  }

  // Convert to JSON for storage
  Map<String, dynamic> toJson() => {
    'userId': userId,
    'fitnessScore': fitnessScore,
    'totalWorkoutsCompleted': totalWorkoutsCompleted,
    'streakDays': streakDays,
    'lastWorkoutDate': lastWorkoutDate.toIso8601String(),
    'progressMetrics': progressMetrics,
    'recentWorkouts': recentWorkouts.map((w) => w.toJson()).toList(),
  };

  // This method is no longer needed with Supabase
  // The toJson method above is used instead

  // Create from JSON
  factory UserStats.fromJson(Map<String, dynamic> json) => UserStats(
    userId: json['userId'],
    fitnessScore: json['fitnessScore'],
    totalWorkoutsCompleted: json['totalWorkoutsCompleted'],
    streakDays: json['streakDays'],
    lastWorkoutDate: DateTime.parse(json['lastWorkoutDate']),
    progressMetrics: Map<String, double>.from(json['progressMetrics']),
    recentWorkouts:
        (json['recentWorkouts'] as List)
            .map((w) => WorkoutLog.fromJson(w))
            .toList(),
  );

  // Create from Supabase row
  factory UserStats.fromSupabase(Map<String, dynamic> data, String id) {
    return UserStats(
      userId: id,
      fitnessScore: data['fitnessScore'] ?? 0.0,
      totalWorkoutsCompleted: data['totalWorkoutsCompleted'] ?? 0,
      streakDays: data['streakDays'] ?? 0,
      lastWorkoutDate:
          data['lastWorkoutDate'] != null
              ? DateTime.parse(data['lastWorkoutDate'])
              : DateTime.now(),
      progressMetrics:
          data['progressMetrics'] != null
              ? Map<String, double>.from(data['progressMetrics'])
              : {},
      recentWorkouts:
          data['recentWorkouts'] != null
              ? (data['recentWorkouts'] as List)
                  .map((w) => WorkoutLog.fromJson(w))
                  .toList()
              : [],
    );
  }
}

class WorkoutLog {
  final String id;
  final String workoutId;
  final String workoutTitle;
  final DateTime completedDate;
  final int durationMinutes;
  final String focusArea;
  final double intensity; // 1-10 scale

  WorkoutLog({
    required this.id,
    required this.workoutId,
    required this.workoutTitle,
    required this.completedDate,
    required this.durationMinutes,
    required this.focusArea,
    required this.intensity,
  });

  // Convert to JSON for storage
  Map<String, dynamic> toJson() => {
    'id': id,
    'workoutId': workoutId,
    'workoutTitle': workoutTitle,
    'completedDate': completedDate.toIso8601String(),
    'durationMinutes': durationMinutes,
    'focusArea': focusArea,
    'intensity': intensity,
  };

  // Create from JSON
  factory WorkoutLog.fromJson(Map<String, dynamic> json) => WorkoutLog(
    id: json['id'],
    workoutId: json['workoutId'],
    workoutTitle: json['workoutTitle'],
    completedDate: DateTime.parse(json['completedDate']),
    durationMinutes: json['durationMinutes'],
    focusArea: json['focusArea'],
    intensity: json['intensity'],
  );
}

class Achievement {
  final String id;
  final String title;
  final String description;
  final String iconUrl;
  final DateTime earnedDate;
  final int points;
  final String category; // e.g., 'Strength', 'Endurance', 'Consistency'

  Achievement({
    required this.id,
    required this.title,
    required this.description,
    required this.iconUrl,
    required this.earnedDate,
    required this.points,
    required this.category,
  });

  // Convert to JSON for storage
  Map<String, dynamic> toJson() => {
    'id': id,
    'title': title,
    'description': description,
    'iconUrl': iconUrl,
    'earnedDate': earnedDate.toIso8601String(),
    'points': points,
    'category': category,
  };

  // Create from JSON
  factory Achievement.fromJson(Map<String, dynamic> json) => Achievement(
    id: json['id'],
    title: json['title'],
    description: json['description'],
    iconUrl: json['iconUrl'],
    earnedDate: DateTime.parse(json['earnedDate']),
    points: json['points'],
    category: json['category'],
  );

  // Create from Supabase row
  factory Achievement.fromSupabase(Map<String, dynamic> data, String id) {
    return Achievement(
      id: id,
      title: data['title'],
      description: data['description'],
      iconUrl: data['iconUrl'],
      earnedDate: DateTime.parse(data['earnedDate']),
      points: data['points'],
      category: data['category'],
    );
  }
}

class UpcomingTest {
  final String id;
  final String title;
  final String description;
  final DateTime testDate;
  final String agency;
  final List<String> requirements;
  final bool isPremium;

  UpcomingTest({
    required this.id,
    required this.title,
    required this.description,
    required this.testDate,
    required this.agency,
    required this.requirements,
    required this.isPremium,
  });

  // Convert to JSON for storage
  Map<String, dynamic> toJson() => {
    'id': id,
    'title': title,
    'description': description,
    'testDate': testDate.toIso8601String(),
    'agency': agency,
    'requirements': requirements,
    'isPremium': isPremium,
  };

  // Create from JSON
  factory UpcomingTest.fromJson(Map<String, dynamic> json) => UpcomingTest(
    id: json['id'],
    title: json['title'],
    description: json['description'],
    testDate: DateTime.parse(json['testDate']),
    agency: json['agency'],
    requirements: List<String>.from(json['requirements']),
    isPremium: json['isPremium'],
  );

  // Create from Supabase row
  factory UpcomingTest.fromSupabase(Map<String, dynamic> data, String id) {
    return UpcomingTest(
      id: id,
      title: data['title'],
      description: data['description'],
      testDate: DateTime.parse(data['testDate']),
      agency: data['agency'],
      requirements: List<String>.from(data['requirements']),
      isPremium: data['isPremium'],
    );
  }
}
