import 'package:flutter/material.dart';
import 'package:fit_4_force/core/theme/app_theme.dart';
import 'package:fit_4_force/core/utils/responsive_utils.dart';
import 'package:fit_4_force/core/widgets/responsive_widgets.dart';
import 'package:fit_4_force/features/community/models/post_model.dart';
import 'package:fit_4_force/features/community/screens/agency_forum_screen.dart';
import 'package:fit_4_force/features/community/screens/badges_screen.dart';
import 'package:fit_4_force/features/community/screens/study_groups_screen.dart';
import 'package:fit_4_force/features/community/screens/success_stories_screen.dart';
import 'package:fit_4_force/shared/models/user_model.dart';
import 'package:fit_4_force/shared/widgets/base_button.dart';
import 'package:fit_4_force/core/config/app_routes.dart';

class CommunityScreen extends StatefulWidget {
  final UserModel user;

  const CommunityScreen({super.key, required this.user});

  @override
  State<CommunityScreen> createState() => _CommunityScreenState();
}

class _CommunityScreenState extends State<CommunityScreen>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;
  final TextEditingController _searchController = TextEditingController();
  String _selectedAgency = 'All';
  final List<String> _agencies = [
    'All',
    'Nigerian Army',
    'Navy',
    'Air Force',
    'DSSC',
    'NDA',
    'NSCDC',
    'EFCC',
  ];

  // Mock data for posts
  final List<PostModel> _posts = [
    PostModel(
      id: '1',
      createdAt: DateTime.now().subtract(const Duration(hours: 2)),
      userId: 'user1',
      userName: 'John Doe',
      userProfileImageUrl: null,
      title: 'Tips for Nigerian Army Physical Test',
      content:
          'I recently passed the Nigerian Army physical test with flying colors. Here are some tips that helped me prepare...',
      agency: 'Nigerian Army',
      tags: ['Physical Test', 'Tips', 'Training'],
      likesCount: 24,
      commentsCount: 5,
      isLikedByCurrentUser: true,
      icon: Icons.fitness_center,
      color: Colors.green,
    ),
    PostModel(
      id: '2',
      createdAt: DateTime.now().subtract(const Duration(days: 1)),
      userId: 'user2',
      userName: 'Jane Smith',
      userProfileImageUrl: null,
      title: 'DSSC Interview Questions 2023',
      content:
          'I attended the DSSC interview last week. Here are some of the questions I was asked...',
      agency: 'DSSC',
      tags: ['Interview', 'Questions', 'Experience'],
      likesCount: 42,
      commentsCount: 12,
      isLikedByCurrentUser: false,
      imageUrl: 'assets/images/content/interview.jpg',
      icon: Icons.question_answer,
      color: Colors.blue,
    ),
    PostModel(
      id: '3',
      createdAt: DateTime.now().subtract(const Duration(days: 2)),
      userId: 'user3',
      userName: 'Michael Johnson',
      userProfileImageUrl: null,
      title: 'Air Force Aptitude Test Study Guide',
      content:
          'I created a comprehensive study guide for the Air Force aptitude test. It covers all the topics you need to know...',
      agency: 'Air Force',
      tags: ['Aptitude Test', 'Study Guide', 'Preparation'],
      likesCount: 78,
      commentsCount: 15,
      isLikedByCurrentUser: false,
      icon: Icons.book,
      color: Colors.orange,
    ),
  ];

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 4, vsync: this);
  }

  @override
  void dispose() {
    _tabController.dispose();
    _searchController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final layoutType = ResponsiveUtils.getLayoutType(context);
    final supportsMultiPane = ResponsiveUtils.supportsMultiPane(context);

    return Scaffold(
      body:
          supportsMultiPane && layoutType != LayoutType.mobilePortrait
              ? _buildMultiPaneLayout(context)
              : _buildSinglePaneLayout(context),
      floatingActionButton:
          widget.user.isPremium
              ? FloatingActionButton(
                backgroundColor: AppTheme.primaryColor,
                child: const Icon(Icons.add),
                onPressed: () {
                  _showCreatePostDialog();
                },
              )
              : null,
    );
  }

  Widget _buildSinglePaneLayout(BuildContext context) {
    return Column(
      children: [
        _buildHeader(),
        _buildTabBar(),
        Expanded(
          child: TabBarView(
            controller: _tabController,
            children: [
              _buildFeedTab(),
              _buildAgencyForumsTab(),
              _buildStudyGroupsTab(),
              _buildMyPostsTab(),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildMultiPaneLayout(BuildContext context) {
    return ResponsiveMultiPaneLayout(
      primaryPane: Column(
        children: [
          _buildHeader(),
          _buildTabBar(),
          Expanded(
            child: TabBarView(
              controller: _tabController,
              children: [
                _buildFeedTab(),
                _buildAgencyForumsTab(),
                _buildStudyGroupsTab(),
                _buildMyPostsTab(),
              ],
            ),
          ),
        ],
      ),
      secondaryPane: _buildSidePanel(context),
      primaryFlex: 2,
      secondaryFlex: 1,
    );
  }

  Widget _buildSidePanel(BuildContext context) {
    return Container(
      padding: ResponsiveUtils.getResponsivePadding(context),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          ResponsiveText(
            'Community Stats',
            mobileFontSize: 18.0,
            tabletFontSize: 20.0,
            desktopFontSize: 22.0,
            fontWeight: FontWeight.bold,
          ),
          SizedBox(height: ResponsiveUtils.getResponsiveSpacing(context)),
          _buildStatCard('Total Posts', '${_posts.length}', Icons.article),
          SizedBox(height: ResponsiveUtils.getResponsiveSpacing(context) / 2),
          _buildStatCard('Active Users', '1,234', Icons.people),
          SizedBox(height: ResponsiveUtils.getResponsiveSpacing(context) / 2),
          _buildStatCard('Study Groups', '45', Icons.group),
          SizedBox(height: ResponsiveUtils.getResponsiveSpacing(context)),
          ResponsiveText(
            'Quick Actions',
            mobileFontSize: 16.0,
            tabletFontSize: 18.0,
            desktopFontSize: 20.0,
            fontWeight: FontWeight.bold,
          ),
          SizedBox(height: ResponsiveUtils.getResponsiveSpacing(context) / 2),
          _buildQuickActionCard('Badges', Icons.emoji_events, () {
            Navigator.of(context).push(
              MaterialPageRoute(
                builder: (context) => BadgesScreen(user: widget.user),
              ),
            );
          }),
          SizedBox(height: ResponsiveUtils.getResponsiveSpacing(context) / 2),
          _buildQuickActionCard('Success Stories', Icons.star, () {
            Navigator.of(context).push(
              MaterialPageRoute(
                builder: (context) => SuccessStoriesScreen(user: widget.user),
              ),
            );
          }),
        ],
      ),
    );
  }

  Widget _buildStatCard(String title, String value, IconData icon) {
    return ResponsiveCard(
      child: Row(
        children: [
          Container(
            padding: const EdgeInsets.all(8),
            decoration: BoxDecoration(
              color: AppTheme.primaryColor.withValues(alpha: 0.1 * 255),
              borderRadius: BorderRadius.circular(8),
            ),
            child: Icon(icon, color: AppTheme.primaryColor, size: 20),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                ResponsiveText(
                  value,
                  mobileFontSize: 18.0,
                  tabletFontSize: 20.0,
                  desktopFontSize: 22.0,
                  fontWeight: FontWeight.bold,
                  color: AppTheme.primaryColor,
                ),
                ResponsiveText(
                  title,
                  mobileFontSize: 12.0,
                  tabletFontSize: 13.0,
                  desktopFontSize: 14.0,
                  color: Colors.grey[600],
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildQuickActionCard(
    String title,
    IconData icon,
    VoidCallback onTap,
  ) {
    return ResponsiveCard(
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(
          ResponsiveUtils.getResponsiveBorderRadius(context),
        ),
        child: Padding(
          padding: const EdgeInsets.all(12.0),
          child: Row(
            children: [
              Icon(icon, color: AppTheme.primaryColor, size: 20),
              const SizedBox(width: 12),
              Expanded(
                child: ResponsiveText(
                  title,
                  mobileFontSize: 14.0,
                  tabletFontSize: 15.0,
                  desktopFontSize: 16.0,
                  fontWeight: FontWeight.w600,
                ),
              ),
              Icon(Icons.arrow_forward_ios, size: 14, color: Colors.grey[600]),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildHeader() {
    final isSmallScreen = ResponsiveUtils.isSmallPhone(context);
    final padding = ResponsiveUtils.getResponsivePadding(context);
    final spacing = ResponsiveUtils.getResponsiveSpacing(context);

    return Container(
      padding: padding,
      decoration: BoxDecoration(
        gradient: const LinearGradient(
          colors: [
            Color(0xFF9C27B0), // Purple
            Color(0xFF7B1FA2), // Deep Purple
          ],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        boxShadow: [
          BoxShadow(
            color: const Color(
              0xFF9C27B0,
            ).withValues(alpha: (0.4 * 255).round().toDouble()),
            blurRadius: 15,
            offset: const Offset(0, 8),
            spreadRadius: 2,
          ),
        ],
      ),
      child: SafeArea(
        child: Column(
          children: [
            Row(
              children: [
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      ResponsiveText(
                        'Community',
                        mobileFontSize: isSmallScreen ? 22.0 : 24.0,
                        tabletFontSize: 26.0,
                        desktopFontSize: 28.0,
                        fontWeight: FontWeight.bold,
                        color: Colors.white,
                        style: const TextStyle(
                          letterSpacing: 0.5,
                          shadows: [
                            Shadow(
                              color: Colors.black38,
                              blurRadius: 3,
                              offset: Offset(0, 1),
                            ),
                          ],
                        ),
                      ),
                      SizedBox(height: spacing / 4),
                      ResponsiveText(
                        'Connect with fellow aspirants',
                        mobileFontSize: isSmallScreen ? 13.0 : 14.0,
                        tabletFontSize: 15.0,
                        desktopFontSize: 16.0,
                        fontWeight: FontWeight.w500,
                        color: Colors.white,
                        style: const TextStyle(
                          letterSpacing: 0.3,
                          shadows: [
                            Shadow(
                              color: Colors.black26,
                              blurRadius: 2,
                              offset: Offset(0, 1),
                            ),
                          ],
                        ),
                      ),
                    ],
                  ),
                ),
                // Responsive action buttons
                if (isSmallScreen) ...[
                  // On small screens, show only essential buttons
                  _buildActionButton(Icons.search, () => _showSearchDialog()),
                  SizedBox(width: spacing / 2),
                  _buildActionButton(
                    Icons.filter_list,
                    () => _showFilterDialog(),
                  ),
                ] else ...[
                  // On larger screens, show all buttons
                  _buildActionButton(Icons.search, () => _showSearchDialog()),
                  SizedBox(width: spacing / 2),
                  _buildActionButton(
                    Icons.filter_list,
                    () => _showFilterDialog(),
                  ),
                  SizedBox(width: spacing / 2),
                  _buildActionButton(Icons.emoji_events, () {
                    Navigator.of(context).push(
                      MaterialPageRoute(
                        builder: (context) => BadgesScreen(user: widget.user),
                      ),
                    );
                  }),
                  SizedBox(width: spacing / 2),
                  _buildActionButton(Icons.star, () {
                    Navigator.of(context).push(
                      MaterialPageRoute(
                        builder:
                            (context) =>
                                SuccessStoriesScreen(user: widget.user),
                      ),
                    );
                  }),
                ],
              ],
            ),
            const SizedBox(height: 16),
            if (!widget.user.isPremium)
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: Colors.white.withValues(alpha: 0.15 * 255),
                  borderRadius: BorderRadius.circular(12),
                  border: Border.all(
                    color: Colors.white.withValues(alpha: 0.2 * 255),
                    width: 1,
                  ),
                ),
                child: Row(
                  children: [
                    Container(
                      padding: const EdgeInsets.all(8),
                      decoration: BoxDecoration(
                        color: Colors.white.withValues(alpha: 0.2 * 255),
                        shape: BoxShape.circle,
                      ),
                      child: const Icon(
                        Icons.workspace_premium,
                        color: Colors.white,
                        size: 20,
                      ),
                    ),
                    const SizedBox(width: 12),
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          const Text(
                            'Upgrade to Premium',
                            style: TextStyle(
                              fontSize: 14,
                              fontWeight: FontWeight.bold,
                              color: Colors.white,
                            ),
                          ),
                          const SizedBox(height: 2),
                          Text(
                            'Create posts and join discussions',
                            style: TextStyle(
                              fontSize: 12,
                              color: Colors.white.withValues(alpha: 0.8 * 255),
                            ),
                          ),
                        ],
                      ),
                    ),
                    BaseButton(
                      text: 'Upgrade',
                      icon: Icons.arrow_forward,
                      backgroundColor: Colors.white,
                      textColor: const Color(0xFF9C27B0),
                      height: 36,
                      onPressed: () {
                        Navigator.of(context).pushNamed(AppRoutes.premium);
                      },
                    ),
                  ],
                ),
              ),
          ],
        ),
      ),
    );
  }

  Widget _buildActionButton(IconData icon, VoidCallback onPressed) {
    return Container(
      decoration: BoxDecoration(
        color: Colors.white.withValues(alpha: 0.2 * 255),
        borderRadius: BorderRadius.circular(12),
      ),
      child: IconButton(
        icon: Icon(icon, color: Colors.white),
        onPressed: onPressed,
      ),
    );
  }

  Widget _buildTabBar() {
    return Container(
      decoration: BoxDecoration(
        color: Colors.white,
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withValues(alpha: 0.1 * 255),
            blurRadius: 6,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: TabBar(
        controller: _tabController,
        labelColor: const Color(0xFF9C27B0),
        unselectedLabelColor: AppTheme.textSecondaryLight,
        indicatorColor: const Color(0xFF9C27B0),
        indicatorWeight: 3,
        labelStyle: const TextStyle(
          fontWeight: FontWeight.bold,
          fontSize: 14,
          letterSpacing: 0.5,
        ),
        unselectedLabelStyle: const TextStyle(
          fontWeight: FontWeight.w500,
          fontSize: 14,
        ),
        tabs: const [
          Tab(text: 'FEED'),
          Tab(text: 'FORUMS'),
          Tab(text: 'STUDY GROUPS'),
          Tab(text: 'MY POSTS'),
        ],
      ),
    );
  }

  Widget _buildFeedTab() {
    return _posts.isEmpty
        ? _buildEmptyState(
          'No posts yet',
          'Be the first to share something with the community!',
        )
        : ListView.builder(
          padding: const EdgeInsets.all(16),
          itemCount: _posts.length,
          itemBuilder: (context, index) {
            return _buildPostCard(_posts[index]);
          },
        );
  }

  Widget _buildAgencyForumsTab() {
    return ListView.builder(
      padding: const EdgeInsets.all(16),
      itemCount: _agencies.length - 1, // Skip "All"
      itemBuilder: (context, index) {
        final agency = _agencies[index + 1]; // Skip "All"
        final agencyPosts =
            _posts.where((post) => post.agency == agency).toList();

        return _buildAgencyCard(
          agency: agency,
          postsCount: agencyPosts.length,
          color: _getAgencyColor(agency),
          icon: _getAgencyIcon(agency),
        );
      },
    );
  }

  Widget _buildStudyGroupsTab() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.group,
            size: 80,
            color: Colors.grey.withValues(alpha: 0.3 * 255),
          ),
          const SizedBox(height: 16),
          const Text(
            'Study Groups',
            style: TextStyle(
              fontSize: 24,
              fontWeight: FontWeight.bold,
              color: Colors.black87,
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 8),
          Text(
            'Form study groups with fellow aspirants to prepare together',
            style: TextStyle(fontSize: 16, color: Colors.grey.shade600),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 24),
          BaseButton(
            text: 'Explore Study Groups',
            icon: Icons.people,
            backgroundColor: AppTheme.primaryColor,
            onPressed: () {
              Navigator.of(context).push(
                MaterialPageRoute(
                  builder: (context) => StudyGroupsScreen(user: widget.user),
                ),
              );
            },
          ),
        ],
      ),
    );
  }

  Widget _buildMyPostsTab() {
    final myPosts =
        _posts.where((post) => post.userId == widget.user.id).toList();

    return myPosts.isEmpty
        ? _buildEmptyState(
          'No posts yet',
          widget.user.isPremium
              ? 'Share your knowledge and experiences with the community!'
              : 'Upgrade to premium to create posts and share your experiences.',
        )
        : ListView.builder(
          padding: const EdgeInsets.all(16),
          itemCount: myPosts.length,
          itemBuilder: (context, index) {
            return _buildPostCard(myPosts[index]);
          },
        );
  }

  Widget _buildEmptyState(String title, String message) {
    return Center(
      child: Padding(
        padding: const EdgeInsets.all(32.0),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.forum_outlined, size: 80, color: Colors.grey[300]),
            const SizedBox(height: 24),
            Text(
              title,
              style: Theme.of(context).textTheme.titleLarge?.copyWith(
                fontWeight: FontWeight.bold,
                color: AppTheme.textPrimaryDark,
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 12),
            Text(
              message,
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                color: AppTheme.textSecondaryLight,
              ),
              textAlign: TextAlign.center,
            ),
            if (!widget.user.isPremium) ...[
              const SizedBox(height: 24),
              BaseButton(
                text: 'Upgrade to Premium',
                icon: Icons.workspace_premium,
                backgroundColor: const Color(0xFF9C27B0),
                onPressed: () {
                  Navigator.of(context).pushNamed(AppRoutes.premium);
                },
              ),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildPostCard(PostModel post) {
    return Container(
      margin: const EdgeInsets.only(bottom: 16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withValues(alpha: 0.1 * 255),
            blurRadius: 10,
            offset: const Offset(0, 4),
            spreadRadius: 0,
          ),
          // Highlight shadow for 3D effect
          BoxShadow(
            color: Colors.white,
            blurRadius: 3,
            offset: const Offset(-1, -1),
            spreadRadius: 0,
          ),
        ],
        border: Border.all(
          color: post.color.withValues(alpha: 0.1 * 255),
          width: 1,
        ),
      ),
      child: ClipRRect(
        borderRadius: BorderRadius.circular(16),
        child: Material(
          color: Colors.transparent,
          child: InkWell(
            onTap: () {
              _navigateToPostDetail(post);
            },
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Post header
                Padding(
                  padding: const EdgeInsets.all(16),
                  child: Row(
                    children: [
                      // User avatar
                      CircleAvatar(
                        radius: 20,
                        backgroundColor: post.color.withValues(
                          alpha: 0.2 * 255,
                        ),
                        backgroundImage:
                            post.userProfileImageUrl != null
                                ? NetworkImage(post.userProfileImageUrl!)
                                : null,
                        child:
                            post.userProfileImageUrl == null
                                ? Text(
                                  post.userName.substring(0, 1).toUpperCase(),
                                  style: TextStyle(
                                    fontWeight: FontWeight.bold,
                                    color: post.color,
                                  ),
                                )
                                : null,
                      ),
                      const SizedBox(width: 12),
                      // User name and post info
                      Expanded(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              post.userName,
                              style: const TextStyle(
                                fontWeight: FontWeight.bold,
                                fontSize: 15,
                                letterSpacing: 0.2,
                                color: Colors.black87,
                              ),
                            ),
                            const SizedBox(height: 2),
                            Row(
                              children: [
                                Container(
                                  padding: const EdgeInsets.symmetric(
                                    horizontal: 6,
                                    vertical: 2,
                                  ),
                                  decoration: BoxDecoration(
                                    color: _getAgencyColor(
                                      post.agency,
                                    ).withValues(alpha: 0.1 * 255),
                                    borderRadius: BorderRadius.circular(4),
                                  ),
                                  child: Text(
                                    post.agency,
                                    style: TextStyle(
                                      fontSize: 10,
                                      fontWeight: FontWeight.w500,
                                      color: _getAgencyColor(post.agency),
                                    ),
                                  ),
                                ),
                                const SizedBox(width: 8),
                                Text(
                                  _formatTimeAgo(post.createdAt),
                                  style: TextStyle(
                                    fontSize: 12,
                                    color: Colors.grey[600],
                                  ),
                                ),
                              ],
                            ),
                          ],
                        ),
                      ),
                      // More options
                      IconButton(
                        icon: const Icon(Icons.more_vert, size: 20),
                        onPressed: () {
                          _showPostOptions(post);
                        },
                      ),
                    ],
                  ),
                ),
                // Post title
                Padding(
                  padding: const EdgeInsets.symmetric(horizontal: 16),
                  child: Text(
                    post.title,
                    style: const TextStyle(
                      fontWeight: FontWeight.bold,
                      fontSize: 18,
                      letterSpacing: 0.3,
                      color: Colors.black87,
                    ),
                  ),
                ),
                // Post content
                Padding(
                  padding: const EdgeInsets.symmetric(
                    horizontal: 16,
                    vertical: 8,
                  ),
                  child: Text(
                    post.content,
                    style: const TextStyle(
                      fontSize: 15,
                      height: 1.4,
                      color: Colors.black87,
                      letterSpacing: 0.2,
                    ),
                    maxLines: 3,
                    overflow: TextOverflow.ellipsis,
                  ),
                ),
                // Post image if available
                if (post.imageUrl != null)
                  Container(
                    height: 200,
                    width: double.infinity,
                    margin: const EdgeInsets.symmetric(vertical: 8),
                    decoration: BoxDecoration(
                      image: DecorationImage(
                        image: AssetImage(post.imageUrl!),
                        fit: BoxFit.cover,
                      ),
                    ),
                  ),
                // Tags
                if (post.tags.isNotEmpty)
                  Padding(
                    padding: const EdgeInsets.symmetric(
                      horizontal: 16,
                      vertical: 8,
                    ),
                    child: Wrap(
                      spacing: 8,
                      runSpacing: 8,
                      children:
                          post.tags.map((tag) {
                            return Container(
                              padding: const EdgeInsets.symmetric(
                                horizontal: 8,
                                vertical: 4,
                              ),
                              decoration: BoxDecoration(
                                color: Colors.grey[100],
                                borderRadius: BorderRadius.circular(12),
                                border: Border.all(
                                  color: Colors.grey[300]!,
                                  width: 1,
                                ),
                              ),
                              child: Text(
                                '#$tag',
                                style: TextStyle(
                                  fontSize: 12,
                                  color: Colors.grey[700],
                                ),
                              ),
                            );
                          }).toList(),
                    ),
                  ),
                // Post actions
                Padding(
                  padding: const EdgeInsets.all(16),
                  child: Row(
                    children: [
                      // Like button
                      InkWell(
                        onTap: () {
                          _toggleLike(post);
                        },
                        borderRadius: BorderRadius.circular(8),
                        child: Padding(
                          padding: const EdgeInsets.all(8.0),
                          child: Row(
                            children: [
                              Icon(
                                post.isLikedByCurrentUser
                                    ? Icons.favorite
                                    : Icons.favorite_border,
                                size: 20,
                                color:
                                    post.isLikedByCurrentUser
                                        ? Colors.red
                                        : Colors.grey[600],
                              ),
                              const SizedBox(width: 4),
                              Text(
                                post.likesCount.toString(),
                                style: TextStyle(
                                  fontSize: 14,
                                  color:
                                      post.isLikedByCurrentUser
                                          ? Colors.red
                                          : Colors.grey[600],
                                ),
                              ),
                            ],
                          ),
                        ),
                      ),
                      const SizedBox(width: 16),
                      // Comment button
                      InkWell(
                        onTap: () {
                          _navigateToPostDetail(post);
                        },
                        borderRadius: BorderRadius.circular(8),
                        child: Padding(
                          padding: const EdgeInsets.all(8.0),
                          child: Row(
                            children: [
                              Icon(
                                Icons.chat_bubble_outline,
                                size: 20,
                                color: Colors.grey[600],
                              ),
                              const SizedBox(width: 4),
                              Text(
                                post.commentsCount.toString(),
                                style: TextStyle(
                                  fontSize: 14,
                                  color: Colors.grey[600],
                                ),
                              ),
                            ],
                          ),
                        ),
                      ),
                      const SizedBox(width: 16),
                      // Share button
                      InkWell(
                        onTap: () {
                          _sharePost(post);
                        },
                        borderRadius: BorderRadius.circular(8),
                        child: Padding(
                          padding: const EdgeInsets.all(8.0),
                          child: Icon(
                            Icons.share,
                            size: 20,
                            color: Colors.grey[600],
                          ),
                        ),
                      ),
                      const Spacer(),
                      // Save button
                      InkWell(
                        onTap: () {
                          _savePost(post);
                        },
                        borderRadius: BorderRadius.circular(8),
                        child: Padding(
                          padding: const EdgeInsets.all(8.0),
                          child: Icon(
                            Icons.bookmark_border,
                            size: 20,
                            color: Colors.grey[600],
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildAgencyCard({
    required String agency,
    required int postsCount,
    required Color color,
    required IconData icon,
  }) {
    return Container(
      margin: const EdgeInsets.only(bottom: 16),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [
            Colors.white,
            Color.lerp(Colors.white, color, 0.05) ?? Colors.white,
          ],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withValues(alpha: 0.1 * 255),
            blurRadius: 10,
            offset: const Offset(0, 4),
            spreadRadius: 0,
          ),
          // Highlight shadow for 3D effect
          BoxShadow(
            color: Colors.white,
            blurRadius: 3,
            offset: const Offset(-1, -1),
            spreadRadius: 0,
          ),
        ],
        border: Border.all(color: color.withValues(alpha: 0.1 * 255), width: 1),
      ),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          onTap: () {
            _navigateToAgencyForum(agency);
          },
          borderRadius: BorderRadius.circular(16),
          child: Padding(
            padding: const EdgeInsets.all(16),
            child: Row(
              children: [
                // Agency icon
                Container(
                  padding: const EdgeInsets.all(12),
                  decoration: BoxDecoration(
                    gradient: LinearGradient(
                      colors: [
                        color.withValues(alpha: 0.2 * 255),
                        color.withValues(alpha: 0.1 * 255),
                      ],
                      begin: Alignment.topLeft,
                      end: Alignment.bottomRight,
                    ),
                    shape: BoxShape.circle,
                    boxShadow: [
                      BoxShadow(
                        color: color.withValues(alpha: 0.2 * 255),
                        blurRadius: 8,
                        offset: const Offset(0, 3),
                        spreadRadius: 0,
                      ),
                    ],
                  ),
                  child: Icon(icon, color: color, size: 24),
                ),
                const SizedBox(width: 16),
                // Agency info
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        agency,
                        style: const TextStyle(
                          fontWeight: FontWeight.bold,
                          fontSize: 16,
                        ),
                      ),
                      const SizedBox(height: 4),
                      Text(
                        '$postsCount posts',
                        style: TextStyle(fontSize: 14, color: Colors.grey[600]),
                      ),
                    ],
                  ),
                ),
                // Arrow icon
                Icon(
                  Icons.arrow_forward_ios,
                  size: 16,
                  color: Colors.grey[400],
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  void _showSearchDialog() {
    showDialog(
      context: context,
      builder: (context) {
        return AlertDialog(
          title: const Text('Search Posts'),
          content: TextField(
            controller: _searchController,
            decoration: const InputDecoration(
              hintText: 'Enter keywords...',
              prefixIcon: Icon(Icons.search),
            ),
            autofocus: true,
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.pop(context),
              child: const Text('CANCEL'),
            ),
            TextButton(
              onPressed: () {
                // Implement search functionality
                Navigator.pop(context);
                // Show search results
              },
              child: const Text('SEARCH'),
            ),
          ],
        );
      },
    );
  }

  void _showFilterDialog() {
    showDialog(
      context: context,
      builder: (context) {
        return StatefulBuilder(
          builder: (context, setState) {
            return AlertDialog(
              title: const Text('Filter Posts'),
              content: Column(
                mainAxisSize: MainAxisSize.min,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  const Text(
                    'Agency',
                    style: TextStyle(fontWeight: FontWeight.bold, fontSize: 16),
                  ),
                  const SizedBox(height: 8),
                  Wrap(
                    spacing: 8,
                    runSpacing: 8,
                    children:
                        _agencies.map((agency) {
                          return ChoiceChip(
                            label: Text(agency),
                            selected: _selectedAgency == agency,
                            onSelected: (selected) {
                              setState(() {
                                _selectedAgency = agency;
                              });
                            },
                          );
                        }).toList(),
                  ),
                ],
              ),
              actions: [
                TextButton(
                  onPressed: () => Navigator.pop(context),
                  child: const Text('CANCEL'),
                ),
                TextButton(
                  onPressed: () {
                    // Apply filters
                    Navigator.pop(context);
                    // Update the UI with filtered posts
                    this.setState(() {});
                  },
                  child: const Text('APPLY'),
                ),
              ],
            );
          },
        );
      },
    );
  }

  void _showCreatePostDialog() {
    final titleController = TextEditingController();
    final contentController = TextEditingController();
    String selectedAgency = widget.user.targetAgency;

    showDialog(
      context: context,
      builder: (context) {
        return AlertDialog(
          title: const Text('Create Post'),
          content: SingleChildScrollView(
            child: Column(
              mainAxisSize: MainAxisSize.min,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                TextField(
                  controller: titleController,
                  decoration: const InputDecoration(
                    labelText: 'Title',
                    hintText: 'Enter post title...',
                  ),
                  maxLength: 100,
                ),
                const SizedBox(height: 16),
                TextField(
                  controller: contentController,
                  decoration: const InputDecoration(
                    labelText: 'Content',
                    hintText: 'Share your thoughts...',
                    alignLabelWithHint: true,
                  ),
                  maxLines: 5,
                  maxLength: 1000,
                ),
                const SizedBox(height: 16),
                DropdownButtonFormField<String>(
                  value: selectedAgency,
                  decoration: const InputDecoration(labelText: 'Agency'),
                  items:
                      _agencies.where((agency) => agency != 'All').map((
                        agency,
                      ) {
                        return DropdownMenuItem<String>(
                          value: agency,
                          child: Text(agency),
                        );
                      }).toList(),
                  onChanged: (value) {
                    selectedAgency = value!;
                  },
                ),
              ],
            ),
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.pop(context),
              child: const Text('CANCEL'),
            ),
            TextButton(
              onPressed: () {
                // Create post
                if (titleController.text.isNotEmpty &&
                    contentController.text.isNotEmpty) {
                  // Add post to the list
                  setState(() {
                    _posts.insert(
                      0,
                      PostModel(
                        id: DateTime.now().millisecondsSinceEpoch.toString(),
                        createdAt: DateTime.now(),
                        userId: widget.user.id,
                        userName: widget.user.fullName,
                        userProfileImageUrl: widget.user.profileImageUrl,
                        title: titleController.text,
                        content: contentController.text,
                        agency: selectedAgency,
                        tags: [],
                        likesCount: 0,
                        commentsCount: 0,
                        isLikedByCurrentUser: false,
                        icon: _getAgencyIcon(selectedAgency),
                        color: _getAgencyColor(selectedAgency),
                      ),
                    );
                  });
                  Navigator.pop(context);
                  ScaffoldMessenger.of(context).showSnackBar(
                    const SnackBar(content: Text('Post created successfully!')),
                  );
                }
              },
              child: const Text('POST'),
            ),
          ],
        );
      },
    );
  }

  void _navigateToPostDetail(PostModel post) {
    // Navigate to post detail screen
    ScaffoldMessenger.of(
      context,
    ).showSnackBar(SnackBar(content: Text('Viewing post: ${post.title}')));
  }

  void _navigateToAgencyForum(String agency) {
    // Navigate to agency forum screen
    Navigator.of(context).push(
      MaterialPageRoute(
        builder:
            (context) => AgencyForumScreen(
              user: widget.user,
              agency: agency,
              color: _getAgencyColor(agency),
              icon: _getAgencyIcon(agency),
            ),
      ),
    );
  }

  void _toggleLike(PostModel post) {
    setState(() {
      final index = _posts.indexWhere((p) => p.id == post.id);
      if (index != -1) {
        final updatedPost = post.copyWith(
          isLikedByCurrentUser: !post.isLikedByCurrentUser,
          likesCount:
              post.isLikedByCurrentUser
                  ? post.likesCount - 1
                  : post.likesCount + 1,
        );
        _posts[index] = updatedPost;
      }
    });
  }

  void _sharePost(PostModel post) {
    ScaffoldMessenger.of(
      context,
    ).showSnackBar(SnackBar(content: Text('Sharing post: ${post.title}')));
  }

  void _savePost(PostModel post) {
    ScaffoldMessenger.of(
      context,
    ).showSnackBar(SnackBar(content: Text('Post saved: ${post.title}')));
  }

  void _showPostOptions(PostModel post) {
    showModalBottomSheet(
      context: context,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(16)),
      ),
      builder: (context) {
        return SafeArea(
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              ListTile(
                leading: const Icon(Icons.bookmark_border),
                title: const Text('Save Post'),
                onTap: () {
                  Navigator.pop(context);
                  _savePost(post);
                },
              ),
              ListTile(
                leading: const Icon(Icons.share),
                title: const Text('Share Post'),
                onTap: () {
                  Navigator.pop(context);
                  _sharePost(post);
                },
              ),
              if (post.userId == widget.user.id)
                ListTile(
                  leading: const Icon(Icons.edit),
                  title: const Text('Edit Post'),
                  onTap: () {
                    Navigator.pop(context);
                    // Show edit post dialog
                  },
                ),
              if (post.userId == widget.user.id)
                ListTile(
                  leading: const Icon(Icons.delete, color: Colors.red),
                  title: const Text(
                    'Delete Post',
                    style: TextStyle(color: Colors.red),
                  ),
                  onTap: () {
                    Navigator.pop(context);
                    _showDeleteConfirmation(post);
                  },
                ),
              ListTile(
                leading: const Icon(Icons.flag),
                title: const Text('Report Post'),
                onTap: () {
                  Navigator.pop(context);
                  // Show report dialog
                },
              ),
            ],
          ),
        );
      },
    );
  }

  void _showDeleteConfirmation(PostModel post) {
    showDialog(
      context: context,
      builder: (context) {
        return AlertDialog(
          title: const Text('Delete Post'),
          content: const Text(
            'Are you sure you want to delete this post? This action cannot be undone.',
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.pop(context),
              child: const Text('CANCEL'),
            ),
            TextButton(
              onPressed: () {
                // Delete post
                setState(() {
                  _posts.removeWhere((p) => p.id == post.id);
                });
                Navigator.pop(context);
                ScaffoldMessenger.of(context).showSnackBar(
                  const SnackBar(content: Text('Post deleted successfully!')),
                );
              },
              child: const Text('DELETE', style: TextStyle(color: Colors.red)),
            ),
          ],
        );
      },
    );
  }

  Color _getAgencyColor(String agency) {
    switch (agency) {
      case 'Nigerian Army':
        return Colors.green;
      case 'Navy':
        return Colors.blue;
      case 'Air Force':
        return Colors.lightBlue;
      case 'DSSC':
        return Colors.purple;
      case 'NDA':
        return Colors.red;
      case 'NSCDC':
        return Colors.orange;
      case 'EFCC':
        return Colors.teal;
      default:
        return Colors.grey;
    }
  }

  IconData _getAgencyIcon(String agency) {
    switch (agency) {
      case 'Nigerian Army':
        return Icons.military_tech;
      case 'Navy':
        return Icons.sailing;
      case 'Air Force':
        return Icons.airplanemode_active;
      case 'DSSC':
        return Icons.school;
      case 'NDA':
        return Icons.shield;
      case 'NSCDC':
        return Icons.security;
      case 'EFCC':
        return Icons.gavel;
      default:
        return Icons.group;
    }
  }

  String _formatTimeAgo(DateTime dateTime) {
    final difference = DateTime.now().difference(dateTime);

    if (difference.inDays > 365) {
      return '${(difference.inDays / 365).floor()}y ago';
    } else if (difference.inDays > 30) {
      return '${(difference.inDays / 30).floor()}mo ago';
    } else if (difference.inDays > 0) {
      return '${difference.inDays}d ago';
    } else if (difference.inHours > 0) {
      return '${difference.inHours}h ago';
    } else if (difference.inMinutes > 0) {
      return '${difference.inMinutes}m ago';
    } else {
      return 'Just now';
    }
  }
}
