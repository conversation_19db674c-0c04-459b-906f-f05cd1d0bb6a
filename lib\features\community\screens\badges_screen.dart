import 'package:flutter/material.dart';
import 'package:fit_4_force/core/theme/app_theme.dart';
import 'package:fit_4_force/features/community/models/badge_model.dart';
import 'package:fit_4_force/shared/models/user_model.dart';
import 'package:intl/intl.dart';

class BadgesScreen extends StatefulWidget {
  final UserModel user;

  const BadgesScreen({super.key, required this.user});

  @override
  State<BadgesScreen> createState() => _BadgesScreenState();
}

class _BadgesScreenState extends State<BadgesScreen>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;
  final List<String> _categories = [
    'All',
    'Community',
    'Study',
    'Fitness',
    'Achievements',
  ];

  // Mock data for badges
  final List<BadgeModel> _badges = [
    BadgeModel(
      id: '1',
      createdAt: DateTime.now().subtract(const Duration(days: 30)),
      name: 'Community Contributor',
      description: 'Create 5 posts that help other aspirants',
      category: 'Community',
      level: 1,
      imageUrl: 'assets/images/badges/contributor.png',
      icon: Icons.forum,
      color: Colors.purple,
      isUnlocked: true,
      unlockedAt: DateTime.now().subtract(const Duration(days: 5)),
      progress: 5,
      requiredProgress: 5,
    ),
    BadgeModel(
      id: '2',
      createdAt: DateTime.now().subtract(const Duration(days: 30)),
      name: 'Study Master',
      description: 'Complete 10 study sessions',
      category: 'Study',
      level: 1,
      imageUrl: 'assets/images/badges/study.png',
      icon: Icons.book,
      color: Colors.blue,
      isUnlocked: true,
      unlockedAt: DateTime.now().subtract(const Duration(days: 10)),
      progress: 10,
      requiredProgress: 10,
    ),
    BadgeModel(
      id: '3',
      createdAt: DateTime.now().subtract(const Duration(days: 30)),
      name: 'Fitness Warrior',
      description: 'Complete 15 workouts',
      category: 'Fitness',
      level: 1,
      imageUrl: 'assets/images/badges/fitness.png',
      icon: Icons.fitness_center,
      color: Colors.green,
      isUnlocked: false,
      progress: 8,
      requiredProgress: 15,
    ),
    BadgeModel(
      id: '4',
      createdAt: DateTime.now().subtract(const Duration(days: 30)),
      name: 'Quiz Champion',
      description: 'Score 90% or higher on 5 quizzes',
      category: 'Study',
      level: 2,
      imageUrl: 'assets/images/badges/quiz.png',
      icon: Icons.quiz,
      color: Colors.orange,
      isUnlocked: false,
      progress: 3,
      requiredProgress: 5,
    ),
    BadgeModel(
      id: '5',
      createdAt: DateTime.now().subtract(const Duration(days: 30)),
      name: 'Group Leader',
      description: 'Create a study group with at least 5 members',
      category: 'Community',
      level: 2,
      imageUrl: 'assets/images/badges/leader.png',
      icon: Icons.people,
      color: Colors.indigo,
      isUnlocked: false,
      progress: 3,
      requiredProgress: 5,
    ),
    BadgeModel(
      id: '6',
      createdAt: DateTime.now().subtract(const Duration(days: 30)),
      name: 'Consistent Learner',
      description: 'Study for 7 consecutive days',
      category: 'Achievements',
      level: 1,
      imageUrl: 'assets/images/badges/streak.png',
      icon: Icons.calendar_today,
      color: Colors.teal,
      isUnlocked: true,
      unlockedAt: DateTime.now().subtract(const Duration(days: 2)),
      progress: 7,
      requiredProgress: 7,
    ),
  ];

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: _categories.length, vsync: this);
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text(
          'Achievement Badges',
          style: TextStyle(fontWeight: FontWeight.bold),
        ),
        bottom: TabBar(
          controller: _tabController,
          isScrollable: true,
          labelColor: AppTheme.primaryColor,
          unselectedLabelColor: AppTheme.textSecondaryLight,
          indicatorColor: AppTheme.primaryColor,
          indicatorWeight: 3,
          tabs: _categories.map((category) => Tab(text: category.toUpperCase())).toList(),
        ),
      ),
      body: TabBarView(
        controller: _tabController,
        children: _categories.map((category) {
          return _buildBadgesGrid(category);
        }).toList(),
      ),
    );
  }

  Widget _buildBadgesGrid(String category) {
    final filteredBadges = category == 'All'
        ? _badges
        : _badges.where((badge) => badge.category == category).toList();

    if (filteredBadges.isEmpty) {
      return _buildEmptyState(
        'No badges in this category',
        'Complete activities to earn badges!',
      );
    }

    return GridView.builder(
      padding: const EdgeInsets.all(16),
      gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: 2,
        childAspectRatio: 0.8,
        crossAxisSpacing: 16,
        mainAxisSpacing: 16,
      ),
      itemCount: filteredBadges.length,
      itemBuilder: (context, index) {
        return _buildBadgeCard(filteredBadges[index]);
      },
    );
  }

  Widget _buildBadgeCard(BadgeModel badge) {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(16),
      ),
      child: InkWell(
        onTap: () => _showBadgeDetails(badge),
        borderRadius: BorderRadius.circular(16),
        child: Padding(
          padding: const EdgeInsets.all(12),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              // Badge icon
              Stack(
                alignment: Alignment.center,
                children: [
                  Container(
                    width: 80,
                    height: 80,
                    decoration: BoxDecoration(
                      gradient: LinearGradient(
                        colors: [
                          badge.color.withValues(alpha: 0.2 * 255),
                          badge.color.withValues(alpha: 0.1 * 255),
                        ],
                        begin: Alignment.topLeft,
                        end: Alignment.bottomRight,
                      ),
                      shape: BoxShape.circle,
                      boxShadow: [
                        BoxShadow(
                          color: badge.color.withValues(alpha: 0.2 * 255),
                          blurRadius: 8,
                          offset: const Offset(0, 3),
                          spreadRadius: 0,
                        ),
                      ],
                    ),
                    child: Icon(
                      badge.icon,
                      color: badge.isUnlocked ? badge.color : Colors.grey,
                      size: 40,
                    ),
                  ),
                  if (!badge.isUnlocked)
                    Container(
                      width: 80,
                      height: 80,
                      decoration: BoxDecoration(
                        color: Colors.black.withValues(alpha: 0.5 * 255),
                        shape: BoxShape.circle,
                      ),
                      child: const Icon(
                        Icons.lock,
                        color: Colors.white,
                        size: 30,
                      ),
                    ),
                ],
              ),
              const SizedBox(height: 12),
              // Badge name
              Text(
                badge.name,
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                  color: badge.isUnlocked ? Colors.black87 : Colors.grey,
                ),
                textAlign: TextAlign.center,
                maxLines: 2,
                overflow: TextOverflow.ellipsis,
              ),
              const SizedBox(height: 8),
              // Badge level
              Container(
                padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 2),
                decoration: BoxDecoration(
                  color: badge.isUnlocked
                      ? badge.color.withValues(alpha: 0.1 * 255)
                      : Colors.grey.withValues(alpha: 0.1 * 255),
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Text(
                  'Level ${badge.level}',
                  style: TextStyle(
                    fontSize: 12,
                    color: badge.isUnlocked ? badge.color : Colors.grey,
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ),
              const SizedBox(height: 8),
              // Progress indicator
              if (!badge.isUnlocked)
                Column(
                  children: [
                    LinearProgressIndicator(
                      value: badge.progress / badge.requiredProgress,
                      backgroundColor: Colors.grey.withValues(alpha: 0.1 * 255),
                      valueColor: AlwaysStoppedAnimation<Color>(badge.color),
                    ),
                    const SizedBox(height: 4),
                    Text(
                      '${badge.progress}/${badge.requiredProgress}',
                      style: TextStyle(
                        fontSize: 12,
                        color: Colors.grey.shade600,
                      ),
                    ),
                  ],
                ),
              if (badge.isUnlocked)
                Text(
                  'Unlocked',
                  style: TextStyle(
                    fontSize: 12,
                    color: badge.color,
                    fontWeight: FontWeight.w500,
                  ),
                ),
            ],
          ),
        ),
      ),
    );
  }

  void _showBadgeDetails(BadgeModel badge) {
    showDialog(
      context: context,
      builder: (context) {
        return AlertDialog(
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(16),
          ),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              // Badge icon
              Container(
                width: 100,
                height: 100,
                decoration: BoxDecoration(
                  gradient: LinearGradient(
                    colors: [
                      badge.color.withValues(alpha: 0.2 * 255),
                      badge.color.withValues(alpha: 0.1 * 255),
                    ],
                    begin: Alignment.topLeft,
                    end: Alignment.bottomRight,
                  ),
                  shape: BoxShape.circle,
                  boxShadow: [
                    BoxShadow(
                      color: badge.color.withValues(alpha: 0.2 * 255),
                      blurRadius: 8,
                      offset: const Offset(0, 3),
                      spreadRadius: 0,
                    ),
                  ],
                ),
                child: Icon(
                  badge.icon,
                  color: badge.isUnlocked ? badge.color : Colors.grey,
                  size: 50,
                ),
              ),
              const SizedBox(height: 16),
              // Badge name
              Text(
                badge.name,
                style: const TextStyle(
                  fontSize: 20,
                  fontWeight: FontWeight.bold,
                  color: Colors.black87,
                ),
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 8),
              // Badge level
              Container(
                padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 4),
                decoration: BoxDecoration(
                  color: badge.isUnlocked
                      ? badge.color.withValues(alpha: 0.1 * 255)
                      : Colors.grey.withValues(alpha: 0.1 * 255),
                  borderRadius: BorderRadius.circular(16),
                ),
                child: Text(
                  'Level ${badge.level}',
                  style: TextStyle(
                    fontSize: 14,
                    color: badge.isUnlocked ? badge.color : Colors.grey,
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ),
              const SizedBox(height: 16),
              // Badge description
              Text(
                badge.description,
                style: const TextStyle(
                  fontSize: 16,
                  color: Colors.black87,
                ),
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 16),
              // Progress indicator
              if (!badge.isUnlocked) ...[
                LinearProgressIndicator(
                  value: badge.progress / badge.requiredProgress,
                  backgroundColor: Colors.grey.withValues(alpha: 0.1 * 255),
                  valueColor: AlwaysStoppedAnimation<Color>(badge.color),
                  minHeight: 8,
                  borderRadius: BorderRadius.circular(4),
                ),
                const SizedBox(height: 8),
                Text(
                  '${badge.progress}/${badge.requiredProgress} completed',
                  style: TextStyle(
                    fontSize: 14,
                    color: Colors.grey.shade600,
                  ),
                ),
              ],
              if (badge.isUnlocked && badge.unlockedAt != null) ...[
                const SizedBox(height: 8),
                Text(
                  'Unlocked on ${DateFormat('MMM d, yyyy').format(badge.unlockedAt!)}',
                  style: TextStyle(
                    fontSize: 14,
                    color: Colors.grey.shade600,
                  ),
                ),
              ],
            ],
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.pop(context),
              child: const Text('CLOSE'),
            ),
          ],
        );
      },
    );
  }

  Widget _buildEmptyState(String title, String message) {
    return Center(
      child: Padding(
        padding: const EdgeInsets.all(24.0),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.emoji_events,
              size: 80,
              color: Colors.grey.withValues(alpha: 0.3 * 255),
            ),
            const SizedBox(height: 16),
            Text(
              title,
              style: const TextStyle(
                fontSize: 20,
                fontWeight: FontWeight.bold,
                color: Colors.black87,
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 8),
            Text(
              message,
              style: TextStyle(
                fontSize: 16,
                color: Colors.grey.shade600,
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }
}
