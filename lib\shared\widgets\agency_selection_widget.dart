import 'package:flutter/material.dart';
import 'package:fit_4_force/core/theme/app_theme.dart';
import 'package:fit_4_force/features/prep/services/enhanced_study_material_service.dart';

/// Widget for selecting target agency
class AgencySelectionWidget extends StatefulWidget {
  final String? initialAgency;
  final Function(String agencyCode, String agencyName)? onAgencySelected;
  final bool showFullName;
  final bool isRequired;

  const AgencySelectionWidget({
    super.key,
    this.initialAgency,
    this.onAgencySelected,
    this.showFullName = true,
    this.isRequired = true,
  });

  @override
  State<AgencySelectionWidget> createState() => _AgencySelectionWidgetState();
}

class _AgencySelectionWidgetState extends State<AgencySelectionWidget> {
  final EnhancedStudyMaterialService _materialService = EnhancedStudyMaterialService();
  
  List<Map<String, dynamic>> _availableAgencies = [];
  String? _selectedAgency;
  String _selectedAgencyCode = '';
  bool _isLoading = true;

  @override
  void initState() {
    super.initState();
    _selectedAgency = widget.initialAgency;
    _loadAvailableAgencies();
  }

  Future<void> _loadAvailableAgencies() async {
    setState(() => _isLoading = true);
    
    try {
      // Load agencies from database or use static list as fallback
      final agencies = [
        {'code': 'army', 'name': 'Nigerian Army', 'full_name': 'Nigerian Army', 'icon': Icons.military_tech},
        {'code': 'navy', 'name': 'Nigerian Navy', 'full_name': 'Nigerian Navy', 'icon': Icons.anchor},
        {'code': 'airforce', 'name': 'Nigerian Air Force', 'full_name': 'Nigerian Air Force', 'icon': Icons.flight},
        {'code': 'nda', 'name': 'NDA', 'full_name': 'Nigerian Defence Academy', 'icon': Icons.school},
        {'code': 'dssc', 'name': 'DSSC/SSC', 'full_name': 'Direct Short Service Commission', 'icon': Icons.badge},
        {'code': 'polac', 'name': 'POLAC', 'full_name': 'Police Academy', 'icon': Icons.local_police},
        {'code': 'fire', 'name': 'Fire Service', 'full_name': 'Federal Fire Service', 'icon': Icons.local_fire_department},
        {'code': 'nscdc', 'name': 'NSCDC', 'full_name': 'Nigeria Security and Civil Defence Corps', 'icon': Icons.security},
        {'code': 'customs', 'name': 'Customs Service', 'full_name': 'Nigeria Customs Service', 'icon': Icons.business},
        {'code': 'immigration', 'name': 'Immigration', 'full_name': 'Nigeria Immigration Service', 'icon': Icons.travel_explore},
        {'code': 'frsc', 'name': 'FRSC', 'full_name': 'Federal Road Safety Corps', 'icon': Icons.traffic},
      ];
      
      setState(() {
        _availableAgencies = agencies;
        _isLoading = false;
        
        // Set initial selection if provided
        if (widget.initialAgency != null) {
          final initialAgencyData = agencies.firstWhere(
            (agency) => agency['code'] == widget.initialAgency || agency['name'] == widget.initialAgency,
            orElse: () => agencies.first,
          );
          _selectedAgency = initialAgencyData['name'] as String;
          _selectedAgencyCode = initialAgencyData['code'] as String;
        } else if (agencies.isNotEmpty) {
          _selectedAgency = agencies.first['name'] as String;
          _selectedAgencyCode = agencies.first['code'] as String;
        }
      });
    } catch (e) {
      setState(() => _isLoading = false);
    }
  }

  @override
  Widget build(BuildContext context) {
    if (_isLoading) {
      return const Center(
        child: Padding(
          padding: EdgeInsets.all(16),
          child: CircularProgressIndicator(),
        ),
      );
    }

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        DropdownButtonFormField<String>(
          value: _selectedAgency,
          decoration: InputDecoration(
            labelText: 'Target Agency${widget.isRequired ? ' *' : ''}',
            prefixIcon: const Icon(Icons.military_tech),
            border: const OutlineInputBorder(),
            filled: true,
            fillColor: Colors.grey[50],
          ),
          items: _availableAgencies.map((agency) {
            return DropdownMenuItem(
              value: agency['name'] as String,
              child: _buildAgencyItem(agency),
            );
          }).toList(),
          onChanged: (value) {
            if (value != null) {
              final selectedAgencyData = _availableAgencies.firstWhere(
                (agency) => agency['name'] == value,
              );
              setState(() {
                _selectedAgency = value;
                _selectedAgencyCode = selectedAgencyData['code'] as String;
              });
              
              // Notify parent widget
              widget.onAgencySelected?.call(
                _selectedAgencyCode,
                value,
              );
            }
          },
          validator: widget.isRequired ? (value) {
            if (value == null || value.isEmpty) {
              return 'Please select your target agency';
            }
            return null;
          } : null,
        ),
        
        if (_selectedAgency != null) ...[
          const SizedBox(height: 12),
          _buildAgencyInfo(),
        ],
      ],
    );
  }

  Widget _buildAgencyItem(Map<String, dynamic> agency) {
    return Row(
      children: [
        Icon(
          agency['icon'] as IconData,
          size: 20,
          color: AppTheme.primaryColor,
        ),
        const SizedBox(width: 12),
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisSize: MainAxisSize.min,
            children: [
              Text(
                agency['name'] as String,
                style: const TextStyle(
                  fontWeight: FontWeight.w500,
                  fontSize: 16,
                ),
              ),
              if (widget.showFullName) ...[
                const SizedBox(height: 2),
                Text(
                  agency['full_name'] as String,
                  style: TextStyle(
                    fontSize: 12,
                    color: Colors.grey[600],
                  ),
                ),
              ],
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildAgencyInfo() {
    final selectedAgencyData = _availableAgencies.firstWhere(
      (agency) => agency['name'] == _selectedAgency,
    );

    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: AppTheme.primaryColor.withValues(alpha: 0.05),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(
          color: AppTheme.primaryColor.withValues(alpha: 0.2),
        ),
      ),
      child: Row(
        children: [
          Container(
            padding: const EdgeInsets.all(8),
            decoration: BoxDecoration(
              color: AppTheme.primaryColor,
              borderRadius: BorderRadius.circular(8),
            ),
            child: Icon(
              selectedAgencyData['icon'] as IconData,
              color: Colors.white,
              size: 20,
            ),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'Selected Agency',
                  style: TextStyle(
                    fontSize: 12,
                    color: Colors.grey[600],
                    fontWeight: FontWeight.w500,
                  ),
                ),
                const SizedBox(height: 2),
                Text(
                  selectedAgencyData['full_name'] as String,
                  style: const TextStyle(
                    fontSize: 14,
                    fontWeight: FontWeight.w600,
                    color: AppTheme.primaryColor,
                  ),
                ),
              ],
            ),
          ),
          Icon(
            Icons.check_circle,
            color: Colors.green,
            size: 20,
          ),
        ],
      ),
    );
  }

  /// Get the currently selected agency code
  String get selectedAgencyCode => _selectedAgencyCode;

  /// Get the currently selected agency name
  String? get selectedAgencyName => _selectedAgency;
}

/// Simple agency selection dialog
class AgencySelectionDialog extends StatefulWidget {
  final String? currentAgency;
  final Function(String agencyCode, String agencyName)? onAgencySelected;

  const AgencySelectionDialog({
    super.key,
    this.currentAgency,
    this.onAgencySelected,
  });

  @override
  State<AgencySelectionDialog> createState() => _AgencySelectionDialogState();
}

class _AgencySelectionDialogState extends State<AgencySelectionDialog> {
  String? _selectedAgencyCode;
  String? _selectedAgencyName;

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      title: const Text('Select Target Agency'),
      content: SizedBox(
        width: double.maxFinite,
        child: AgencySelectionWidget(
          initialAgency: widget.currentAgency,
          onAgencySelected: (code, name) {
            _selectedAgencyCode = code;
            _selectedAgencyName = name;
          },
        ),
      ),
      actions: [
        TextButton(
          onPressed: () => Navigator.pop(context),
          child: const Text('Cancel'),
        ),
        ElevatedButton(
          onPressed: _selectedAgencyCode != null ? () {
            widget.onAgencySelected?.call(_selectedAgencyCode!, _selectedAgencyName!);
            Navigator.pop(context);
          } : null,
          style: ElevatedButton.styleFrom(
            backgroundColor: AppTheme.primaryColor,
            foregroundColor: Colors.white,
          ),
          child: const Text('Select'),
        ),
      ],
    );
  }
}
