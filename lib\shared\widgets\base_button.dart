import 'package:flutter/material.dart';
import 'package:fit_4_force/core/theme/app_theme.dart';
import 'package:fit_4_force/core/theme/app_ui.dart';

class BaseButton extends StatelessWidget {
  final String text;
  final VoidCallback? onPressed;
  final bool isDisabled;
  final double? width;
  final double? height;
  final Color? backgroundColor;
  final Color? textColor;
  final IconData? icon;
  final bool isOutlined;

  const BaseButton({
    super.key,
    required this.text,
    required this.onPressed,
    this.isDisabled = false,
    this.width,
    this.height = 50,
    this.backgroundColor,
    this.textColor,
    this.icon,
    this.isOutlined = false,
  });

  @override
  Widget build(BuildContext context) {
    final effectiveBackgroundColor = backgroundColor ?? AppTheme.primaryColor;
    final effectiveTextColor = textColor ?? Colors.white;

    if (isOutlined) {
      return SizedBox(
        width: width,
        height: height,
        child: OutlinedButton(
          onPressed: isDisabled || onPressed == null ? null : onPressed,
          style: AppUI.outlinedButtonStyle(
            foregroundColor: effectiveBackgroundColor,
            borderColor: effectiveBackgroundColor,
            borderRadius: BorderRadius.circular(AppTheme.borderRadiusLarge),
          ),
          child: _buildButtonContent(effectiveBackgroundColor),
        ),
      );
    }

    return SizedBox(
      width: width,
      height: height,
      child: ElevatedButton(
        onPressed: isDisabled || onPressed == null ? null : onPressed,
        style: AppUI.buttonStyle(
          backgroundColor: effectiveBackgroundColor,
          foregroundColor: effectiveTextColor,
          borderRadius: BorderRadius.circular(AppTheme.borderRadiusLarge),
          elevation: AppTheme.elevationSmall,
        ),
        child: _buildButtonContent(effectiveTextColor),
      ),
    );
  }

  Widget _buildButtonContent(Color color) {
    final textStyle = TextStyle(
      color: color,
      fontSize: AppTheme.fontSizeSubtitle,
      fontWeight: AppTheme.fontWeightSemiBold,
      letterSpacing: 0.5,
    );

    if (icon != null) {
      return Row(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(icon, color: color, size: 20),
          const SizedBox(width: AppTheme.spacingSmall),
          Text(text, style: textStyle),
        ],
      );
    }

    return Text(text, style: textStyle);
  }
}
