import 'package:fit_4_force/shared/models/user_model.dart';
import 'package:logger/logger.dart';

/// Mock service for handling authentication (no actual Supabase connection)
class SupabaseAuthService {
  final Logger _logger = Logger();

  // Mock user data
  UserModel? _currentUser;

  /// Get current user ID
  String? get currentUserId => _currentUser?.id;

  /// Check if user is authenticated
  bool get isAuthenticated => _currentUser != null;

  /// Sign up with email and password
  Future<UserModel> signUp({
    required String email,
    required String password,
    required String fullName,
    required int age,
    required String gender,
    required double height,
    required double weight,
    required String targetAgency,
    required String fitnessGoal,
    required Map<String, bool> notificationPreferences,
  }) async {
    try {
      // Create mock user
      final user = UserModel(
        id: DateTime.now().millisecondsSinceEpoch.toString(),
        createdAt: DateTime.now(),
        fullName: fullName,
        email: email,
        age: age,
        gender: gender,
        height: height,
        weight: weight,
        targetAgency: targetAgency,
        fitnessGoal: fitnessGoal,
        isPremium: false,
        notificationPreferences: notificationPreferences,
        completedQuizzes: [],
        savedWorkouts: [],
      );

      // Set as current user
      _currentUser = user;

      _logger.i('Mock user created: ${user.fullName}');
      return user;
    } catch (e) {
      _logger.e('Error signing up: $e');
      rethrow;
    }
  }

  /// Sign in with email and password
  Future<UserModel> signIn({
    required String email,
    required String password,
  }) async {
    try {
      // Create a mock user for testing
      final user = UserModel(
        id: '123456789',
        createdAt: DateTime.now(),
        fullName: 'Test User',
        email: email,
        age: 25,
        gender: 'Male',
        height: 175.0,
        weight: 70.0,
        targetAgency: 'Nigerian Army',
        fitnessGoal: 'Pass fitness test',
        isPremium: false,
        notificationPreferences: {'push': true, 'email': true},
        completedQuizzes: [],
        savedWorkouts: [],
      );

      // Set as current user
      _currentUser = user;

      _logger.i('Mock user signed in: ${user.fullName}');
      return user;
    } catch (e) {
      _logger.e('Error signing in: $e');
      rethrow;
    }
  }

  /// Sign out
  Future<void> signOut() async {
    try {
      _currentUser = null;
      _logger.i('Mock user signed out');
    } catch (e) {
      _logger.e('Error signing out: $e');
      rethrow;
    }
  }

  /// Reset password
  Future<void> resetPassword(String email) async {
    try {
      _logger.i('Mock password reset for: $email');
    } catch (e) {
      _logger.e('Error resetting password: $e');
      rethrow;
    }
  }

  /// Get current user
  Future<UserModel?> getCurrentUser() async {
    try {
      return _currentUser;
    } catch (e) {
      _logger.e('Error getting current user: $e');
      return null;
    }
  }

  /// Update user profile
  Future<void> updateProfile(String userId, Map<String, dynamic> data) async {
    try {
      if (_currentUser != null && _currentUser!.id == userId) {
        // Update the current user with the new data
        _currentUser = UserModel(
          id: _currentUser!.id,
          createdAt: _currentUser!.createdAt,
          fullName: data['fullName'] ?? _currentUser!.fullName,
          email: data['email'] ?? _currentUser!.email,
          age: data['age'] ?? _currentUser!.age,
          gender: data['gender'] ?? _currentUser!.gender,
          height: data['height'] ?? _currentUser!.height,
          weight: data['weight'] ?? _currentUser!.weight,
          targetAgency: data['targetAgency'] ?? _currentUser!.targetAgency,
          fitnessGoal: data['fitnessGoal'] ?? _currentUser!.fitnessGoal,
          isPremium: data['isPremium'] ?? _currentUser!.isPremium,
          notificationPreferences:
              data['notificationPreferences'] ??
              _currentUser!.notificationPreferences,
          completedQuizzes:
              data['completedQuizzes'] ?? _currentUser!.completedQuizzes,
          savedWorkouts: data['savedWorkouts'] ?? _currentUser!.savedWorkouts,
        );
      }
      _logger.i('Mock profile updated for user: $userId');
    } catch (e) {
      _logger.e('Error updating profile: $e');
      rethrow;
    }
  }

  /// Update user email
  Future<void> updateEmail(String newEmail, String password) async {
    try {
      if (_currentUser != null) {
        // Update the current user's email
        _currentUser = UserModel(
          id: _currentUser!.id,
          createdAt: _currentUser!.createdAt,
          fullName: _currentUser!.fullName,
          email: newEmail,
          age: _currentUser!.age,
          gender: _currentUser!.gender,
          height: _currentUser!.height,
          weight: _currentUser!.weight,
          targetAgency: _currentUser!.targetAgency,
          fitnessGoal: _currentUser!.fitnessGoal,
          isPremium: _currentUser!.isPremium,
          notificationPreferences: _currentUser!.notificationPreferences,
          completedQuizzes: _currentUser!.completedQuizzes,
          savedWorkouts: _currentUser!.savedWorkouts,
        );
      }
      _logger.i('Mock email updated to: $newEmail');
    } catch (e) {
      _logger.e('Error updating email: $e');
      rethrow;
    }
  }

  /// Update user password
  Future<void> updatePassword(
    String currentPassword,
    String newPassword,
  ) async {
    try {
      _logger.i('Mock password updated');
    } catch (e) {
      _logger.e('Error updating password: $e');
      rethrow;
    }
  }

  /// Update premium status
  Future<void> updatePremiumStatus(
    String userId,
    bool isPremium,
    DateTime? expiryDate,
  ) async {
    try {
      if (_currentUser != null && _currentUser!.id == userId) {
        // Update the current user's premium status
        _currentUser = UserModel(
          id: _currentUser!.id,
          createdAt: _currentUser!.createdAt,
          fullName: _currentUser!.fullName,
          email: _currentUser!.email,
          age: _currentUser!.age,
          gender: _currentUser!.gender,
          height: _currentUser!.height,
          weight: _currentUser!.weight,
          targetAgency: _currentUser!.targetAgency,
          fitnessGoal: _currentUser!.fitnessGoal,
          isPremium: isPremium,
          notificationPreferences: _currentUser!.notificationPreferences,
          completedQuizzes: _currentUser!.completedQuizzes,
          savedWorkouts: _currentUser!.savedWorkouts,
        );
      }
      _logger.i(
        'Mock premium status updated for user: $userId, isPremium: $isPremium',
      );
    } catch (e) {
      _logger.e('Error updating premium status: $e');
      rethrow;
    }
  }

  /// Delete user account
  Future<void> deleteAccount(String userId, String password) async {
    try {
      if (_currentUser != null && _currentUser!.id == userId) {
        // Delete the current user
        _currentUser = null;
      }
      _logger.i('Mock account deleted for user: $userId');
    } catch (e) {
      _logger.e('Error deleting account: $e');
      rethrow;
    }
  }
}
