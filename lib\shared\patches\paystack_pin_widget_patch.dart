import 'package:flutter/material.dart';
import 'package:fit_4_force/shared/extensions/context_extensions.dart';

/// A patched version of the PinWidget from flutter_paystack
///
/// This is used to fix the compatibility issues with the latest Flutter version
class PinWidget extends StatelessWidget {
  final int count;

  const PinWidget({super.key, required this.count});

  @override
  Widget build(BuildContext context) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.center,
      children: List.generate(
        4, // Fixed pin length
        (i) => Container(
          margin: const EdgeInsets.symmetric(horizontal: 5.0),
          height: 20.0,
          width: 20.0,
          decoration: BoxDecoration(
            color: i < count ? context.colorScheme().primary : null,
            border: Border.all(
              color: context.textTheme().titleMedium?.color ?? Colors.black,
            ),
            borderRadius: BorderRadius.circular(10.0),
          ),
        ),
      ),
    );
  }
}

/// Factory to create a PinWidget with the correct count
/// This is used to patch the flutter_paystack package
class PinWidgetFactory {
  /// Create a PinWidget with the correct count
  static Widget create() {
    return const PinWidget(count: 0);
  }
}
