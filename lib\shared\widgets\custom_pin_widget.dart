import 'package:flutter/material.dart';
import 'package:fit_4_force/shared/extensions/context_extensions.dart';

/// A custom PIN widget that can be used as a replacement for the Paystack PinWidget
class CustomPinWidget extends StatelessWidget {
  final int count;
  final int pinLength;
  final double height;
  final double width;
  final double space;
  final BoxDecoration? decoration;

  const CustomPinWidget({
    super.key,
    required this.count,
    this.pinLength = 4,
    this.height = 20.0,
    this.width = 20.0,
    this.space = 10.0,
    this.decoration,
  });

  @override
  Widget build(BuildContext context) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.center,
      children: List.generate(
        pinLength,
        (i) => Container(
          margin: EdgeInsets.symmetric(horizontal: space / 2),
          height: height,
          width: width,
          decoration: decoration ??
              BoxDecoration(
                color: i < count ? context.colorScheme().primary : null,
                border: Border.all(
                  color: context.textTheme().titleMedium?.color ?? Colors.black,
                ),
                borderRadius: BorderRadius.circular(height / 2),
              ),
        ),
      ),
    );
  }
}
