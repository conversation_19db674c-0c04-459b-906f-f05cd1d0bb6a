// This file contains the fixes needed for the Flutter Paystack package
// to work with newer Flutter versions.

// The following changes need to be made:
// 1. Replace 'bodyText1' with 'bodyLarge'
// 2. Replace 'accentColor' with 'colorScheme.secondary'
// 3. Remove 'vsync: this' parameter from AnimatedSize

// To apply these fixes, you need to modify the following files in the Flutter Paystack package:
// 1. lib/src/widgets/checkout/checkout_widget.dart
// 2. lib/src/widgets/buttons.dart
// 3. Other files that use deprecated APIs

// Example fixes:
// From: color: Theme.of(context).textTheme.bodyText1!.color,
// To:   color: Theme.of(context).textTheme.bodyLarge!.color,

// From: color: Theme.of(context).accentColor,
// To:   color: Theme.of(context).colorScheme.secondary,

// From: AnimatedSize(vsync: this, ...)
// To:   AnimatedSize(...)

// Instructions for manual fixes:
// 1. Find the Flutter Paystack package in your pub cache:
//    - Windows: %LOCALAPPDATA%\Pub\Cache\git\flutter_paystack-a4a33c3dd0a12f46d655a2e63d11e9f20ba82d01
//    - macOS/Linux: ~/.pub-cache/git/flutter_paystack-a4a33c3dd0a12f46d655a2e63d11e9f20ba82d01
// 2. Make a backup of the original files
// 3. Apply the fixes to the files
// 4. Run 'flutter pub get' to update the package
