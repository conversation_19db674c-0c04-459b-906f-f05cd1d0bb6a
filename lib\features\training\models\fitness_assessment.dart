// No imports needed

class FitnessAssessment {
  final String id;
  final String userId;
  final int pushUps;
  final int sitUps;
  final double runTime; // in minutes for 2.4km
  final String militaryAgency;
  final DateTime assessmentDate;
  final double? height; // in cm
  final double? weight; // in kg

  FitnessAssessment({
    required this.id,
    required this.userId,
    required this.pushUps,
    required this.sitUps,
    required this.runTime,
    required this.militaryAgency,
    required this.assessmentDate,
    this.height,
    this.weight,
  });

  // Calculate fitness score based on agency standards
  double calculateScore() {
    // Implementation varies by agency
    switch (militaryAgency) {
      case 'Nigerian Army':
        return (pushUps * 0.4) + (sitUps * 0.4) + ((15 - runTime) * 2);
      case 'Nigerian Navy':
        // Different calculation for Navy
        return (pushUps * 0.3) + (sitUps * 0.3) + ((15 - runTime) * 3);
      case 'Nigerian Air Force':
        return (pushUps * 0.35) + (sitUps * 0.35) + ((15 - runTime) * 2.5);
      case 'Nigerian Police':
        return (pushUps * 0.45) + (sitUps * 0.35) + ((15 - runTime) * 1.5);
      default:
        return (pushUps * 0.4) + (sitUps * 0.4) + ((15 - runTime) * 2);
    }
  }

  // Determine fitness level
  String getFitnessLevel() {
    final score = calculateScore();
    if (score > 80) return 'Advanced';
    if (score > 60) return 'Intermediate';
    return 'Beginner';
  }

  // Calculate BMI if height and weight are available
  double? calculateBMI() {
    if (height == null || weight == null) return null;
    return weight! / ((height! / 100) * (height! / 100));
  }

  // Get BMI category
  String? getBMICategory() {
    final bmi = calculateBMI();
    if (bmi == null) return null;

    if (bmi < 18.5) return 'Underweight';
    if (bmi < 25) return 'Normal';
    if (bmi < 30) return 'Overweight';
    return 'Obese';
  }

  // Convert to JSON for storage
  Map<String, dynamic> toJson() => {
    'id': id,
    'userId': userId,
    'pushUps': pushUps,
    'sitUps': sitUps,
    'runTime': runTime,
    'militaryAgency': militaryAgency,
    'assessmentDate': assessmentDate.toIso8601String(),
    'height': height,
    'weight': weight,
  };

  // Create from JSON
  factory FitnessAssessment.fromJson(Map<String, dynamic> json) =>
      FitnessAssessment(
        id: json['id'],
        userId: json['userId'],
        pushUps: json['pushUps'],
        sitUps: json['sitUps'],
        runTime: json['runTime'],
        militaryAgency: json['militaryAgency'],
        assessmentDate: DateTime.parse(json['assessmentDate']),
        height: json['height'],
        weight: json['weight'],
      );

  // Create from Supabase row
  factory FitnessAssessment.fromSupabase(Map<String, dynamic> data, String id) {
    return FitnessAssessment(
      id: id,
      userId: data['userId'],
      pushUps: data['pushUps'],
      sitUps: data['sitUps'],
      runTime: data['runTime'],
      militaryAgency: data['militaryAgency'],
      assessmentDate: DateTime.parse(data['assessmentDate']),
      height: data['height'],
      weight: data['weight'],
    );
  }
}
