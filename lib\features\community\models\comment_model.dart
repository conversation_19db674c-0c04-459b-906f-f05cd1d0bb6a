import 'package:fit_4_force/shared/models/base_model.dart';

/// Model representing a comment on a post
class CommentModel extends BaseModel {
  final String userId;
  final String userName;
  final String? userProfileImageUrl;
  final String content;
  final int likesCount;
  final bool isLikedByCurrentUser;

  const CommentModel({
    required super.id,
    required super.createdAt,
    super.updatedAt,
    required this.userId,
    required this.userName,
    this.userProfileImageUrl,
    required this.content,
    required this.likesCount,
    required this.isLikedByCurrentUser,
  });

  @override
  List<Object?> get props => [
        ...super.props,
        userId,
        userName,
        userProfileImageUrl,
        content,
        likesCount,
        isLikedByCurrentUser,
      ];

  @override
  Map<String, dynamic> toJson() {
    return {
      ...super.toJson(),
      'userId': userId,
      'userName': userName,
      'userProfileImageUrl': userProfileImageUrl,
      'content': content,
      'likesCount': likesCount,
      'isLikedByCurrentUser': isLikedByCurrentUser,
    };
  }

  factory CommentModel.fromJson(Map<String, dynamic> json) {
    return CommentModel(
      id: json['id'] as String,
      createdAt: DateTime.parse(json['createdAt'] as String),
      updatedAt: json['updatedAt'] != null
          ? DateTime.parse(json['updatedAt'] as String)
          : null,
      userId: json['userId'] as String,
      userName: json['userName'] as String,
      userProfileImageUrl: json['userProfileImageUrl'] as String?,
      content: json['content'] as String,
      likesCount: json['likesCount'] as int,
      isLikedByCurrentUser: json['isLikedByCurrentUser'] as bool,
    );
  }

  @override
  CommentModel copyWith({
    String? id,
    DateTime? createdAt,
    DateTime? updatedAt,
    String? userId,
    String? userName,
    String? userProfileImageUrl,
    String? content,
    int? likesCount,
    bool? isLikedByCurrentUser,
  }) {
    return CommentModel(
      id: id ?? this.id,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      userId: userId ?? this.userId,
      userName: userName ?? this.userName,
      userProfileImageUrl: userProfileImageUrl ?? this.userProfileImageUrl,
      content: content ?? this.content,
      likesCount: likesCount ?? this.likesCount,
      isLikedByCurrentUser: isLikedByCurrentUser ?? this.isLikedByCurrentUser,
    );
  }
}
