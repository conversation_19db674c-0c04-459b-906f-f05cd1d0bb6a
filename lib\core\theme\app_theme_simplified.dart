import 'package:flutter/material.dart';

/// App theme configuration with simplified color scheme
class AppTheme {
  // Primary Colors
  static const Color primaryColor = Color(0xFF6C63FF); // Purple
  static const Color primaryLightColor = Color(0xFF9D97FF);
  static const Color primaryDarkColor = Color(0xFF4A42C8);

  // Secondary Colors
  static const Color secondaryColor = Color(0xFF5E35B1); // Deep Purple
  static const Color secondaryLightColor = Color(0xFF9162E4);
  static const Color secondaryDarkColor = Color(0xFF3B1C80);

  // Accent Colors
  static const Color accentColor = Color(0xFF3F51B5); // Indigo
  static const Color accentLightColor = Color(0xFF757DE8);
  static const Color accentDarkColor = Color(0xFF002984);

  // Background Colors
  static const Color backgroundLight = Color(0xFFF8F9FF); // Light purple tint
  static const Color backgroundDark = Color(0xFF121225); // Dark purple tint

  // Text Colors
  static const Color textPrimaryLight = Color(0xFF333366); // Dark purple text
  static const Color textSecondaryLight = Color(0xFF666699); // Medium purple text
  static const Color textPrimaryDark = Color(0xFFF8F9FF); // Light purple tint
  static const Color textSecondaryDark = Color(0xFFB0B0D0); // Light purple text

  // Status Colors
  static const Color successColor = Color(0xFF4CAF50); // Green
  static const Color errorColor = Color(0xFFE53935); // Red
  static const Color warningColor = Color(0xFFFFC107); // Amber
  static const Color infoColor = Color(0xFF6C63FF); // Purple

  // Premium Colors
  static const Color premiumColor = Color(0xFF9C27B0); // Purple
  static const Color premiumDarkColor = Color(0xFF6A1B9A); // Dark Purple
  
  // Agency Colors
  static final Map<String, Color> agencyColors = {
    'Nigerian Army': Color(0xFF4CAF50), // Green
    'Navy': Color(0xFF1565C0), // Navy Blue
    'Air Force': Color(0xFF3F51B5), // Indigo
    'DSSC': Color(0xFF9C27B0), // Purple
    'NDA': Color(0xFF795548), // Brown
    'NSCDC': Color(0xFF607D8B), // Blue Grey
    'EFCC': Color(0xFFE91E63), // Pink
    'Fire Service': Color(0xFFF44336), // Red
    'Immigration': Color(0xFF009688), // Teal
    'Customs': Color(0xFF673AB7), // Deep Purple
    'FRSC': Color(0xFF2196F3), // Blue
    'Police (POLAC)': Color(0xFF212121), // Dark Grey
  };
  
  // Get a ThemeData object with the app's theme
  static ThemeData getLightTheme() {
    return ThemeData(
      useMaterial3: true,
      colorScheme: ColorScheme.light(
        primary: primaryColor,
        secondary: secondaryColor,
        tertiary: accentColor,
        surface: backgroundLight,
        error: errorColor,
      ),
      scaffoldBackgroundColor: backgroundLight,
      appBarTheme: AppBarTheme(
        backgroundColor: primaryColor,
        foregroundColor: Colors.white,
        elevation: 0,
      ),
    );
  }
  
  // Get a dark ThemeData object with the app's theme
  static ThemeData getDarkTheme() {
    return ThemeData(
      useMaterial3: true,
      colorScheme: ColorScheme.dark(
        primary: primaryColor,
        secondary: secondaryColor,
        tertiary: accentColor,
        surface: backgroundDark,
        error: errorColor,
      ),
      scaffoldBackgroundColor: backgroundDark,
      appBarTheme: AppBarTheme(
        backgroundColor: Colors.black,
        foregroundColor: Colors.white,
        elevation: 0,
      ),
    );
  }
}
