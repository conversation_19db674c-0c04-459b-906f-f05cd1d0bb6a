/* Fit4Force Legal Pages Styles */
:root {
    --primary-color: #2563eb;
    --primary-dark: #1d4ed8;
    --secondary-color: #64748b;
    --background-color: #f8fafc;
    --card-background: #ffffff;
    --text-primary: #1e293b;
    --text-secondary: #64748b;
    --border-color: #e2e8f0;
    --shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
    --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
}

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
    line-height: 1.6;
    color: var(--text-primary);
    background-color: var(--background-color);
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

/* Header */
header {
    background: linear-gradient(135deg, var(--primary-color), var(--primary-dark));
    color: white;
    padding: 2rem 0;
    box-shadow: var(--shadow);
}

.logo h1 {
    font-size: 2.5rem;
    font-weight: 700;
    margin-bottom: 0.5rem;
}

.logo p {
    font-size: 1.1rem;
    opacity: 0.9;
}

/* Main Content */
main {
    padding: 3rem 0;
    min-height: calc(100vh - 200px);
}

.hero {
    text-align: center;
    margin-bottom: 3rem;
}

.hero h2 {
    font-size: 2.5rem;
    font-weight: 600;
    margin-bottom: 1rem;
    color: var(--text-primary);
}

.hero p {
    font-size: 1.2rem;
    color: var(--text-secondary);
    max-width: 600px;
    margin: 0 auto;
}

/* Legal Grid */
.legal-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 2rem;
    margin-top: 2rem;
}

.legal-card {
    background: var(--card-background);
    border-radius: 12px;
    padding: 2rem;
    box-shadow: var(--shadow);
    transition: all 0.3s ease;
    border: 1px solid var(--border-color);
    text-align: center;
}

.legal-card:hover {
    transform: translateY(-4px);
    box-shadow: var(--shadow-lg);
}

.card-icon {
    font-size: 3rem;
    margin-bottom: 1rem;
}

.legal-card h3 {
    font-size: 1.5rem;
    font-weight: 600;
    margin-bottom: 1rem;
    color: var(--text-primary);
}

.legal-card p {
    color: var(--text-secondary);
    margin-bottom: 1.5rem;
    line-height: 1.6;
}

/* Buttons */
.btn-primary {
    display: inline-block;
    background: var(--primary-color);
    color: white;
    padding: 0.75rem 1.5rem;
    border-radius: 8px;
    text-decoration: none;
    font-weight: 500;
    transition: background-color 0.3s ease;
}

.btn-primary:hover {
    background: var(--primary-dark);
}

.btn-secondary {
    display: inline-block;
    background: transparent;
    color: var(--primary-color);
    padding: 0.75rem 1.5rem;
    border: 2px solid var(--primary-color);
    border-radius: 8px;
    text-decoration: none;
    font-weight: 500;
    transition: all 0.3s ease;
}

.btn-secondary:hover {
    background: var(--primary-color);
    color: white;
}

/* Document Pages */
.document-header {
    text-align: center;
    margin-bottom: 3rem;
    padding-bottom: 2rem;
    border-bottom: 2px solid var(--border-color);
}

.document-header h1 {
    font-size: 2.5rem;
    font-weight: 600;
    margin-bottom: 1rem;
    color: var(--text-primary);
}

.document-meta {
    color: var(--text-secondary);
    font-size: 1rem;
}

.document-content {
    background: var(--card-background);
    border-radius: 12px;
    padding: 3rem;
    box-shadow: var(--shadow);
    margin-bottom: 2rem;
}

.document-content h2 {
    font-size: 1.8rem;
    font-weight: 600;
    margin: 2rem 0 1rem 0;
    color: var(--text-primary);
}

.document-content h3 {
    font-size: 1.4rem;
    font-weight: 600;
    margin: 1.5rem 0 0.75rem 0;
    color: var(--text-primary);
}

.document-content p {
    margin-bottom: 1rem;
    line-height: 1.7;
}

.document-content ul, .document-content ol {
    margin: 1rem 0 1rem 2rem;
}

.document-content li {
    margin-bottom: 0.5rem;
    line-height: 1.6;
}

.back-link {
    display: inline-flex;
    align-items: center;
    color: var(--primary-color);
    text-decoration: none;
    font-weight: 500;
    margin-bottom: 2rem;
    transition: color 0.3s ease;
}

.back-link:hover {
    color: var(--primary-dark);
}

.back-link::before {
    content: "←";
    margin-right: 0.5rem;
    font-size: 1.2rem;
}

/* Footer */
footer {
    background: var(--text-primary);
    color: white;
    text-align: center;
    padding: 2rem 0;
    margin-top: 3rem;
}

footer p {
    margin-bottom: 0.5rem;
    opacity: 0.8;
}

/* Responsive Design */
@media (max-width: 768px) {
    .container {
        padding: 0 15px;
    }
    
    .hero h2 {
        font-size: 2rem;
    }
    
    .hero p {
        font-size: 1.1rem;
    }
    
    .legal-grid {
        grid-template-columns: 1fr;
        gap: 1.5rem;
    }
    
    .legal-card {
        padding: 1.5rem;
    }
    
    .document-content {
        padding: 2rem 1.5rem;
    }
    
    .document-header h1 {
        font-size: 2rem;
    }
}

@media (max-width: 480px) {
    .logo h1 {
        font-size: 2rem;
    }
    
    .hero h2 {
        font-size: 1.8rem;
    }
    
    .legal-card {
        padding: 1.25rem;
    }
    
    .document-content {
        padding: 1.5rem 1rem;
    }
}
