import 'package:fit_4_force/core/config/app_config.dart';

/// Enum representing different API environments
enum ApiEnvironment {
  /// Development environment
  development,

  /// Staging environment
  staging,

  /// Production environment
  production,
}

/// API environment configuration
class ApiConfig {
  /// Current API environment
  static const ApiEnvironment currentEnvironment = ApiEnvironment.production;

  /// Get the base URL for the current environment
  static String get baseUrl {
    switch (currentEnvironment) {
      case ApiEnvironment.development:
        return AppConfig.devBaseUrl;
      case ApiEnvironment.staging:
        return AppConfig.stagingBaseUrl;
      case ApiEnvironment.production:
        return AppConfig.baseUrl;
    }
  }

  /// Get the headers for API requests
  static Map<String, String> get headers => AppConfig.defaultHeaders;

  /// Get the timeout duration for API requests
  static int get timeout => AppConfig.apiTimeout;

  /// Check if the current environment is production
  static bool get isProduction => currentEnvironment == ApiEnvironment.production;

  /// Check if the current environment is development
  static bool get isDevelopment => currentEnvironment == ApiEnvironment.development;

  /// Check if the current environment is staging
  static bool get isStaging => currentEnvironment == ApiEnvironment.staging;
}
