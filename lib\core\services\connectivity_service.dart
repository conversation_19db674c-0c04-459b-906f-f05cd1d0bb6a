import 'dart:async';
import 'package:connectivity_plus/connectivity_plus.dart';

/// Service to check network connectivity
class ConnectivityService {
  /// Connectivity instance
  final Connectivity _connectivity = Connectivity();

  /// Stream controller for connectivity status
  final StreamController<bool> _connectionStatusController =
      StreamController<bool>.broadcast();

  /// Stream of connectivity status
  Stream<bool> get connectionStatus => _connectionStatusController.stream;

  /// Constructor
  ConnectivityService() {
    // Initialize connectivity monitoring
    _initConnectivity();
    // Listen for connectivity changes
    _connectivity.onConnectivityChanged.listen(_updateConnectionStatus);
  }

  /// Initialize connectivity
  Future<void> _initConnectivity() async {
    try {
      final status = await _connectivity.checkConnectivity();
      _updateConnectionStatus(status);
    } catch (e) {
      _connectionStatusController.add(false);
    }
  }

  /// Update connection status
  void _updateConnectionStatus(ConnectivityResult result) {
    final isConnected = result != ConnectivityResult.none;
    _connectionStatusController.add(isConnected);
  }

  /// Check if device is connected to the internet
  Future<bool> isConnected() async {
    final result = await _connectivity.checkConnectivity();
    return result != ConnectivityResult.none;
  }

  /// Dispose resources
  void dispose() {
    _connectionStatusController.close();
  }
}
