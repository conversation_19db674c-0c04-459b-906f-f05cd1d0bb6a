import 'package:flutter/material.dart';

/// Extensions on Color class to provide additional functionality
extension ColorExtensions on Color {
  /// Create a new color with the specified alpha value
  /// This is a more precise alternative to withOpacity() which can lose precision
  /// @param alpha The alpha value (0-255)
  Color withValues({int? red, int? green, int? blue, double? alpha}) {
    return Color.fromARGB(
      alpha != null ? alpha.toInt() : a.toInt(),
      red ?? r.toInt(),
      green ?? g.toInt(),
      blue ?? b.toInt(),
    );
  }
}
