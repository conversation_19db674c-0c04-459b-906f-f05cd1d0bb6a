import 'package:flutter/material.dart';
import 'package:fit_4_force/features/prep/models/progress_model.dart';

class ProgressService {
  // Singleton pattern
  static final ProgressService _instance = ProgressService._internal();
  
  factory ProgressService() {
    return _instance;
  }
  
  ProgressService._internal();
  
  // Sample progress data
  final List<StudyProgressModel> _progressEntries = [
    // Today
    StudyProgressModel(
      id: '1',
      userId: 'user1',
      category: 'General Knowledge',
      date: DateTime.now(),
      studyMinutes: 45,
      flashcardsReviewed: 20,
      quizzesTaken: 1,
      quizAvgScore: 85.0,
      color: Colors.blue,
    ),
    
    // Yesterday
    StudyProgressModel(
      id: '2',
      userId: 'user1',
      category: 'Mathematics',
      date: DateTime.now().subtract(const Duration(days: 1)),
      studyMinutes: 30,
      flashcardsReviewed: 15,
      quizzesTaken: 0,
      quizAvgScore: 0.0,
      color: Colors.green,
    ),
    
    // 2 days ago
    StudyProgressModel(
      id: '3',
      userId: 'user1',
      category: 'English',
      date: DateTime.now().subtract(const Duration(days: 2)),
      studyMinutes: 60,
      flashcardsReviewed: 25,
      quizzesTaken: 2,
      quizAvgScore: 92.0,
      color: Colors.purple,
    ),
    
    // 3 days ago
    StudyProgressModel(
      id: '4',
      userId: 'user1',
      category: 'Current Affairs',
      date: DateTime.now().subtract(const Duration(days: 3)),
      studyMinutes: 40,
      flashcardsReviewed: 10,
      quizzesTaken: 1,
      quizAvgScore: 78.0,
      color: Colors.orange,
    ),
    
    // 5 days ago
    StudyProgressModel(
      id: '5',
      userId: 'user1',
      category: 'Physical Training',
      date: DateTime.now().subtract(const Duration(days: 5)),
      studyMinutes: 50,
      flashcardsReviewed: 0,
      quizzesTaken: 0,
      quizAvgScore: 0.0,
      color: Colors.red,
    ),
    
    // 6 days ago
    StudyProgressModel(
      id: '6',
      userId: 'user1',
      category: 'Agency Specific',
      date: DateTime.now().subtract(const Duration(days: 6)),
      studyMinutes: 35,
      flashcardsReviewed: 18,
      quizzesTaken: 1,
      quizAvgScore: 90.0,
      color: Colors.indigo,
    ),
    
    // Last week
    StudyProgressModel(
      id: '7',
      userId: 'user1',
      category: 'General Knowledge',
      date: DateTime.now().subtract(const Duration(days: 8)),
      studyMinutes: 55,
      flashcardsReviewed: 22,
      quizzesTaken: 1,
      quizAvgScore: 88.0,
      color: Colors.blue,
    ),
    
    StudyProgressModel(
      id: '8',
      userId: 'user1',
      category: 'Mathematics',
      date: DateTime.now().subtract(const Duration(days: 9)),
      studyMinutes: 40,
      flashcardsReviewed: 15,
      quizzesTaken: 1,
      quizAvgScore: 75.0,
      color: Colors.green,
    ),
    
    StudyProgressModel(
      id: '9',
      userId: 'user1',
      category: 'English',
      date: DateTime.now().subtract(const Duration(days: 10)),
      studyMinutes: 30,
      flashcardsReviewed: 10,
      quizzesTaken: 0,
      quizAvgScore: 0.0,
      color: Colors.purple,
    ),
    
    StudyProgressModel(
      id: '10',
      userId: 'user1',
      category: 'Current Affairs',
      date: DateTime.now().subtract(const Duration(days: 12)),
      studyMinutes: 45,
      flashcardsReviewed: 20,
      quizzesTaken: 1,
      quizAvgScore: 82.0,
      color: Colors.orange,
    ),
  ];
  
  // Sample study goals
  final List<StudyGoalModel> _studyGoals = [
    StudyGoalModel(
      id: '1',
      userId: 'user1',
      title: 'Master Mathematics',
      description: 'Complete all math flashcards and achieve 90% quiz score',
      createdAt: DateTime.now().subtract(const Duration(days: 15)),
      targetDate: DateTime.now().add(const Duration(days: 7)),
      targetMinutes: 300,
      targetFlashcards: 100,
      targetQuizzes: 5,
      targetQuizScore: 90.0,
      currentMinutes: 120,
      currentFlashcards: 45,
      currentQuizzes: 2,
      currentQuizScore: 82.5,
      isCompleted: false,
      color: Colors.green,
    ),
    
    StudyGoalModel(
      id: '2',
      userId: 'user1',
      title: 'Current Affairs Update',
      description: 'Stay updated with latest current affairs for the exam',
      createdAt: DateTime.now().subtract(const Duration(days: 10)),
      targetDate: DateTime.now().add(const Duration(days: 14)),
      targetMinutes: 240,
      targetFlashcards: 50,
      targetQuizzes: 3,
      targetQuizScore: 85.0,
      currentMinutes: 85,
      currentFlashcards: 30,
      currentQuizzes: 1,
      currentQuizScore: 78.0,
      isCompleted: false,
      color: Colors.orange,
    ),
    
    StudyGoalModel(
      id: '3',
      userId: 'user1',
      title: 'English Vocabulary',
      description: 'Improve vocabulary for verbal section',
      createdAt: DateTime.now().subtract(const Duration(days: 20)),
      targetDate: DateTime.now().subtract(const Duration(days: 2)),
      targetMinutes: 180,
      targetFlashcards: 80,
      targetQuizzes: 4,
      targetQuizScore: 80.0,
      currentMinutes: 180,
      currentFlashcards: 80,
      currentQuizzes: 4,
      currentQuizScore: 92.0,
      isCompleted: true,
      color: Colors.purple,
    ),
  ];
  
  // Get all progress entries for a user
  List<StudyProgressModel> getProgressEntries(String userId) {
    return _progressEntries.where((entry) => entry.userId == userId).toList();
  }
  
  // Get progress entries for a specific date range
  List<StudyProgressModel> getProgressForDateRange(
    String userId,
    DateTime startDate,
    DateTime endDate,
  ) {
    return _progressEntries.where((entry) {
      return entry.userId == userId &&
          entry.date.isAfter(startDate) &&
          entry.date.isBefore(endDate.add(const Duration(days: 1)));
    }).toList();
  }
  
  // Get progress entries for today
  List<StudyProgressModel> getTodayProgress(String userId) {
    final now = DateTime.now();
    final today = DateTime(now.year, now.month, now.day);
    final tomorrow = today.add(const Duration(days: 1));
    
    return getProgressForDateRange(userId, today, today);
  }
  
  // Get progress entries for this week
  List<StudyProgressModel> getThisWeekProgress(String userId) {
    final now = DateTime.now();
    final today = DateTime(now.year, now.month, now.day);
    
    // Find the start of the week (Sunday)
    final startOfWeek = today.subtract(Duration(days: today.weekday % 7));
    
    // Find the end of the week (Saturday)
    final endOfWeek = startOfWeek.add(const Duration(days: 6));
    
    return getProgressForDateRange(userId, startOfWeek, endOfWeek);
  }
  
  // Get weekly progress summary
  List<WeeklyProgressModel> getWeeklyProgressSummary(String userId, {int weeksCount = 4}) {
    final now = DateTime.now();
    final today = DateTime(now.year, now.month, now.day);
    
    // Find the start of the current week (Sunday)
    final currentWeekStart = today.subtract(Duration(days: today.weekday % 7));
    
    List<WeeklyProgressModel> weeklyProgress = [];
    
    for (int i = 0; i < weeksCount; i++) {
      final weekStart = currentWeekStart.subtract(Duration(days: i * 7));
      final weekEnd = weekStart.add(const Duration(days: 6));
      
      final weekEntries = getProgressForDateRange(userId, weekStart, weekEnd);
      
      // Calculate totals
      int totalMinutes = 0;
      int totalFlashcards = 0;
      int totalQuizzes = 0;
      double totalQuizScore = 0;
      int totalPoints = 0;
      Map<String, int> minutesByCategory = {};
      
      for (var entry in weekEntries) {
        totalMinutes += entry.studyMinutes;
        totalFlashcards += entry.flashcardsReviewed;
        totalQuizzes += entry.quizzesTaken;
        totalQuizScore += entry.quizAvgScore * entry.quizzesTaken;
        totalPoints += entry.totalPoints;
        
        // Add minutes by category
        if (minutesByCategory.containsKey(entry.category)) {
          minutesByCategory[entry.category] = 
              minutesByCategory[entry.category]! + entry.studyMinutes;
        } else {
          minutesByCategory[entry.category] = entry.studyMinutes;
        }
      }
      
      // Calculate average quiz score
      final avgQuizScore = totalQuizzes > 0 
          ? totalQuizScore / totalQuizzes 
          : 0.0;
      
      weeklyProgress.add(
        WeeklyProgressModel(
          weekStart: weekStart,
          weekEnd: weekEnd,
          minutesByCategory: minutesByCategory,
          totalMinutes: totalMinutes,
          totalFlashcards: totalFlashcards,
          totalQuizzes: totalQuizzes,
          avgQuizScore: avgQuizScore,
          totalPoints: totalPoints,
          isCurrentWeek: i == 0,
        ),
      );
    }
    
    return weeklyProgress;
  }
  
  // Get study streak information
  StudyStreakModel getStudyStreak(String userId) {
    // Get all study dates
    final studyDates = _progressEntries
        .where((entry) => entry.userId == userId)
        .map((entry) => DateTime(
              entry.date.year,
              entry.date.month,
              entry.date.day,
            ))
        .toSet()
        .toList();
    
    // Sort dates
    studyDates.sort((a, b) => a.compareTo(b));
    
    if (studyDates.isEmpty) {
      return StudyStreakModel(
        currentStreak: 0,
        longestStreak: 0,
        lastStudyDate: DateTime.now().subtract(const Duration(days: 30)),
        studyDates: [],
      );
    }
    
    // Calculate current streak
    int currentStreak = 0;
    final now = DateTime.now();
    final today = DateTime(now.year, now.month, now.day);
    DateTime checkDate = today;
    
    while (studyDates.contains(checkDate)) {
      currentStreak++;
      checkDate = checkDate.subtract(const Duration(days: 1));
    }
    
    // Calculate longest streak
    int longestStreak = 0;
    int tempStreak = 1;
    
    for (int i = 1; i < studyDates.length; i++) {
      final difference = studyDates[i].difference(studyDates[i - 1]).inDays;
      
      if (difference == 1) {
        tempStreak++;
      } else {
        longestStreak = tempStreak > longestStreak ? tempStreak : longestStreak;
        tempStreak = 1;
      }
    }
    
    longestStreak = tempStreak > longestStreak ? tempStreak : longestStreak;
    
    return StudyStreakModel(
      currentStreak: currentStreak,
      longestStreak: longestStreak,
      lastStudyDate: studyDates.last,
      studyDates: studyDates,
    );
  }
  
  // Get all study goals for a user
  List<StudyGoalModel> getStudyGoals(String userId) {
    return _studyGoals.where((goal) => goal.userId == userId).toList();
  }
  
  // Get active (not completed) study goals
  List<StudyGoalModel> getActiveGoals(String userId) {
    return _studyGoals.where((goal) => 
        goal.userId == userId && !goal.isCompleted).toList();
  }
  
  // Add a new progress entry
  void addProgressEntry(StudyProgressModel entry) {
    _progressEntries.add(entry);
  }
  
  // Add a new study goal
  void addStudyGoal(StudyGoalModel goal) {
    _studyGoals.add(goal);
  }
  
  // Update a study goal
  void updateStudyGoal(StudyGoalModel goal) {
    final index = _studyGoals.indexWhere((g) => g.id == goal.id);
    if (index != -1) {
      _studyGoals[index] = goal;
    }
  }
  
  // Mark a goal as completed
  void completeGoal(String goalId) {
    final index = _studyGoals.indexWhere((g) => g.id == goalId);
    if (index != -1) {
      final goal = _studyGoals[index];
      _studyGoals[index] = StudyGoalModel(
        id: goal.id,
        userId: goal.userId,
        title: goal.title,
        description: goal.description,
        createdAt: goal.createdAt,
        targetDate: goal.targetDate,
        targetMinutes: goal.targetMinutes,
        targetFlashcards: goal.targetFlashcards,
        targetQuizzes: goal.targetQuizzes,
        targetQuizScore: goal.targetQuizScore,
        currentMinutes: goal.currentMinutes,
        currentFlashcards: goal.currentFlashcards,
        currentQuizzes: goal.currentQuizzes,
        currentQuizScore: goal.currentQuizScore,
        isCompleted: true,
        color: goal.color,
      );
    }
  }
}
