import 'package:get_it/get_it.dart';
import 'package:fit_4_force/core/services/supabase_auth_service.dart';
import 'package:fit_4_force/shared/services/supabase_auth_service.dart'
    as shared;
import 'package:fit_4_force/core/services/user_storage_service.dart';
import 'package:fit_4_force/core/services/notification_service.dart';
import 'package:fit_4_force/core/services/realtime_service.dart';
import 'package:fit_4_force/core/services/error_handling_service.dart';
import 'package:fit_4_force/core/services/offline_service.dart';
import 'package:fit_4_force/core/auth/enhanced_auth_service.dart';
import 'package:fit_4_force/core/services/file_upload_service.dart';
import 'package:fit_4_force/core/services/user_rating_service.dart';
import 'package:fit_4_force/features/prep/services/enhanced_study_material_service.dart';

/// Service locator for dependency injection
final GetIt getIt = GetIt.instance;

/// Setup all services for dependency injection
Future<void> setupServiceLocator() async {
  // Core services
  getIt.registerLazySingleton<ErrorHandlingService>(
    () => ErrorHandlingService(),
  );
  getIt.registerLazySingleton<OfflineService>(() => OfflineService());

  // Auth services
  getIt.registerLazySingleton<SupabaseAuthService>(() => SupabaseAuthService());
  getIt.registerLazySingleton<shared.SupabaseAuthService>(
    () => shared.SupabaseAuthService(),
  );
  getIt.registerLazySingleton<EnhancedAuthService>(() => EnhancedAuthService());

  // Storage and data services
  getIt.registerLazySingleton<UserStorageService>(() => UserStorageService());
  getIt.registerLazySingleton<FileUploadService>(() => FileUploadService());
  getIt.registerLazySingleton<UserRatingService>(() => UserRatingService());

  // Feature services
  getIt.registerLazySingleton<EnhancedStudyMaterialService>(
    () => EnhancedStudyMaterialService(),
  );

  // Communication services
  getIt.registerLazySingleton<NotificationService>(() => NotificationService());
  getIt.registerLazySingleton<RealtimeService>(() => RealtimeService());

  // Initialize services that need setup
  await getIt<OfflineService>().initialize();
}

/// Reset service locator (useful for testing)
Future<void> resetServiceLocator() async {
  await getIt.reset();
}

/// Get service instance
T getService<T extends Object>() => getIt<T>();
