import 'package:fit_4_force/shared/models/base_model.dart';
import 'package:fit_4_force/shared/models/user_model.dart';

/// Model representing a study group
class StudyGroupModel extends BaseModel {
  final String name;
  final String description;
  final String creatorId;
  final String creatorName;
  final String? creatorProfileImageUrl;
  final String agency;
  final List<String> topics;
  final int membersCount;
  final int maxMembers;
  final bool isPrivate;
  final List<UserModel>? members;
  final String? meetingLink;
  final DateTime? nextMeetingTime;
  final List<String>? resources;

  const StudyGroupModel({
    required super.id,
    required super.createdAt,
    super.updatedAt,
    required this.name,
    required this.description,
    required this.creatorId,
    required this.creatorName,
    this.creatorProfileImageUrl,
    required this.agency,
    required this.topics,
    required this.membersCount,
    required this.maxMembers,
    required this.isPrivate,
    this.members,
    this.meetingLink,
    this.nextMeetingTime,
    this.resources,
  });

  @override
  List<Object?> get props => [
        ...super.props,
        name,
        description,
        creatorId,
        creatorName,
        creatorProfileImageUrl,
        agency,
        topics,
        membersCount,
        maxMembers,
        isPrivate,
        members,
        meetingLink,
        nextMeetingTime,
        resources,
      ];

  @override
  StudyGroupModel copyWith({
    String? id,
    DateTime? createdAt,
    DateTime? updatedAt,
    String? name,
    String? description,
    String? creatorId,
    String? creatorName,
    String? creatorProfileImageUrl,
    String? agency,
    List<String>? topics,
    int? membersCount,
    int? maxMembers,
    bool? isPrivate,
    List<UserModel>? members,
    String? meetingLink,
    DateTime? nextMeetingTime,
    List<String>? resources,
  }) {
    return StudyGroupModel(
      id: id ?? this.id,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      name: name ?? this.name,
      description: description ?? this.description,
      creatorId: creatorId ?? this.creatorId,
      creatorName: creatorName ?? this.creatorName,
      creatorProfileImageUrl: creatorProfileImageUrl ?? this.creatorProfileImageUrl,
      agency: agency ?? this.agency,
      topics: topics ?? this.topics,
      membersCount: membersCount ?? this.membersCount,
      maxMembers: maxMembers ?? this.maxMembers,
      isPrivate: isPrivate ?? this.isPrivate,
      members: members ?? this.members,
      meetingLink: meetingLink ?? this.meetingLink,
      nextMeetingTime: nextMeetingTime ?? this.nextMeetingTime,
      resources: resources ?? this.resources,
    );
  }
}
