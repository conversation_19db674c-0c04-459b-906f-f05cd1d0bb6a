import 'package:flutter/material.dart';
import 'package:fit_4_force/core/theme/app_theme.dart';
import 'package:fit_4_force/features/prep/models/flashcard_model.dart';
import 'package:fit_4_force/features/prep/services/flashcard_service.dart';
import 'package:fit_4_force/features/prep/screens/flashcard_study_screen.dart';
import 'package:fit_4_force/shared/models/user_model.dart';
import 'package:fit_4_force/shared/widgets/premium_badge.dart';

class FlashcardsScreen extends StatefulWidget {
  final UserModel user;

  const FlashcardsScreen({super.key, required this.user});

  @override
  State<FlashcardsScreen> createState() => _FlashcardsScreenState();
}

class _FlashcardsScreenState extends State<FlashcardsScreen>
    with SingleTickerProviderStateMixin {
  final FlashcardService _flashcardService = FlashcardService();
  late TabController _tabController;
  late List<Map<String, dynamic>> _categories;
  late List<FlashcardModel> _allFlashcards;
  late List<FlashcardModel> _dueFlashcards;
  String _selectedCategory = 'All';
  bool _isSearching = false;
  final TextEditingController _searchController = TextEditingController();
  List<FlashcardModel> _filteredFlashcards = [];

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 2, vsync: this);
    _categories = _flashcardService.getAllCategories();
    _allFlashcards = _flashcardService.getAllFlashcards();
    _dueFlashcards = _flashcardService.getFlashcardsDueForReview();
    _filteredFlashcards = _allFlashcards;

    _searchController.addListener(() {
      _filterFlashcards(_searchController.text);
    });
  }

  @override
  void dispose() {
    _tabController.dispose();
    _searchController.dispose();
    super.dispose();
  }

  void _filterFlashcards(String query) {
    setState(() {
      if (query.isEmpty) {
        _filteredFlashcards =
            _selectedCategory == 'All'
                ? _allFlashcards
                : _flashcardService.getFlashcardsByCategory(_selectedCategory);
      } else {
        final baseList =
            _selectedCategory == 'All'
                ? _allFlashcards
                : _flashcardService.getFlashcardsByCategory(_selectedCategory);

        _filteredFlashcards =
            baseList.where((card) {
              return card.question.toLowerCase().contains(
                    query.toLowerCase(),
                  ) ||
                  card.answer.toLowerCase().contains(query.toLowerCase()) ||
                  card.tags.any(
                    (tag) => tag.toLowerCase().contains(query.toLowerCase()),
                  );
            }).toList();
      }
    });
  }

  void _selectCategory(String category) {
    setState(() {
      _selectedCategory = category;
      _filterFlashcards(_searchController.text);
    });
  }

  void _showAddFlashcardDialog() {
    final questionController = TextEditingController();
    final answerController = TextEditingController();
    String selectedCategory = _categories[0]['name'] as String;

    showDialog(
      context: context,
      builder: (context) {
        return AlertDialog(
          title: const Text('Create Flashcard'),
          content: SingleChildScrollView(
            child: Column(
              mainAxisSize: MainAxisSize.min,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                TextField(
                  controller: questionController,
                  decoration: const InputDecoration(
                    labelText: 'Question',
                    hintText: 'Enter your question...',
                  ),
                  maxLength: 200,
                  maxLines: 3,
                ),
                const SizedBox(height: 16),
                TextField(
                  controller: answerController,
                  decoration: const InputDecoration(
                    labelText: 'Answer',
                    hintText: 'Enter the answer...',
                    alignLabelWithHint: true,
                  ),
                  maxLength: 500,
                  maxLines: 5,
                ),
                const SizedBox(height: 16),
                DropdownButtonFormField<String>(
                  value: selectedCategory,
                  decoration: const InputDecoration(labelText: 'Category'),
                  items:
                      _categories.map((category) {
                        return DropdownMenuItem<String>(
                          value: category['name'] as String,
                          child: Text(category['name'] as String),
                        );
                      }).toList(),
                  onChanged: (value) {
                    if (value != null) {
                      selectedCategory = value;
                    }
                  },
                ),
              ],
            ),
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.pop(context),
              child: const Text('CANCEL'),
            ),
            TextButton(
              onPressed: () {
                if (questionController.text.isNotEmpty &&
                    answerController.text.isNotEmpty) {
                  // Get color for selected category
                  final categoryData = _categories.firstWhere(
                    (cat) => cat['name'] == selectedCategory,
                    orElse: () => _categories[0],
                  );

                  // Create new flashcard
                  final newCard = FlashcardModel(
                    id: DateTime.now().millisecondsSinceEpoch.toString(),
                    question: questionController.text,
                    answer: answerController.text,
                    category: selectedCategory,
                    color: categoryData['color'] as Color,
                    createdAt: DateTime.now(),
                    tags: [],
                  );

                  // Add to service
                  _flashcardService.addFlashcard(newCard);

                  // Update state
                  setState(() {
                    _allFlashcards = _flashcardService.getAllFlashcards();
                    _filterFlashcards(_searchController.text);
                  });

                  Navigator.pop(context);
                }
              },
              child: const Text('CREATE'),
            ),
          ],
        );
      },
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title:
            _isSearching
                ? TextField(
                  controller: _searchController,
                  decoration: const InputDecoration(
                    hintText: 'Search flashcards...',
                    border: InputBorder.none,
                  ),
                  autofocus: true,
                )
                : const Text(
                  'Flashcards',
                  style: TextStyle(fontWeight: FontWeight.bold),
                ),
        actions: [
          IconButton(
            icon: Icon(_isSearching ? Icons.close : Icons.search),
            onPressed: () {
              setState(() {
                _isSearching = !_isSearching;
                if (!_isSearching) {
                  _searchController.clear();
                }
              });
            },
          ),
          IconButton(
            icon: const Icon(Icons.add),
            onPressed: _showAddFlashcardDialog,
          ),
        ],
        bottom: TabBar(
          controller: _tabController,
          tabs: const [Tab(text: 'All Cards'), Tab(text: 'Due for Review')],
        ),
      ),
      body: Column(
        children: [
          _buildCategoryFilter(),
          Expanded(
            child: TabBarView(
              controller: _tabController,
              children: [
                _buildFlashcardsList(_filteredFlashcards),
                _buildFlashcardsList(_dueFlashcards),
              ],
            ),
          ),
        ],
      ),
      floatingActionButton: FloatingActionButton.extended(
        onPressed: () {
          Navigator.push(
            context,
            MaterialPageRoute(
              builder:
                  (context) => FlashcardStudyScreen(
                    flashcards:
                        _tabController.index == 0
                            ? _filteredFlashcards
                            : _dueFlashcards,
                    onUpdateConfidence: (String id, int level) {
                      _flashcardService.updateConfidenceLevel(id, level);
                      setState(() {
                        _allFlashcards = _flashcardService.getAllFlashcards();
                        _dueFlashcards =
                            _flashcardService.getFlashcardsDueForReview();
                        _filterFlashcards(_searchController.text);
                      });
                    },
                  ),
            ),
          );
        },
        icon: const Icon(Icons.play_arrow),
        label: const Text('Start Study Session'),
        backgroundColor: AppTheme.primaryColor,
      ),
    );
  }

  Widget _buildCategoryFilter() {
    return Container(
      height: 60,
      padding: const EdgeInsets.symmetric(vertical: 8),
      child: ListView(
        scrollDirection: Axis.horizontal,
        padding: const EdgeInsets.symmetric(horizontal: 16),
        children: [
          // All category
          _buildCategoryChip('All', null),
          // Other categories
          ..._categories.map((category) {
            return _buildCategoryChip(
              category['name'] as String,
              category['color'] as Color,
            );
          }),
        ],
      ),
    );
  }

  Widget _buildCategoryChip(String name, Color? color) {
    final isSelected = _selectedCategory == name;

    return Padding(
      padding: const EdgeInsets.only(right: 8),
      child: FilterChip(
        selected: isSelected,
        label: Text(name),
        labelStyle: TextStyle(
          color: isSelected ? Colors.white : Colors.black87,
          fontWeight: isSelected ? FontWeight.bold : FontWeight.normal,
        ),
        backgroundColor: Colors.grey.shade200,
        selectedColor: color ?? AppTheme.primaryColor,
        onSelected: (selected) {
          _selectCategory(name);
        },
      ),
    );
  }

  Widget _buildFlashcardsList(List<FlashcardModel> flashcards) {
    if (flashcards.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.note_alt_outlined,
              size: 80,
              color: Colors.grey.shade300,
            ),
            const SizedBox(height: 16),
            Text(
              'No flashcards found',
              style: TextStyle(
                fontSize: 18,
                color: Colors.grey.shade600,
                fontWeight: FontWeight.w500,
              ),
            ),
            const SizedBox(height: 8),
            Text(
              'Create new flashcards to start studying',
              style: TextStyle(fontSize: 14, color: Colors.grey.shade500),
            ),
          ],
        ),
      );
    }

    return ListView.builder(
      padding: const EdgeInsets.all(16),
      itemCount: flashcards.length,
      itemBuilder: (context, index) {
        final flashcard = flashcards[index];
        return _buildFlashcardItem(flashcard);
      },
    );
  }

  Widget _buildFlashcardItem(FlashcardModel flashcard) {
    return Card(
      margin: const EdgeInsets.only(bottom: 16),
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(16),
        side: BorderSide(
          color: flashcard.color.withValues(alpha: 0.3 * 255),
          width: 1,
        ),
      ),
      child: InkWell(
        onTap: () {
          _showFlashcardDetailDialog(flashcard);
        },
        borderRadius: BorderRadius.circular(16),
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Container(
                    padding: const EdgeInsets.all(8),
                    decoration: BoxDecoration(
                      color: flashcard.color.withValues(alpha: 0.1 * 255),
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: Icon(
                      _getCategoryIcon(flashcard.category),
                      color: flashcard.color,
                      size: 20,
                    ),
                  ),
                  const SizedBox(width: 12),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          flashcard.question,
                          style: const TextStyle(
                            fontWeight: FontWeight.bold,
                            fontSize: 16,
                          ),
                        ),
                        const SizedBox(height: 4),
                        Text(
                          flashcard.category,
                          style: TextStyle(
                            color: flashcard.color,
                            fontSize: 12,
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                      ],
                    ),
                  ),
                  if (flashcard.isPremium) const PremiumBadge(),
                ],
              ),
              if (flashcard.tags.isNotEmpty) ...[
                const SizedBox(height: 12),
                Wrap(
                  spacing: 8,
                  runSpacing: 8,
                  children:
                      flashcard.tags.map((tag) {
                        return Container(
                          padding: const EdgeInsets.symmetric(
                            horizontal: 8,
                            vertical: 4,
                          ),
                          decoration: BoxDecoration(
                            color: Colors.grey.shade200,
                            borderRadius: BorderRadius.circular(12),
                          ),
                          child: Text(
                            '#$tag',
                            style: TextStyle(
                              fontSize: 12,
                              color: Colors.grey.shade700,
                            ),
                          ),
                        );
                      }).toList(),
                ),
              ],
              const SizedBox(height: 12),
              Row(
                children: [
                  Icon(
                    Icons.access_time,
                    size: 14,
                    color: Colors.grey.shade600,
                  ),
                  const SizedBox(width: 4),
                  Text(
                    'Last reviewed: ${flashcard.lastReviewed != null ? _formatDate(flashcard.lastReviewed!) : 'Never'}',
                    style: TextStyle(fontSize: 12, color: Colors.grey.shade600),
                  ),
                  const Spacer(),
                  _buildConfidenceIndicator(flashcard.confidenceLevel),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildConfidenceIndicator(int level) {
    final colors = [
      Colors.red,
      Colors.orange,
      Colors.yellow,
      Colors.lightGreen,
      Colors.green,
    ];

    return Row(
      children: [
        Text(
          'Confidence: ',
          style: TextStyle(fontSize: 12, color: Colors.grey.shade600),
        ),
        ...List.generate(5, (index) {
          return Container(
            width: 8,
            height: 8,
            margin: const EdgeInsets.only(right: 2),
            decoration: BoxDecoration(
              shape: BoxShape.circle,
              color: index < level ? colors[index] : Colors.grey.shade300,
            ),
          );
        }),
      ],
    );
  }

  void _showFlashcardDetailDialog(FlashcardModel flashcard) {
    showDialog(
      context: context,
      builder: (context) {
        return AlertDialog(
          title: Row(
            children: [
              Expanded(
                child: Text(
                  'Flashcard Details',
                  style: TextStyle(color: flashcard.color),
                ),
              ),
              if (flashcard.isPremium) const PremiumBadge(),
            ],
          ),
          content: SingleChildScrollView(
            child: Column(
              mainAxisSize: MainAxisSize.min,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                const Text(
                  'Question:',
                  style: TextStyle(fontWeight: FontWeight.bold, fontSize: 16),
                ),
                const SizedBox(height: 8),
                Text(flashcard.question),
                const SizedBox(height: 16),
                const Text(
                  'Answer:',
                  style: TextStyle(fontWeight: FontWeight.bold, fontSize: 16),
                ),
                const SizedBox(height: 8),
                Text(flashcard.answer),
                const SizedBox(height: 16),
                Row(
                  children: [
                    const Text(
                      'Category: ',
                      style: TextStyle(fontWeight: FontWeight.bold),
                    ),
                    Text(flashcard.category),
                  ],
                ),
                const SizedBox(height: 8),
                Row(
                  children: [
                    const Text(
                      'Created: ',
                      style: TextStyle(fontWeight: FontWeight.bold),
                    ),
                    Text(_formatDate(flashcard.createdAt)),
                  ],
                ),
                if (flashcard.lastReviewed != null) ...[
                  const SizedBox(height: 8),
                  Row(
                    children: [
                      const Text(
                        'Last Reviewed: ',
                        style: TextStyle(fontWeight: FontWeight.bold),
                      ),
                      Text(_formatDate(flashcard.lastReviewed!)),
                    ],
                  ),
                ],
                const SizedBox(height: 8),
                Row(
                  children: [
                    const Text(
                      'Confidence Level: ',
                      style: TextStyle(fontWeight: FontWeight.bold),
                    ),
                    _buildConfidenceIndicator(flashcard.confidenceLevel),
                  ],
                ),
                if (flashcard.tags.isNotEmpty) ...[
                  const SizedBox(height: 16),
                  const Text(
                    'Tags:',
                    style: TextStyle(fontWeight: FontWeight.bold),
                  ),
                  const SizedBox(height: 8),
                  Wrap(
                    spacing: 8,
                    runSpacing: 8,
                    children:
                        flashcard.tags.map((tag) {
                          return Container(
                            padding: const EdgeInsets.symmetric(
                              horizontal: 8,
                              vertical: 4,
                            ),
                            decoration: BoxDecoration(
                              color: Colors.grey.shade200,
                              borderRadius: BorderRadius.circular(12),
                            ),
                            child: Text(
                              '#$tag',
                              style: TextStyle(
                                fontSize: 12,
                                color: Colors.grey.shade700,
                              ),
                            ),
                          );
                        }).toList(),
                  ),
                ],
              ],
            ),
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.pop(context),
              child: const Text('CLOSE'),
            ),
            TextButton(
              onPressed: () {
                // Delete flashcard
                _flashcardService.deleteFlashcard(flashcard.id);
                setState(() {
                  _allFlashcards = _flashcardService.getAllFlashcards();
                  _dueFlashcards =
                      _flashcardService.getFlashcardsDueForReview();
                  _filterFlashcards(_searchController.text);
                });
                Navigator.pop(context);
              },
              child: const Text('DELETE'),
            ),
            TextButton(
              onPressed: () {
                Navigator.pop(context);
                // Navigate to study screen with just this card
                Navigator.push(
                  context,
                  MaterialPageRoute(
                    builder:
                        (context) => FlashcardStudyScreen(
                          flashcards: [flashcard],
                          onUpdateConfidence: (String id, int level) {
                            _flashcardService.updateConfidenceLevel(id, level);
                            setState(() {
                              _allFlashcards =
                                  _flashcardService.getAllFlashcards();
                              _dueFlashcards =
                                  _flashcardService.getFlashcardsDueForReview();
                              _filterFlashcards(_searchController.text);
                            });
                          },
                        ),
                  ),
                );
              },
              child: const Text('STUDY'),
            ),
          ],
        );
      },
    );
  }

  IconData _getCategoryIcon(String category) {
    final categoryData = _categories.firstWhere(
      (cat) => cat['name'] == category,
      orElse: () => {'icon': Icons.help_outline},
    );
    return categoryData['icon'] as IconData;
  }

  String _formatDate(DateTime date) {
    return '${date.day}/${date.month}/${date.year}';
  }
}
