import 'package:flutter/material.dart';

/// Model representing a recovery session
class RecoverySessionModel {
  final String id;
  final String name;
  final String description;
  final String imageUrl;
  final String category; // e.g., "Stretching", "Foam Rolling", "Ice Bath"
  final int durationMinutes;
  final List<RecoveryExerciseModel> exercises;
  final IconData icon;
  final Color color;
  final bool isPremium;
  final List<String> targetAreas; // e.g., "Legs", "Back", "Full Body"
  final String intensity; // e.g., "Light", "Moderate", "Intense"

  const RecoverySessionModel({
    required this.id,
    required this.name,
    required this.description,
    required this.imageUrl,
    required this.category,
    required this.durationMinutes,
    required this.exercises,
    required this.icon,
    required this.color,
    this.isPremium = false,
    required this.targetAreas,
    required this.intensity,
  });

  // Create a copy of the recovery session with modified properties
  RecoverySessionModel copyWith({
    String? id,
    String? name,
    String? description,
    String? imageUrl,
    String? category,
    int? durationMinutes,
    List<RecoveryExerciseModel>? exercises,
    IconData? icon,
    Color? color,
    bool? isPremium,
    List<String>? targetAreas,
    String? intensity,
  }) {
    return RecoverySessionModel(
      id: id ?? this.id,
      name: name ?? this.name,
      description: description ?? this.description,
      imageUrl: imageUrl ?? this.imageUrl,
      category: category ?? this.category,
      durationMinutes: durationMinutes ?? this.durationMinutes,
      exercises: exercises ?? this.exercises,
      icon: icon ?? this.icon,
      color: color ?? this.color,
      isPremium: isPremium ?? this.isPremium,
      targetAreas: targetAreas ?? this.targetAreas,
      intensity: intensity ?? this.intensity,
    );
  }

  // Convert recovery session to map
  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'name': name,
      'description': description,
      'imageUrl': imageUrl,
      'category': category,
      'durationMinutes': durationMinutes,
      'exercises': exercises.map((e) => e.toMap()).toList(),
      'isPremium': isPremium,
      'targetAreas': targetAreas,
      'intensity': intensity,
    };
  }

  // Create recovery session from map
  factory RecoverySessionModel.fromMap(Map<String, dynamic> map) {
    return RecoverySessionModel(
      id: map['id'],
      name: map['name'],
      description: map['description'],
      imageUrl: map['imageUrl'],
      category: map['category'],
      durationMinutes: map['durationMinutes'],
      exercises: List<RecoveryExerciseModel>.from(
        map['exercises']?.map((x) => RecoveryExerciseModel.fromMap(x)),
      ),
      icon: Icons.self_improvement, // Default icon
      color: Colors.teal, // Default color
      isPremium: map['isPremium'] ?? false,
      targetAreas: List<String>.from(map['targetAreas'] ?? []),
      intensity: map['intensity'] ?? 'Moderate',
    );
  }
}

/// Model representing a recovery exercise
class RecoveryExerciseModel {
  final String id;
  final String name;
  final String description;
  final String imageUrl;
  final String? videoUrl;
  final int durationSeconds;
  final int sets;
  final int repetitions;
  final List<String> instructions;
  final List<String> targetAreas;
  final String equipment; // e.g., "Foam Roller", "Resistance Band", "None"

  const RecoveryExerciseModel({
    required this.id,
    required this.name,
    required this.description,
    required this.imageUrl,
    this.videoUrl,
    required this.durationSeconds,
    required this.sets,
    required this.repetitions,
    required this.instructions,
    required this.targetAreas,
    required this.equipment,
  });

  // Create a copy of the recovery exercise with modified properties
  RecoveryExerciseModel copyWith({
    String? id,
    String? name,
    String? description,
    String? imageUrl,
    String? videoUrl,
    int? durationSeconds,
    int? sets,
    int? repetitions,
    List<String>? instructions,
    List<String>? targetAreas,
    String? equipment,
  }) {
    return RecoveryExerciseModel(
      id: id ?? this.id,
      name: name ?? this.name,
      description: description ?? this.description,
      imageUrl: imageUrl ?? this.imageUrl,
      videoUrl: videoUrl ?? this.videoUrl,
      durationSeconds: durationSeconds ?? this.durationSeconds,
      sets: sets ?? this.sets,
      repetitions: repetitions ?? this.repetitions,
      instructions: instructions ?? this.instructions,
      targetAreas: targetAreas ?? this.targetAreas,
      equipment: equipment ?? this.equipment,
    );
  }

  // Convert recovery exercise to map
  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'name': name,
      'description': description,
      'imageUrl': imageUrl,
      'videoUrl': videoUrl,
      'durationSeconds': durationSeconds,
      'sets': sets,
      'repetitions': repetitions,
      'instructions': instructions,
      'targetAreas': targetAreas,
      'equipment': equipment,
    };
  }

  // Create recovery exercise from map
  factory RecoveryExerciseModel.fromMap(Map<String, dynamic> map) {
    return RecoveryExerciseModel(
      id: map['id'],
      name: map['name'],
      description: map['description'],
      imageUrl: map['imageUrl'],
      videoUrl: map['videoUrl'],
      durationSeconds: map['durationSeconds'],
      sets: map['sets'],
      repetitions: map['repetitions'],
      instructions: List<String>.from(map['instructions'] ?? []),
      targetAreas: List<String>.from(map['targetAreas'] ?? []),
      equipment: map['equipment'] ?? 'None',
    );
  }
}

/// Model representing a recovery log entry
class RecoveryLogModel {
  final String id;
  final DateTime date;
  final String sessionId;
  final String sessionName;
  final int durationMinutes;
  final int perceivedEffectiveness; // 1-5 scale
  final String notes;
  final List<String> painAreas; // e.g., "Legs", "Back", "Shoulders"
  final int painLevel; // 1-10 scale
  final int recoveryLevel; // 1-10 scale
  final int energyLevel; // 1-10 scale
  final int sleepQuality; // 1-10 scale
  final int sleepHours;

  const RecoveryLogModel({
    required this.id,
    required this.date,
    required this.sessionId,
    required this.sessionName,
    required this.durationMinutes,
    required this.perceivedEffectiveness,
    required this.notes,
    required this.painAreas,
    required this.painLevel,
    required this.recoveryLevel,
    required this.energyLevel,
    required this.sleepQuality,
    required this.sleepHours,
  });

  // Create a copy of the recovery log with modified properties
  RecoveryLogModel copyWith({
    String? id,
    DateTime? date,
    String? sessionId,
    String? sessionName,
    int? durationMinutes,
    int? perceivedEffectiveness,
    String? notes,
    List<String>? painAreas,
    int? painLevel,
    int? recoveryLevel,
    int? energyLevel,
    int? sleepQuality,
    int? sleepHours,
  }) {
    return RecoveryLogModel(
      id: id ?? this.id,
      date: date ?? this.date,
      sessionId: sessionId ?? this.sessionId,
      sessionName: sessionName ?? this.sessionName,
      durationMinutes: durationMinutes ?? this.durationMinutes,
      perceivedEffectiveness: perceivedEffectiveness ?? this.perceivedEffectiveness,
      notes: notes ?? this.notes,
      painAreas: painAreas ?? this.painAreas,
      painLevel: painLevel ?? this.painLevel,
      recoveryLevel: recoveryLevel ?? this.recoveryLevel,
      energyLevel: energyLevel ?? this.energyLevel,
      sleepQuality: sleepQuality ?? this.sleepQuality,
      sleepHours: sleepHours ?? this.sleepHours,
    );
  }

  // Convert recovery log to map
  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'date': date.toIso8601String(),
      'sessionId': sessionId,
      'sessionName': sessionName,
      'durationMinutes': durationMinutes,
      'perceivedEffectiveness': perceivedEffectiveness,
      'notes': notes,
      'painAreas': painAreas,
      'painLevel': painLevel,
      'recoveryLevel': recoveryLevel,
      'energyLevel': energyLevel,
      'sleepQuality': sleepQuality,
      'sleepHours': sleepHours,
    };
  }

  // Create recovery log from map
  factory RecoveryLogModel.fromMap(Map<String, dynamic> map) {
    return RecoveryLogModel(
      id: map['id'],
      date: DateTime.parse(map['date']),
      sessionId: map['sessionId'],
      sessionName: map['sessionName'],
      durationMinutes: map['durationMinutes'],
      perceivedEffectiveness: map['perceivedEffectiveness'],
      notes: map['notes'] ?? '',
      painAreas: List<String>.from(map['painAreas'] ?? []),
      painLevel: map['painLevel'] ?? 1,
      recoveryLevel: map['recoveryLevel'] ?? 5,
      energyLevel: map['energyLevel'] ?? 5,
      sleepQuality: map['sleepQuality'] ?? 5,
      sleepHours: map['sleepHours'] ?? 7,
    );
  }

  // Get formatted date string
  String get formattedDate {
    final now = DateTime.now();
    final difference = now.difference(date);

    if (difference.inDays == 0) {
      return 'Today';
    } else if (difference.inDays == 1) {
      return 'Yesterday';
    } else if (difference.inDays < 7) {
      return '${difference.inDays} days ago';
    } else {
      return '${date.day}/${date.month}/${date.year}';
    }
  }
}
