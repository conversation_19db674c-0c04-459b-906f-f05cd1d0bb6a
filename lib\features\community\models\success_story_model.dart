import 'package:flutter/material.dart';
import 'package:fit_4_force/shared/models/base_model.dart';

/// Model representing a success story
class SuccessStoryModel extends BaseModel {
  final String userId;
  final String userName;
  final String? userProfileImageUrl;
  final String title;
  final String content;
  final String agency;
  final String position;
  final DateTime successDate;
  final List<String> tags;
  final int likesCount;
  final int commentsCount;
  final bool isLikedByCurrentUser;
  final List<String>? imageUrls;
  final IconData icon;
  final Color color;
  final bool isVerified;

  const SuccessStoryModel({
    required super.id,
    required super.createdAt,
    super.updatedAt,
    required this.userId,
    required this.userName,
    this.userProfileImageUrl,
    required this.title,
    required this.content,
    required this.agency,
    required this.position,
    required this.successDate,
    required this.tags,
    required this.likesCount,
    required this.commentsCount,
    required this.isLikedByCurrentUser,
    this.imageUrls,
    required this.icon,
    required this.color,
    required this.isVerified,
  });

  @override
  List<Object?> get props => [
        ...super.props,
        userId,
        userName,
        userProfileImageUrl,
        title,
        content,
        agency,
        position,
        successDate,
        tags,
        likesCount,
        commentsCount,
        isLikedByCurrentUser,
        imageUrls,
        icon,
        color,
        isVerified,
      ];

  @override
  SuccessStoryModel copyWith({
    String? id,
    DateTime? createdAt,
    DateTime? updatedAt,
    String? userId,
    String? userName,
    String? userProfileImageUrl,
    String? title,
    String? content,
    String? agency,
    String? position,
    DateTime? successDate,
    List<String>? tags,
    int? likesCount,
    int? commentsCount,
    bool? isLikedByCurrentUser,
    List<String>? imageUrls,
    IconData? icon,
    Color? color,
    bool? isVerified,
  }) {
    return SuccessStoryModel(
      id: id ?? this.id,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      userId: userId ?? this.userId,
      userName: userName ?? this.userName,
      userProfileImageUrl: userProfileImageUrl ?? this.userProfileImageUrl,
      title: title ?? this.title,
      content: content ?? this.content,
      agency: agency ?? this.agency,
      position: position ?? this.position,
      successDate: successDate ?? this.successDate,
      tags: tags ?? this.tags,
      likesCount: likesCount ?? this.likesCount,
      commentsCount: commentsCount ?? this.commentsCount,
      isLikedByCurrentUser: isLikedByCurrentUser ?? this.isLikedByCurrentUser,
      imageUrls: imageUrls ?? this.imageUrls,
      icon: icon ?? this.icon,
      color: color ?? this.color,
      isVerified: isVerified ?? this.isVerified,
    );
  }
}
