import 'package:flutter/material.dart';
import 'package:fit_4_force/features/prep/models/study_material_model.dart';
import 'package:logger/logger.dart';
import 'package:uuid/uuid.dart';

/// Service for handling study materials
class StudyMaterialService {
  final Logger _logger = Logger();
  final Uuid _uuid = const Uuid();

  // Mock categories
  final List<StudyMaterialCategory> _categories = [
    const StudyMaterialCategory(
      id: 'history',
      name: 'History',
      description: 'Nigerian and military history materials',
      icon: Icons.history_edu,
      color: Colors.brown,
      materialCount: 12,
    ),
    const StudyMaterialCategory(
      id: 'mathematics',
      name: 'Mathematics',
      description: 'Essential mathematics for military exams',
      icon: Icons.calculate,
      color: Colors.blue,
      materialCount: 15,
    ),
    const StudyMaterialCategory(
      id: 'english',
      name: 'English',
      description: 'English language and comprehension',
      icon: Icons.spellcheck,
      color: Colors.green,
      materialCount: 10,
    ),
    const StudyMaterialCategory(
      id: 'current-affairs',
      name: 'Current Affairs',
      description: 'Latest news and current affairs',
      icon: Icons.newspaper,
      color: Colors.orange,
      materialCount: 8,
    ),
    const StudyMaterialCategory(
      id: 'general-knowledge',
      name: 'General Knowledge',
      description: 'General knowledge and aptitude',
      icon: Icons.psychology,
      color: Colors.purple,
      materialCount: 20,
    ),
    const StudyMaterialCategory(
      id: 'physical-training',
      name: 'Physical Training',
      description: 'Physical fitness training guides',
      icon: Icons.fitness_center,
      color: Colors.red,
      materialCount: 5,
    ),
  ];

  // Mock study materials
  final List<StudyMaterialModel> _studyMaterials = [
    StudyMaterialModel(
      id: 'sm001',
      title: 'Nigerian Military History',
      description: 'A comprehensive guide to Nigerian military history from colonial times to present day.',
      category: 'History',
      agency: 'Nigerian Army',
      contentType: 'pdf',
      contentUrl: 'assets/images/content/history.jpg',
      publishedDate: DateTime(2023, 1, 15),
      isPremium: true,
      icon: Icons.history_edu,
      color: Colors.brown,
      estimatedReadTime: 45,
      tags: ['history', 'military', 'nigeria'],
    ),
    StudyMaterialModel(
      id: 'sm002',
      title: 'Basic Mathematics for Military Exams',
      description: 'Essential mathematics concepts frequently tested in military entrance exams.',
      category: 'Mathematics',
      agency: 'All',
      contentType: 'pdf',
      contentUrl: 'assets/images/content/math.jpg',
      publishedDate: DateTime(2023, 2, 10),
      isPremium: false,
      icon: Icons.calculate,
      color: Colors.blue,
      estimatedReadTime: 30,
      tags: ['mathematics', 'basics', 'exam prep'],
    ),
    StudyMaterialModel(
      id: 'sm003',
      title: 'English Comprehension Guide',
      description: 'Improve your English comprehension skills for military entrance exams.',
      category: 'English',
      agency: 'All',
      contentType: 'pdf',
      contentUrl: 'assets/images/content/english.jpg',
      publishedDate: DateTime(2023, 3, 5),
      isPremium: false,
      icon: Icons.spellcheck,
      color: Colors.green,
      estimatedReadTime: 25,
      tags: ['english', 'comprehension', 'grammar'],
    ),
    StudyMaterialModel(
      id: 'sm004',
      title: 'Current Affairs: 2023 Update',
      description: 'Latest current affairs relevant to military entrance exams.',
      category: 'Current Affairs',
      agency: 'All',
      contentType: 'pdf',
      contentUrl: 'assets/images/content/current_affairs.jpg',
      publishedDate: DateTime(2023, 4, 20),
      isPremium: true,
      icon: Icons.newspaper,
      color: Colors.orange,
      estimatedReadTime: 20,
      tags: ['current affairs', '2023', 'news'],
    ),
    StudyMaterialModel(
      id: 'sm005',
      title: 'Interview Preparation Guide',
      description: 'Comprehensive guide to prepare for military interviews.',
      category: 'General Knowledge',
      agency: 'All',
      contentType: 'pdf',
      contentUrl: 'assets/images/content/interview.jpg',
      publishedDate: DateTime(2023, 5, 12),
      isPremium: true,
      icon: Icons.psychology,
      color: Colors.purple,
      estimatedReadTime: 35,
      tags: ['interview', 'preparation', 'tips'],
    ),
    StudyMaterialModel(
      id: 'sm006',
      title: 'Push-up and Sit-up Techniques',
      description: 'Proper techniques for push-ups and sit-ups to pass physical fitness tests.',
      category: 'Physical Training',
      agency: 'All',
      contentType: 'video',
      contentUrl: 'assets/images/content/pushups.jpg',
      publishedDate: DateTime(2023, 6, 8),
      isPremium: false,
      icon: Icons.fitness_center,
      color: Colors.red,
      estimatedReadTime: 15,
      tags: ['fitness', 'push-ups', 'sit-ups', 'techniques'],
    ),
  ];

  /// Get all study material categories
  List<StudyMaterialCategory> getAllCategories() {
    _logger.i('Getting all study material categories');
    return _categories;
  }

  /// Get study materials by category
  List<StudyMaterialModel> getMaterialsByCategory(String categoryId) {
    _logger.i('Getting study materials for category: $categoryId');
    return _studyMaterials
        .where((material) => material.category.toLowerCase() == categoryId.toLowerCase())
        .toList();
  }

  /// Get study materials by agency
  List<StudyMaterialModel> getMaterialsByAgency(String agency) {
    _logger.i('Getting study materials for agency: $agency');
    return _studyMaterials
        .where((material) => material.agency == agency || material.agency == 'All')
        .toList();
  }

  /// Get study material by ID
  StudyMaterialModel? getMaterialById(String id) {
    _logger.i('Getting study material with ID: $id');
    try {
      return _studyMaterials.firstWhere((material) => material.id == id);
    } catch (e) {
      _logger.e('Error getting study material: $e');
      return null;
    }
  }

  /// Get featured study materials
  List<StudyMaterialModel> getFeaturedMaterials() {
    _logger.i('Getting featured study materials');
    // Return the 3 most recent materials
    final sortedMaterials = List<StudyMaterialModel>.from(_studyMaterials)
      ..sort((a, b) => b.publishedDate.compareTo(a.publishedDate));
    return sortedMaterials.take(3).toList();
  }

  /// Get free study materials
  List<StudyMaterialModel> getFreeMaterials() {
    _logger.i('Getting free study materials');
    return _studyMaterials.where((material) => !material.isPremium).toList();
  }

  /// Search study materials
  List<StudyMaterialModel> searchMaterials(String query) {
    _logger.i('Searching study materials with query: $query');
    final lowercaseQuery = query.toLowerCase();
    return _studyMaterials.where((material) {
      return material.title.toLowerCase().contains(lowercaseQuery) ||
          material.description.toLowerCase().contains(lowercaseQuery) ||
          material.tags.any((tag) => tag.toLowerCase().contains(lowercaseQuery));
    }).toList();
  }
}
