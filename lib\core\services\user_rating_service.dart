import 'package:fit_4_force/core/services/user_storage_service.dart';
import 'package:logger/logger.dart';

/// Service for handling user ratings and reviews
class UserRatingService {
  static final UserRatingService _instance = UserRatingService._internal();
  factory UserRatingService() => _instance;
  UserRatingService._internal();

  final UserStorageService _storageService = UserStorageService();
  final Logger _logger = Logger();

  /// Get user's rating for a specific material
  Future<Map<String, dynamic>?> getUserRating(String materialId) async {
    try {
      return await _storageService.getUserRating(materialId);
    } catch (e) {
      _logger.e('❌ Error getting user rating: $e');
      return null;
    }
  }

  /// Rate a study material
  Future<bool> rateMaterial({
    required String materialId,
    required int rating,
    String? review,
  }) async {
    try {
      if (rating < 1 || rating > 5) {
        throw Exception('Rating must be between 1 and 5');
      }

      return await _storageService.rateStudyMaterial(
        materialId: materialId,
        rating: rating,
        review: review,
      );
    } catch (e) {
      _logger.e('❌ Error rating material: $e');
      return false;
    }
  }

  /// Get all ratings for a material (for display purposes)
  Future<List<Map<String, dynamic>>> getMaterialRatings(String materialId) async {
    try {
      // This would require a separate query to get all ratings for a material
      // For now, return empty list as this is typically handled by the material's average rating
      return [];
    } catch (e) {
      _logger.e('❌ Error getting material ratings: $e');
      return [];
    }
  }

  /// Get user's rating statistics
  Future<Map<String, dynamic>> getUserRatingStats() async {
    try {
      // This would require aggregating user's rating activity
      // For now, return basic stats
      return {
        'total_ratings': 0,
        'average_rating_given': 0.0,
        'materials_rated': 0,
      };
    } catch (e) {
      _logger.e('❌ Error getting user rating stats: $e');
      return {};
    }
  }
}
