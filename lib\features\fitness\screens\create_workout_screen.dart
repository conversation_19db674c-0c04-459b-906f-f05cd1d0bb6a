import 'package:flutter/material.dart';
import 'package:fit_4_force/core/theme/app_theme.dart';
import 'package:fit_4_force/core/utils/navigation_service.dart';
import 'package:fit_4_force/features/fitness/models/workout_model.dart';
import 'package:fit_4_force/features/fitness/services/workout_service.dart';

class CreateWorkoutScreen extends StatefulWidget {
  const CreateWorkoutScreen({super.key});

  @override
  State<CreateWorkoutScreen> createState() => _CreateWorkoutScreenState();
}

class _CreateWorkoutScreenState extends State<CreateWorkoutScreen> {
  final WorkoutService _workoutService = WorkoutService();
  final _formKey = GlobalKey<FormState>();

  // Form fields
  final _nameController = TextEditingController();
  final _descriptionController = TextEditingController();
  String _selectedCategory = '';
  final List<ExerciseModel> _exercises = [];

  // Available categories
  late List<WorkoutCategoryModel> _categories;

  // Selected color and icon
  Color _selectedColor = Colors.blue;
  IconData _selectedIcon = Icons.fitness_center;

  // Available colors and icons for selection
  final List<Color> _availableColors = [
    Colors.blue,
    Colors.green,
    Colors.orange,
    Colors.purple,
    Colors.red,
    Colors.teal,
  ];

  final List<IconData> _availableIcons = [
    Icons.fitness_center,
    Icons.directions_run,
    Icons.accessibility_new,
    Icons.timer,
    Icons.sports_gymnastics,
    Icons.sports_handball,
  ];

  @override
  void initState() {
    super.initState();
    _loadCategories();
  }

  @override
  void dispose() {
    _nameController.dispose();
    _descriptionController.dispose();
    super.dispose();
  }

  void _loadCategories() {
    _categories = _workoutService.getAllCategories();
    if (_categories.isNotEmpty) {
      _selectedCategory = _categories.first.name;
    }
  }

  void _addExercise() {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(16)),
      ),
      builder: (context) => _buildAddExerciseSheet(),
    );
  }

  Widget _buildAddExerciseSheet() {
    final nameController = TextEditingController();
    final descriptionController = TextEditingController();
    int sets = 3;
    int reps = 12;
    int duration = 60;
    int restTime = 30;

    return StatefulBuilder(
      builder: (context, setState) {
        return DraggableScrollableSheet(
          initialChildSize: 0.9,
          minChildSize: 0.5,
          maxChildSize: 0.9,
          expand: false,
          builder: (context, scrollController) {
            return SingleChildScrollView(
              controller: scrollController,
              child: Padding(
                padding: const EdgeInsets.all(16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        const Text(
                          'Add Exercise',
                          style: TextStyle(
                            fontSize: 20,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                        IconButton(
                          icon: const Icon(Icons.close),
                          onPressed: () => Navigator.pop(context),
                        ),
                      ],
                    ),
                    const SizedBox(height: 16),
                    TextField(
                      controller: nameController,
                      decoration: const InputDecoration(
                        labelText: 'Exercise Name',
                        border: OutlineInputBorder(),
                      ),
                    ),
                    const SizedBox(height: 16),
                    TextField(
                      controller: descriptionController,
                      maxLines: 3,
                      decoration: const InputDecoration(
                        labelText: 'Description',
                        border: OutlineInputBorder(),
                      ),
                    ),
                    const SizedBox(height: 24),
                    const Text(
                      'Sets',
                      style: TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    Slider(
                      value: sets.toDouble(),
                      min: 1,
                      max: 10,
                      divisions: 9,
                      label: sets.toString(),
                      onChanged: (value) {
                        setState(() {
                          sets = value.toInt();
                        });
                      },
                    ),
                    Text('$sets sets'),
                    const SizedBox(height: 16),
                    const Text(
                      'Reps',
                      style: TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    Slider(
                      value: reps.toDouble(),
                      min: 1,
                      max: 30,
                      divisions: 29,
                      label: reps.toString(),
                      onChanged: (value) {
                        setState(() {
                          reps = value.toInt();
                        });
                      },
                    ),
                    Text('$reps reps'),
                    const SizedBox(height: 16),
                    const Text(
                      'Duration (seconds)',
                      style: TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    Slider(
                      value: duration.toDouble(),
                      min: 10,
                      max: 180,
                      divisions: 17,
                      label: duration.toString(),
                      onChanged: (value) {
                        setState(() {
                          duration = value.toInt();
                        });
                      },
                    ),
                    Text('$duration seconds'),
                    const SizedBox(height: 16),
                    const Text(
                      'Rest Time (seconds)',
                      style: TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    Slider(
                      value: restTime.toDouble(),
                      min: 0,
                      max: 120,
                      divisions: 12,
                      label: restTime.toString(),
                      onChanged: (value) {
                        setState(() {
                          restTime = value.toInt();
                        });
                      },
                    ),
                    Text('$restTime seconds'),
                    const SizedBox(height: 24),
                    SizedBox(
                      width: double.infinity,
                      child: ElevatedButton(
                        onPressed: () {
                          if (nameController.text.isEmpty) {
                            ScaffoldMessenger.of(context).showSnackBar(
                              const SnackBar(
                                content: Text('Please enter an exercise name'),
                              ),
                            );
                            return;
                          }

                          final exercise = ExerciseModel(
                            id:
                                DateTime.now().millisecondsSinceEpoch
                                    .toString(),
                            name: nameController.text,
                            description: descriptionController.text,
                            imageUrl: 'assets/images/exercises/default.jpg',
                            videoUrl: '',
                            duration: duration,
                            sets: sets,
                            reps: reps,
                            restTime: restTime,
                          );

                          Navigator.pop(context, exercise);
                        },
                        style: ElevatedButton.styleFrom(
                          backgroundColor: AppTheme.primaryColor,
                          padding: const EdgeInsets.symmetric(vertical: 16),
                        ),
                        child: const Text('ADD EXERCISE'),
                      ),
                    ),
                  ],
                ),
              ),
            );
          },
        );
      },
    );
  }

  void _removeExercise(int index) {
    setState(() {
      _exercises.removeAt(index);
    });
  }

  void _saveWorkout() {
    if (_formKey.currentState!.validate()) {
      if (_exercises.isEmpty) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('Please add at least one exercise')),
        );
        return;
      }

      final workout = _workoutService.createCustomWorkout(
        name: _nameController.text,
        description: _descriptionController.text,
        category: _selectedCategory,
        exercises: _exercises,
        icon: _selectedIcon,
        color: _selectedColor,
      );

      // Navigate to the workout detail screen
      NavigationService().navigateToReplacement(
        '/workout-detail',
        arguments: workout.id,
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(title: const Text('Create Custom Workout')),
      body: Form(
        key: _formKey,
        child: ListView(
          padding: const EdgeInsets.all(16),
          children: [
            TextFormField(
              controller: _nameController,
              decoration: const InputDecoration(
                labelText: 'Workout Name',
                border: OutlineInputBorder(),
              ),
              validator: (value) {
                if (value == null || value.isEmpty) {
                  return 'Please enter a workout name';
                }
                return null;
              },
            ),
            const SizedBox(height: 16),
            TextFormField(
              controller: _descriptionController,
              maxLines: 3,
              decoration: const InputDecoration(
                labelText: 'Description',
                border: OutlineInputBorder(),
              ),
              validator: (value) {
                if (value == null || value.isEmpty) {
                  return 'Please enter a description';
                }
                return null;
              },
            ),
            const SizedBox(height: 16),
            DropdownButtonFormField<String>(
              value: _selectedCategory.isNotEmpty ? _selectedCategory : null,
              decoration: const InputDecoration(
                labelText: 'Category',
                border: OutlineInputBorder(),
              ),
              items:
                  _categories.map((category) {
                    return DropdownMenuItem<String>(
                      value: category.name,
                      child: Text(category.name),
                    );
                  }).toList(),
              onChanged: (value) {
                setState(() {
                  _selectedCategory = value!;
                });
              },
              validator: (value) {
                if (value == null || value.isEmpty) {
                  return 'Please select a category';
                }
                return null;
              },
            ),
            const SizedBox(height: 24),
            const Text(
              'Customize Appearance',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 16),
            Row(
              children: [
                const Text('Color: '),
                const SizedBox(width: 16),
                Expanded(
                  child: SingleChildScrollView(
                    scrollDirection: Axis.horizontal,
                    child: Row(
                      children:
                          _availableColors.map((color) {
                            return GestureDetector(
                              onTap: () {
                                setState(() {
                                  _selectedColor = color;
                                });
                              },
                              child: Container(
                                margin: const EdgeInsets.only(right: 8),
                                width: 40,
                                height: 40,
                                decoration: BoxDecoration(
                                  color: color,
                                  shape: BoxShape.circle,
                                  border:
                                      _selectedColor == color
                                          ? Border.all(
                                            color: Colors.black,
                                            width: 2,
                                          )
                                          : null,
                                ),
                              ),
                            );
                          }).toList(),
                    ),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            Row(
              children: [
                const Text('Icon: '),
                const SizedBox(width: 16),
                Expanded(
                  child: SingleChildScrollView(
                    scrollDirection: Axis.horizontal,
                    child: Row(
                      children:
                          _availableIcons.map((icon) {
                            return GestureDetector(
                              onTap: () {
                                setState(() {
                                  _selectedIcon = icon;
                                });
                              },
                              child: Container(
                                margin: const EdgeInsets.only(right: 8),
                                width: 40,
                                height: 40,
                                decoration: BoxDecoration(
                                  color: _selectedColor.withValues(
                                    alpha: (0.1 * 255).round().toDouble(),
                                  ),
                                  shape: BoxShape.circle,
                                  border:
                                      _selectedIcon == icon
                                          ? Border.all(
                                            color: _selectedColor,
                                            width: 2,
                                          )
                                          : null,
                                ),
                                child: Icon(icon, color: _selectedColor),
                              ),
                            );
                          }).toList(),
                    ),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 24),
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                const Text(
                  'Exercises',
                  style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                ),
                ElevatedButton.icon(
                  onPressed: _addExercise,
                  icon: const Icon(Icons.add),
                  label: const Text('ADD'),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: AppTheme.primaryColor,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            _exercises.isEmpty
                ? _buildEmptyExercisesState()
                : _buildExercisesList(),
            const SizedBox(height: 32),
            SizedBox(
              width: double.infinity,
              child: ElevatedButton(
                onPressed: _saveWorkout,
                style: ElevatedButton.styleFrom(
                  backgroundColor: _selectedColor,
                  foregroundColor: Colors.white,
                  padding: const EdgeInsets.symmetric(vertical: 16),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(16),
                  ),
                ),
                child: const Text(
                  'SAVE WORKOUT',
                  style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildEmptyExercisesState() {
    return Container(
      height: 150,
      decoration: BoxDecoration(
        color: Colors.grey.shade100,
        borderRadius: BorderRadius.circular(16),
      ),
      child: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.fitness_center_outlined,
              size: 48,
              color: Colors.grey.shade400,
            ),
            const SizedBox(height: 16),
            Text(
              'No exercises added yet',
              style: TextStyle(color: AppTheme.textSecondaryLight),
            ),
            const SizedBox(height: 8),
            Text(
              'Tap the ADD button to add exercises',
              style: TextStyle(
                fontSize: 12,
                color: AppTheme.textSecondaryLight,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildExercisesList() {
    return ListView.separated(
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      itemCount: _exercises.length,
      separatorBuilder: (context, index) => const SizedBox(height: 12),
      itemBuilder: (context, index) {
        final exercise = _exercises[index];
        return _buildExerciseItem(exercise, index);
      },
    );
  }

  Widget _buildExerciseItem(ExerciseModel exercise, int index) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withValues(
              alpha: (0.1 * 255).round().toDouble(),
            ),
            blurRadius: 10,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Row(
        children: [
          Container(
            width: 40,
            height: 40,
            decoration: BoxDecoration(
              color: _selectedColor.withValues(
                alpha: (0.1 * 255).round().toDouble(),
              ),
              shape: BoxShape.circle,
            ),
            child: Center(
              child: Text(
                '${index + 1}',
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                  color: _selectedColor,
                ),
              ),
            ),
          ),
          const SizedBox(width: 16),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  exercise.name,
                  style: const TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                if (exercise.description.isNotEmpty) ...[
                  const SizedBox(height: 4),
                  Text(
                    exercise.description,
                    style: TextStyle(
                      fontSize: 14,
                      color: AppTheme.textSecondaryLight,
                    ),
                    maxLines: 2,
                    overflow: TextOverflow.ellipsis,
                  ),
                ],
                const SizedBox(height: 8),
                Row(
                  children: [
                    _buildExerciseStat(Icons.repeat, '${exercise.sets} sets'),
                    const SizedBox(width: 16),
                    _buildExerciseStat(
                      Icons.fitness_center,
                      '${exercise.reps} reps',
                    ),
                    const SizedBox(width: 16),
                    _buildExerciseStat(
                      Icons.timer_outlined,
                      '${exercise.restTime}s rest',
                    ),
                  ],
                ),
              ],
            ),
          ),
          IconButton(
            icon: const Icon(Icons.delete_outline),
            color: Colors.red,
            onPressed: () => _removeExercise(index),
          ),
        ],
      ),
    );
  }

  Widget _buildExerciseStat(IconData icon, String text) {
    return Row(
      children: [
        Icon(icon, size: 14, color: AppTheme.textSecondaryLight),
        const SizedBox(width: 4),
        Text(
          text,
          style: TextStyle(fontSize: 12, color: AppTheme.textSecondaryLight),
        ),
      ],
    );
  }
}
