import 'package:flutter/material.dart';

/// Model representing a workout
class WorkoutModel {
  final String id;
  final String name;
  final String description;
  final String imageUrl;
  final String category;
  final int duration; // in minutes
  final int calories;
  final List<ExerciseModel> exercises;
  final IconData icon;
  final Color color;
  final bool isPremium;
  final String difficulty; // beginner, intermediate, advanced

  const WorkoutModel({
    required this.id,
    required this.name,
    required this.description,
    required this.imageUrl,
    required this.category,
    required this.duration,
    required this.calories,
    required this.exercises,
    required this.icon,
    required this.color,
    this.isPremium = false,
    this.difficulty = 'beginner',
  });

  // Create a copy of the workout with modified properties
  WorkoutModel copyWith({
    String? id,
    String? name,
    String? description,
    String? imageUrl,
    String? category,
    int? duration,
    int? calories,
    List<ExerciseModel>? exercises,
    IconData? icon,
    Color? color,
    bool? isPremium,
    String? difficulty,
  }) {
    return WorkoutModel(
      id: id ?? this.id,
      name: name ?? this.name,
      description: description ?? this.description,
      imageUrl: imageUrl ?? this.imageUrl,
      category: category ?? this.category,
      duration: duration ?? this.duration,
      calories: calories ?? this.calories,
      exercises: exercises ?? this.exercises,
      icon: icon ?? this.icon,
      color: color ?? this.color,
      isPremium: isPremium ?? this.isPremium,
      difficulty: difficulty ?? this.difficulty,
    );
  }

  // Convert workout to map
  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'name': name,
      'description': description,
      'imageUrl': imageUrl,
      'category': category,
      'duration': duration,
      'calories': calories,
      'exercises': exercises.map((e) => e.toMap()).toList(),
      'isPremium': isPremium,
      'difficulty': difficulty,
    };
  }

  // Create workout from map
  factory WorkoutModel.fromMap(Map<String, dynamic> map) {
    return WorkoutModel(
      id: map['id'],
      name: map['name'],
      description: map['description'],
      imageUrl: map['imageUrl'],
      category: map['category'],
      duration: map['duration'],
      calories: map['calories'],
      exercises: List<ExerciseModel>.from(
        map['exercises']?.map((x) => ExerciseModel.fromMap(x)),
      ),
      icon: Icons.fitness_center, // Default icon
      color: Colors.blue, // Default color
      isPremium: map['isPremium'] ?? false,
      difficulty: map['difficulty'] ?? 'beginner',
    );
  }
}

/// Model representing an exercise within a workout
class ExerciseModel {
  final String id;
  final String name;
  final String description;
  final String imageUrl;
  final String videoUrl;
  final int duration; // in seconds
  final int sets;
  final int reps;
  final int restTime; // in seconds

  const ExerciseModel({
    required this.id,
    required this.name,
    required this.description,
    required this.imageUrl,
    required this.videoUrl,
    required this.duration,
    required this.sets,
    required this.reps,
    required this.restTime,
  });

  // Create a copy of the exercise with modified properties
  ExerciseModel copyWith({
    String? id,
    String? name,
    String? description,
    String? imageUrl,
    String? videoUrl,
    int? duration,
    int? sets,
    int? reps,
    int? restTime,
  }) {
    return ExerciseModel(
      id: id ?? this.id,
      name: name ?? this.name,
      description: description ?? this.description,
      imageUrl: imageUrl ?? this.imageUrl,
      videoUrl: videoUrl ?? this.videoUrl,
      duration: duration ?? this.duration,
      sets: sets ?? this.sets,
      reps: reps ?? this.reps,
      restTime: restTime ?? this.restTime,
    );
  }

  // Convert exercise to map
  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'name': name,
      'description': description,
      'imageUrl': imageUrl,
      'videoUrl': videoUrl,
      'duration': duration,
      'sets': sets,
      'reps': reps,
      'restTime': restTime,
    };
  }

  // Create exercise from map
  factory ExerciseModel.fromMap(Map<String, dynamic> map) {
    return ExerciseModel(
      id: map['id'],
      name: map['name'],
      description: map['description'],
      imageUrl: map['imageUrl'],
      videoUrl: map['videoUrl'],
      duration: map['duration'],
      sets: map['sets'],
      reps: map['reps'],
      restTime: map['restTime'],
    );
  }
}

/// Model representing a workout category
class WorkoutCategoryModel {
  final String id;
  final String name;
  final String description;
  final IconData icon;
  final Color color;
  final String imageUrl;
  final int workoutCount;

  const WorkoutCategoryModel({
    required this.id,
    required this.name,
    required this.description,
    required this.icon,
    required this.color,
    required this.imageUrl,
    this.workoutCount = 0,
  });

  // Create a copy of the category with modified properties
  WorkoutCategoryModel copyWith({
    String? id,
    String? name,
    String? description,
    IconData? icon,
    Color? color,
    String? imageUrl,
    int? workoutCount,
  }) {
    return WorkoutCategoryModel(
      id: id ?? this.id,
      name: name ?? this.name,
      description: description ?? this.description,
      icon: icon ?? this.icon,
      color: color ?? this.color,
      imageUrl: imageUrl ?? this.imageUrl,
      workoutCount: workoutCount ?? this.workoutCount,
    );
  }

  // Convert category to map
  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'name': name,
      'description': description,
      'imageUrl': imageUrl,
      'workoutCount': workoutCount,
    };
  }

  // Create category from map
  factory WorkoutCategoryModel.fromMap(Map<String, dynamic> map) {
    return WorkoutCategoryModel(
      id: map['id'],
      name: map['name'],
      description: map['description'],
      icon: Icons.fitness_center, // Default icon
      color: Colors.blue, // Default color
      imageUrl: map['imageUrl'],
      workoutCount: map['workoutCount'] ?? 0,
    );
  }
}

/// Model representing a workout plan
class WorkoutPlanModel {
  final String id;
  final String name;
  final String description;
  final String imageUrl;
  final String duration; // e.g., "4 weeks"
  final String level; // beginner, intermediate, advanced
  final Color color;
  final List<WorkoutModel> workouts;
  final bool isPremium;

  const WorkoutPlanModel({
    required this.id,
    required this.name,
    required this.description,
    required this.imageUrl,
    required this.duration,
    required this.level,
    required this.color,
    required this.workouts,
    this.isPremium = false,
  });

  // Create a copy of the plan with modified properties
  WorkoutPlanModel copyWith({
    String? id,
    String? name,
    String? description,
    String? imageUrl,
    String? duration,
    String? level,
    Color? color,
    List<WorkoutModel>? workouts,
    bool? isPremium,
  }) {
    return WorkoutPlanModel(
      id: id ?? this.id,
      name: name ?? this.name,
      description: description ?? this.description,
      imageUrl: imageUrl ?? this.imageUrl,
      duration: duration ?? this.duration,
      level: level ?? this.level,
      color: color ?? this.color,
      workouts: workouts ?? this.workouts,
      isPremium: isPremium ?? this.isPremium,
    );
  }

  // Convert plan to map
  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'name': name,
      'description': description,
      'imageUrl': imageUrl,
      'duration': duration,
      'level': level,
      'workouts': workouts.map((w) => w.toMap()).toList(),
      'isPremium': isPremium,
    };
  }

  // Create plan from map
  factory WorkoutPlanModel.fromMap(Map<String, dynamic> map) {
    return WorkoutPlanModel(
      id: map['id'],
      name: map['name'],
      description: map['description'],
      imageUrl: map['imageUrl'],
      duration: map['duration'],
      level: map['level'],
      color: Colors.blue, // Default color
      workouts: List<WorkoutModel>.from(
        map['workouts']?.map((x) => WorkoutModel.fromMap(x)),
      ),
      isPremium: map['isPremium'] ?? false,
    );
  }
}

/// Model representing a workout history entry
class WorkoutHistoryModel {
  final String id;
  final String workoutId;
  final String workoutName;
  final DateTime date;
  final int duration; // in minutes
  final int calories;
  final IconData icon;
  final Color color;
  final bool completed;

  const WorkoutHistoryModel({
    required this.id,
    required this.workoutId,
    required this.workoutName,
    required this.date,
    required this.duration,
    required this.calories,
    required this.icon,
    required this.color,
    this.completed = true,
  });

  // Create a copy of the history entry with modified properties
  WorkoutHistoryModel copyWith({
    String? id,
    String? workoutId,
    String? workoutName,
    DateTime? date,
    int? duration,
    int? calories,
    IconData? icon,
    Color? color,
    bool? completed,
  }) {
    return WorkoutHistoryModel(
      id: id ?? this.id,
      workoutId: workoutId ?? this.workoutId,
      workoutName: workoutName ?? this.workoutName,
      date: date ?? this.date,
      duration: duration ?? this.duration,
      calories: calories ?? this.calories,
      icon: icon ?? this.icon,
      color: color ?? this.color,
      completed: completed ?? this.completed,
    );
  }

  // Convert history entry to map
  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'workoutId': workoutId,
      'workoutName': workoutName,
      'date': date.toIso8601String(),
      'duration': duration,
      'calories': calories,
      'completed': completed,
    };
  }

  // Create history entry from map
  factory WorkoutHistoryModel.fromMap(Map<String, dynamic> map) {
    return WorkoutHistoryModel(
      id: map['id'],
      workoutId: map['workoutId'],
      workoutName: map['workoutName'],
      date: DateTime.parse(map['date']),
      duration: map['duration'],
      calories: map['calories'],
      icon: Icons.fitness_center, // Default icon
      color: Colors.blue, // Default color
      completed: map['completed'] ?? true,
    );
  }

  // Get formatted date string
  String get formattedDate {
    final now = DateTime.now();
    final difference = now.difference(date);

    if (difference.inDays == 0) {
      return 'Today';
    } else if (difference.inDays == 1) {
      return 'Yesterday';
    } else if (difference.inDays < 7) {
      return '${difference.inDays} days ago';
    } else {
      return '${date.day}/${date.month}/${date.year}';
    }
  }
}
