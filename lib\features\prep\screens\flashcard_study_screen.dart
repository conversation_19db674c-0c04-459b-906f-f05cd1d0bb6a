import 'dart:math';
import 'package:flutter/material.dart';
import 'package:fit_4_force/core/theme/app_theme.dart';
import 'package:fit_4_force/features/prep/models/flashcard_model.dart';

class FlashcardStudyScreen extends StatefulWidget {
  final List<FlashcardModel> flashcards;
  final Function(String id, int level) onUpdateConfidence;

  const FlashcardStudyScreen({
    super.key,
    required this.flashcards,
    required this.onUpdateConfidence,
  });

  @override
  State<FlashcardStudyScreen> createState() => _FlashcardStudyScreenState();
}

class _FlashcardStudyScreenState extends State<FlashcardStudyScreen> with SingleTickerProviderStateMixin {
  late List<FlashcardModel> _studyDeck;
  int _currentIndex = 0;
  bool _isShowingAnswer = false;
  bool _isCompleted = false;
  int _correctCount = 0;
  int _incorrectCount = 0;
  
  // Animation controller for card flip
  late AnimationController _animationController;
  late Animation<double> _animation;
  AnimationStatus _animationStatus = AnimationStatus.dismissed;
  
  @override
  void initState() {
    super.initState();
    
    // Create a copy of the flashcards and shuffle them
    _studyDeck = List.from(widget.flashcards);
    _studyDeck.shuffle(Random());
    
    // Initialize animation controller
    _animationController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 500),
    );
    
    _animation = Tween<double>(begin: 0, end: 1).animate(_animationController)
      ..addListener(() {
        setState(() {});
      })
      ..addStatusListener((status) {
        _animationStatus = status;
        if (status == AnimationStatus.completed) {
          setState(() {
            _isShowingAnswer = true;
          });
        } else if (status == AnimationStatus.dismissed) {
          setState(() {
            _isShowingAnswer = false;
          });
        }
      });
  }
  
  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }
  
  void _flipCard() {
    if (_animationStatus == AnimationStatus.dismissed) {
      _animationController.forward();
    } else {
      _animationController.reverse();
    }
  }
  
  void _nextCard() {
    if (_currentIndex < _studyDeck.length - 1) {
      // If showing answer, flip back first
      if (_isShowingAnswer) {
        _animationController.reverse().then((_) {
          setState(() {
            _currentIndex++;
          });
        });
      } else {
        setState(() {
          _currentIndex++;
        });
      }
    } else {
      setState(() {
        _isCompleted = true;
      });
    }
  }
  
  void _previousCard() {
    if (_currentIndex > 0) {
      // If showing answer, flip back first
      if (_isShowingAnswer) {
        _animationController.reverse().then((_) {
          setState(() {
            _currentIndex--;
          });
        });
      } else {
        setState(() {
          _currentIndex--;
        });
      }
    }
  }
  
  void _rateConfidence(int level) {
    final flashcard = _studyDeck[_currentIndex];
    
    // Update confidence level
    widget.onUpdateConfidence(flashcard.id, level);
    
    // Track correct/incorrect for summary
    if (level >= 3) {
      _correctCount++;
    } else {
      _incorrectCount++;
    }
    
    // Move to next card
    _nextCard();
  }
  
  void _restartStudy() {
    setState(() {
      _studyDeck.shuffle(Random());
      _currentIndex = 0;
      _isCompleted = false;
      _correctCount = 0;
      _incorrectCount = 0;
      _isShowingAnswer = false;
    });
    
    if (_animationStatus == AnimationStatus.completed) {
      _animationController.reverse();
    }
  }
  
  @override
  Widget build(BuildContext context) {
    if (_studyDeck.isEmpty) {
      return Scaffold(
        appBar: AppBar(
          title: const Text('Study Session'),
        ),
        body: const Center(
          child: Text('No flashcards available for study.'),
        ),
      );
    }
    
    if (_isCompleted) {
      return _buildCompletionScreen();
    }
    
    return Scaffold(
      appBar: AppBar(
        title: const Text('Study Session'),
        actions: [
          TextButton.icon(
            onPressed: _restartStudy,
            icon: const Icon(Icons.refresh),
            label: const Text('Restart'),
          ),
        ],
      ),
      body: Column(
        children: [
          _buildProgressIndicator(),
          Expanded(
            child: Padding(
              padding: const EdgeInsets.all(16.0),
              child: _buildFlashcard(),
            ),
          ),
          _buildNavigationControls(),
        ],
      ),
    );
  }
  
  Widget _buildProgressIndicator() {
    return Padding(
      padding: const EdgeInsets.all(16.0),
      child: Column(
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Text(
                'Card ${_currentIndex + 1} of ${_studyDeck.length}',
                style: const TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                ),
              ),
              const SizedBox(width: 8),
              Container(
                padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                decoration: BoxDecoration(
                  color: _studyDeck[_currentIndex].color.withValues(alpha: 0.1 * 255),
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Text(
                  _studyDeck[_currentIndex].category,
                  style: TextStyle(
                    fontSize: 12,
                    fontWeight: FontWeight.w500,
                    color: _studyDeck[_currentIndex].color,
                  ),
                ),
              ),
            ],
          ),
          const SizedBox(height: 8),
          LinearProgressIndicator(
            value: (_currentIndex + 1) / _studyDeck.length,
            backgroundColor: Colors.grey.shade200,
            valueColor: AlwaysStoppedAnimation<Color>(AppTheme.primaryColor),
            borderRadius: BorderRadius.circular(10),
            minHeight: 8,
          ),
        ],
      ),
    );
  }
  
  Widget _buildFlashcard() {
    final flashcard = _studyDeck[_currentIndex];
    
    return GestureDetector(
      onTap: _flipCard,
      child: Transform(
        alignment: Alignment.center,
        transform: Matrix4.identity()
          ..setEntry(3, 2, 0.001)
          ..rotateY(pi * _animation.value),
        child: _animation.value <= 0.5
            ? _buildCardFront(flashcard)
            : Transform(
                alignment: Alignment.center,
                transform: Matrix4.identity()..rotateY(pi),
                child: _buildCardBack(flashcard),
              ),
      ),
    );
  }
  
  Widget _buildCardFront(FlashcardModel flashcard) {
    return Card(
      elevation: 4,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(20),
        side: BorderSide(
          color: flashcard.color.withValues(alpha: 0.3 * 255),
          width: 2,
        ),
      ),
      child: Container(
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(20),
          gradient: LinearGradient(
            colors: [
              Colors.white,
              flashcard.color.withValues(alpha: 0.1 * 255),
            ],
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
          ),
        ),
        padding: const EdgeInsets.all(24),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Spacer(),
            Icon(
              _getCategoryIcon(flashcard.category),
              size: 48,
              color: flashcard.color.withValues(alpha: 0.5 * 255),
            ),
            const SizedBox(height: 24),
            Text(
              flashcard.question,
              style: const TextStyle(
                fontSize: 22,
                fontWeight: FontWeight.bold,
              ),
              textAlign: TextAlign.center,
            ),
            const Spacer(),
            const Text(
              'Tap to reveal answer',
              style: TextStyle(
                fontSize: 14,
                color: Colors.grey,
              ),
            ),
          ],
        ),
      ),
    );
  }
  
  Widget _buildCardBack(FlashcardModel flashcard) {
    return Card(
      elevation: 4,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(20),
        side: BorderSide(
          color: flashcard.color.withValues(alpha: 0.3 * 255),
          width: 2,
        ),
      ),
      child: Container(
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(20),
          gradient: LinearGradient(
            colors: [
              flashcard.color.withValues(alpha: 0.2 * 255),
              Colors.white,
            ],
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
          ),
        ),
        padding: const EdgeInsets.all(24),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Text(
              'ANSWER',
              style: TextStyle(
                fontSize: 14,
                fontWeight: FontWeight.bold,
                color: Colors.grey,
                letterSpacing: 2,
              ),
            ),
            const SizedBox(height: 16),
            Expanded(
              child: SingleChildScrollView(
                child: Text(
                  flashcard.answer,
                  style: const TextStyle(
                    fontSize: 18,
                    height: 1.5,
                  ),
                  textAlign: TextAlign.center,
                ),
              ),
            ),
            const SizedBox(height: 24),
            const Text(
              'How well did you know this?',
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.w500,
              ),
            ),
            const SizedBox(height: 16),
            _buildConfidenceRating(),
          ],
        ),
      ),
    );
  }
  
  Widget _buildConfidenceRating() {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceEvenly,
      children: [
        _buildRatingButton(1, 'Not at all', Colors.red),
        _buildRatingButton(2, 'Barely', Colors.orange),
        _buildRatingButton(3, 'Somewhat', Colors.yellow.shade700),
        _buildRatingButton(4, 'Well', Colors.lightGreen),
        _buildRatingButton(5, 'Perfect', Colors.green),
      ],
    );
  }
  
  Widget _buildRatingButton(int level, String label, Color color) {
    return Column(
      children: [
        ElevatedButton(
          onPressed: () => _rateConfidence(level),
          style: ElevatedButton.styleFrom(
            backgroundColor: color,
            shape: const CircleBorder(),
            padding: const EdgeInsets.all(16),
          ),
          child: Text(
            level.toString(),
            style: const TextStyle(
              color: Colors.white,
              fontWeight: FontWeight.bold,
            ),
          ),
        ),
        const SizedBox(height: 4),
        Text(
          label,
          style: const TextStyle(
            fontSize: 12,
          ),
        ),
      ],
    );
  }
  
  Widget _buildNavigationControls() {
    return Padding(
      padding: const EdgeInsets.all(16.0),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          ElevatedButton.icon(
            onPressed: _currentIndex > 0 ? _previousCard : null,
            icon: const Icon(Icons.arrow_back),
            label: const Text('Previous'),
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.grey.shade200,
              foregroundColor: Colors.black87,
            ),
          ),
          ElevatedButton.icon(
            onPressed: _isShowingAnswer ? _nextCard : _flipCard,
            icon: Icon(_isShowingAnswer ? Icons.arrow_forward : Icons.flip),
            label: Text(_isShowingAnswer ? 'Next' : 'Flip'),
            style: ElevatedButton.styleFrom(
              backgroundColor: AppTheme.primaryColor,
              foregroundColor: Colors.white,
            ),
          ),
        ],
      ),
    );
  }
  
  Widget _buildCompletionScreen() {
    final totalCards = _correctCount + _incorrectCount;
    final percentCorrect = totalCards > 0 ? (_correctCount / totalCards * 100).round() : 0;
    
    return Scaffold(
      appBar: AppBar(
        title: const Text('Study Complete'),
      ),
      body: Center(
        child: Padding(
          padding: const EdgeInsets.all(24.0),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              const Icon(
                Icons.check_circle,
                color: Colors.green,
                size: 80,
              ),
              const SizedBox(height: 24),
              const Text(
                'Study Session Complete!',
                style: TextStyle(
                  fontSize: 24,
                  fontWeight: FontWeight.bold,
                ),
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 16),
              Text(
                'You reviewed ${_studyDeck.length} flashcards',
                style: const TextStyle(
                  fontSize: 18,
                ),
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 32),
              Container(
                padding: const EdgeInsets.all(24),
                decoration: BoxDecoration(
                  color: Colors.white,
                  borderRadius: BorderRadius.circular(16),
                  boxShadow: [
                    BoxShadow(
                      color: Colors.grey.withValues(alpha: 0.2 * 255),
                      blurRadius: 10,
                      offset: const Offset(0, 5),
                    ),
                  ],
                ),
                child: Column(
                  children: [
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                      children: [
                        _buildStatItem(
                          'Correct',
                          _correctCount.toString(),
                          Colors.green,
                          Icons.check_circle,
                        ),
                        _buildStatItem(
                          'Incorrect',
                          _incorrectCount.toString(),
                          Colors.red,
                          Icons.cancel,
                        ),
                        _buildStatItem(
                          'Accuracy',
                          '$percentCorrect%',
                          AppTheme.primaryColor,
                          Icons.analytics,
                        ),
                      ],
                    ),
                  ],
                ),
              ),
              const SizedBox(height: 32),
              ElevatedButton.icon(
                onPressed: _restartStudy,
                icon: const Icon(Icons.replay),
                label: const Text('Study Again'),
                style: ElevatedButton.styleFrom(
                  backgroundColor: AppTheme.primaryColor,
                  foregroundColor: Colors.white,
                  padding: const EdgeInsets.symmetric(
                    horizontal: 24,
                    vertical: 12,
                  ),
                ),
              ),
              const SizedBox(height: 16),
              TextButton(
                onPressed: () {
                  Navigator.pop(context);
                },
                child: const Text('Return to Flashcards'),
              ),
            ],
          ),
        ),
      ),
    );
  }
  
  Widget _buildStatItem(String label, String value, Color color, IconData icon) {
    return Column(
      children: [
        Container(
          padding: const EdgeInsets.all(12),
          decoration: BoxDecoration(
            color: color.withValues(alpha: 0.1 * 255),
            shape: BoxShape.circle,
          ),
          child: Icon(
            icon,
            color: color,
            size: 24,
          ),
        ),
        const SizedBox(height: 8),
        Text(
          value,
          style: TextStyle(
            fontSize: 20,
            fontWeight: FontWeight.bold,
            color: color,
          ),
        ),
        Text(
          label,
          style: TextStyle(
            fontSize: 14,
            color: Colors.grey.shade600,
          ),
        ),
      ],
    );
  }
  
  IconData _getCategoryIcon(String category) {
    switch (category) {
      case 'General Knowledge':
        return Icons.lightbulb_outline;
      case 'Mathematics':
        return Icons.calculate;
      case 'English':
        return Icons.menu_book;
      case 'Current Affairs':
        return Icons.public;
      case 'Physical Training':
        return Icons.fitness_center;
      case 'Agency Specific':
        return Icons.shield;
      default:
        return Icons.help_outline;
    }
  }
}
