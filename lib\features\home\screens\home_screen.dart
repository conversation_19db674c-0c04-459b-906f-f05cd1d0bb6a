import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:fit_4_force/core/config/app_routes.dart';
import 'package:fit_4_force/core/theme/app_theme.dart';
import 'package:fit_4_force/core/utils/responsive_utils.dart';
import 'package:fit_4_force/features/auth/bloc/auth_bloc.dart';
import 'package:fit_4_force/features/community/screens/community_screen.dart';
import 'package:fit_4_force/features/fitness/screens/fitness_screen.dart';
import 'package:fit_4_force/features/home/<USER>/dashboard_screen.dart';
import 'package:fit_4_force/features/prep/screens/prep_dashboard_screen.dart';
import 'package:fit_4_force/shared/models/user_model.dart';
import 'package:fit_4_force/shared/widgets/base_widget.dart';
import 'package:fit_4_force/shared/widgets/app_drawer.dart';

class HomeScreen extends StatefulWidget {
  const HomeScreen({super.key});

  @override
  State<HomeScreen> createState() => _HomeScreenState();
}

class _HomeScreenState extends State<HomeScreen> {
  int _currentIndex = 0;
  final List<String> _tabTitles = [
    'Dashboard',
    'Prep',
    'Fitness',
    'Community',
    'Profile',
  ];

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<AuthBloc, AuthState>(
      builder: (context, state) {
        if (state is Authenticated) {
          final user = state.user;
          return Scaffold(
            appBar: AppBar(
              title: Text(_tabTitles[_currentIndex]),
              actions: [
                IconButton(
                  icon: const Icon(Icons.notifications_outlined),
                  onPressed: () {
                    Navigator.of(context).pushNamed(AppRoutes.notifications);
                  },
                ),
                if (!user.isPremium)
                  TextButton.icon(
                    icon: const Icon(
                      Icons.workspace_premium,
                      color: AppTheme.premiumColor,
                    ),
                    label: const Text(
                      'UPGRADE',
                      style: TextStyle(
                        color: AppTheme.premiumColor,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    onPressed: () {
                      Navigator.of(context).pushNamed(AppRoutes.premium);
                    },
                  ),
              ],
            ),
            drawer: AppDrawer(
              user: user,
              onTabChange: (index) {
                setState(() {
                  _currentIndex = index;
                });
              },
            ),
            body: _buildBody(user),
            bottomNavigationBar: _buildResponsiveBottomNavigation(context),
          );
        }
        return const Scaffold(body: Center(child: CircularProgressIndicator()));
      },
    );
  }

  Widget _buildResponsiveBottomNavigation(BuildContext context) {
    final isLandscape = ResponsiveUtils.isLandscape(context);
    final isSmallScreen = ResponsiveUtils.isSmallPhone(context);

    return BottomNavigationBar(
      currentIndex: _currentIndex,
      onTap: (index) {
        setState(() {
          _currentIndex = index;
        });
      },
      type: BottomNavigationBarType.fixed,
      selectedFontSize: isSmallScreen ? 10.0 : (isLandscape ? 11.0 : 12.0),
      unselectedFontSize: isSmallScreen ? 9.0 : (isLandscape ? 10.0 : 11.0),
      iconSize: isSmallScreen ? 20.0 : (isLandscape ? 22.0 : 24.0),
      items: [
        BottomNavigationBarItem(
          icon: const Icon(Icons.dashboard_outlined),
          activeIcon: const Icon(Icons.dashboard),
          label: isLandscape ? 'Home' : 'Dashboard',
        ),
        BottomNavigationBarItem(
          icon: const Icon(Icons.menu_book_outlined),
          activeIcon: const Icon(Icons.menu_book),
          label: 'Prep',
        ),
        BottomNavigationBarItem(
          icon: const Icon(Icons.fitness_center_outlined),
          activeIcon: const Icon(Icons.fitness_center),
          label: 'Fitness',
        ),
        BottomNavigationBarItem(
          icon: const Icon(Icons.forum_outlined),
          activeIcon: const Icon(Icons.forum),
          label: isLandscape ? 'Chat' : 'Community',
        ),
        BottomNavigationBarItem(
          icon: const Icon(Icons.person_outline),
          activeIcon: const Icon(Icons.person),
          label: 'Profile',
        ),
      ],
    );
  }

  Widget _buildBody(UserModel user) {
    switch (_currentIndex) {
      case 0:
        return DashboardScreen(
          user: user,
          onTabChange: (index) {
            setState(() {
              _currentIndex = index;
            });
          },
        );
      case 1:
        return _buildPrepTab(user);
      case 2:
        return _buildFitnessTab(user);
      case 3:
        return _buildCommunityTab(user);
      case 4:
        return _buildProfileTab(user);
      default:
        return DashboardScreen(
          user: user,
          onTabChange: (index) {
            setState(() {
              _currentIndex = index;
            });
          },
        );
    }
  }

  Widget _buildPrepTab(UserModel user) {
    return PrepDashboardScreen(user: user);
  }

  Widget _buildFitnessTab(UserModel user) {
    // Allow all users to access the fitness screen, but with limited functionality for non-premium users
    return FitnessScreen(user: user);
  }

  Widget _buildCommunityTab(UserModel user) {
    return CommunityScreen(user: user);
  }

  Widget _buildProfileTab(UserModel user) {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Profile header
          Center(
            child: Column(
              children: [
                CircleAvatar(
                  radius: 60,
                  backgroundColor: AppTheme.primaryLightColor,
                  backgroundImage:
                      user.profileImageUrl != null
                          ? NetworkImage(user.profileImageUrl!)
                          : null,
                  child:
                      user.profileImageUrl == null
                          ? Text(
                            user.fullName.substring(0, 1).toUpperCase(),
                            style: const TextStyle(
                              fontSize: 48,
                              fontWeight: FontWeight.bold,
                              color: Colors.white,
                            ),
                          )
                          : null,
                ),
                const SizedBox(height: 16),
                Text(
                  user.fullName,
                  style: Theme.of(context).textTheme.headlineMedium?.copyWith(
                    fontWeight: FontWeight.bold,
                    color: Colors.black,
                  ),
                ),
                const SizedBox(height: 4),
                Text(
                  user.email,
                  style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                    color: Colors.black87,
                    fontWeight: FontWeight.w500,
                  ),
                ),
                const SizedBox(height: 8),
                Container(
                  padding: const EdgeInsets.symmetric(
                    horizontal: 16,
                    vertical: 8,
                  ),
                  decoration: BoxDecoration(
                    color:
                        AppTheme.agencyColors[user.targetAgency]?.withAlpha(
                          51,
                        ) ??
                        AppTheme.primaryColor.withAlpha(51),
                    borderRadius: BorderRadius.circular(20),
                  ),
                  child: Text(
                    user.targetAgency,
                    style: Theme.of(context).textTheme.labelLarge?.copyWith(
                      color:
                          AppTheme.agencyColors[user.targetAgency] ??
                          AppTheme.primaryColor,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
                if (user.isPremium) ...[
                  const SizedBox(height: 16),
                  Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      const Icon(
                        Icons.workspace_premium,
                        color: AppTheme.premiumColor,
                      ),
                      const SizedBox(width: 8),
                      Text(
                        'PREMIUM MEMBER',
                        style: Theme.of(context).textTheme.labelLarge?.copyWith(
                          color: AppTheme.premiumDarkColor,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ],
                  ),
                  if (user.premiumExpiryDate != null)
                    Text(
                      'Expires on ${_formatDate(user.premiumExpiryDate!)}',
                      style: Theme.of(context).textTheme.bodySmall,
                    ),
                ],
              ],
            ),
          ),
          const SizedBox(height: 32),

          // Personal Information
          Text(
            'Personal Information',
            style: Theme.of(context).textTheme.titleLarge?.copyWith(
              fontWeight: FontWeight.bold,
              color: Colors.black,
            ),
          ),
          const SizedBox(height: 16),
          BaseCard(
            child: Column(
              children: [
                _buildProfileInfoItem(
                  context,
                  label: 'Age',
                  value: '${user.age} years',
                  icon: Icons.calendar_today,
                ),
                const Divider(),
                _buildProfileInfoItem(
                  context,
                  label: 'Gender',
                  value: user.gender,
                  icon: Icons.person,
                ),
                const Divider(),
                _buildProfileInfoItem(
                  context,
                  label: 'Height',
                  value: '${user.height} cm',
                  icon: Icons.height,
                ),
                const Divider(),
                _buildProfileInfoItem(
                  context,
                  label: 'Weight',
                  value: '${user.weight} kg',
                  icon: Icons.monitor_weight,
                ),
                const Divider(),
                _buildProfileInfoItem(
                  context,
                  label: 'Fitness Goal',
                  value: user.fitnessGoal,
                  icon: Icons.fitness_center,
                ),
              ],
            ),
          ),
          const SizedBox(height: 24),

          // Account actions
          Text(
            'Account',
            style: Theme.of(context).textTheme.titleLarge?.copyWith(
              fontWeight: FontWeight.bold,
              color: Colors.black,
            ),
          ),
          const SizedBox(height: 16),
          BaseCard(
            child: Column(
              children: [
                _buildActionItem(
                  context,
                  label: 'Edit Profile',
                  icon: Icons.edit,
                  onTap: () {
                    // Navigate to edit profile screen
                    // Since the edit profile screen is not yet implemented, show a message
                    ScaffoldMessenger.of(context).showSnackBar(
                      const SnackBar(
                        content: Text('Edit Profile feature coming soon!'),
                      ),
                    );
                  },
                ),
                const Divider(),
                _buildActionItem(
                  context,
                  label: 'Notification Settings',
                  icon: Icons.notifications,
                  onTap: () {
                    // Navigate to notification settings
                    // Since the notification settings screen is not yet implemented, show a message
                    ScaffoldMessenger.of(context).showSnackBar(
                      const SnackBar(
                        content: Text('Notification Settings coming soon!'),
                      ),
                    );
                  },
                ),
                const Divider(),
                _buildActionItem(
                  context,
                  label: 'Change Password',
                  icon: Icons.lock,
                  onTap: () {
                    // Navigate to change password screen
                    // Since the change password screen is not yet implemented, show a message
                    ScaffoldMessenger.of(context).showSnackBar(
                      const SnackBar(
                        content: Text('Change Password feature coming soon!'),
                      ),
                    );
                  },
                ),
                const Divider(),
                _buildActionItem(
                  context,
                  label: 'Help & Support',
                  icon: Icons.help,
                  onTap: () {
                    // Navigate to help & support screen
                    // Since the help & support screen is not yet implemented, show a message
                    ScaffoldMessenger.of(context).showSnackBar(
                      const SnackBar(
                        content: Text('Help & Support coming soon!'),
                      ),
                    );
                  },
                ),
                const Divider(),
                _buildActionItem(
                  context,
                  label: 'Sign Out',
                  icon: Icons.logout,
                  color: Colors.red,
                  onTap: () {
                    _showSignOutDialog(context);
                  },
                ),
              ],
            ),
          ),
          const SizedBox(height: 32),

          // App information
          Center(
            child: Column(
              children: [
                Text(
                  'Fit4Force',
                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.bold,
                    color: Colors.black,
                  ),
                ),
                const SizedBox(height: 4),
                Text(
                  'Version 1.0.0',
                  style: Theme.of(context).textTheme.bodySmall?.copyWith(
                    color: Colors.black87,
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ],
            ),
          ),
          const SizedBox(height: 32),
        ],
      ),
    );
  }

  Widget _buildProfileInfoItem(
    BuildContext context, {
    required String label,
    required String value,
    required IconData icon,
  }) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 12),
      child: Row(
        children: [
          Icon(icon, color: AppTheme.primaryColor),
          const SizedBox(width: 16),
          Expanded(
            child: Text(
              label,
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                color: Colors.black87,
                fontWeight: FontWeight.w500,
              ),
            ),
          ),
          Text(
            value,
            style: Theme.of(context).textTheme.bodyLarge?.copyWith(
              fontWeight: FontWeight.w500,
              color: Colors.black87,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildActionItem(
    BuildContext context, {
    required String label,
    required IconData icon,
    required VoidCallback onTap,
    Color? color,
  }) {
    return InkWell(
      onTap: onTap,
      child: Padding(
        padding: const EdgeInsets.symmetric(vertical: 12),
        child: Row(
          children: [
            Icon(icon, color: color ?? AppTheme.primaryColor),
            const SizedBox(width: 16),
            Expanded(
              child: Text(
                label,
                style: Theme.of(context).textTheme.titleMedium?.copyWith(
                  color: color ?? Colors.black87,
                  fontWeight: FontWeight.w500,
                ),
              ),
            ),
            Icon(Icons.chevron_right, color: Colors.grey[600]),
          ],
        ),
      ),
    );
  }

  void _showSignOutDialog(BuildContext context) {
    showDialog(
      context: context,
      builder:
          (context) => AlertDialog(
            title: const Text('Sign Out'),
            content: const Text('Are you sure you want to sign out?'),
            actions: [
              TextButton(
                onPressed: () {
                  Navigator.of(context).pop();
                },
                child: const Text('Cancel'),
              ),
              TextButton(
                onPressed: () {
                  Navigator.of(context).pop();
                  context.read<AuthBloc>().add(SignOutEvent());
                },
                child: const Text(
                  'Sign Out',
                  style: TextStyle(color: Colors.red),
                ),
              ),
            ],
          ),
    );
  }

  String _formatDate(DateTime date) {
    final months = [
      'January',
      'February',
      'March',
      'April',
      'May',
      'June',
      'July',
      'August',
      'September',
      'October',
      'November',
      'December',
    ];
    return '${months[date.month - 1]} ${date.day}, ${date.year}';
  }
}
