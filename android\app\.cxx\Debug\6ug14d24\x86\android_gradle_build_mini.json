{"buildFiles": ["C:\\Flutter\\Soc\\flutter\\packages\\flutter_tools\\gradle\\src\\main\\groovy\\CMakeLists.txt"], "cleanCommandsComponents": [["C:\\Users\\<USER>\\AppData\\Local\\Android\\sdk\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "C:\\Flutter\\Soc\\Fit_4_force\\android\\app\\.cxx\\Debug\\6ug14d24\\x86", "clean"]], "buildTargetsCommandComponents": ["C:\\Users\\<USER>\\AppData\\Local\\Android\\sdk\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "C:\\Flutter\\Soc\\Fit_4_force\\android\\app\\.cxx\\Debug\\6ug14d24\\x86", "{LIST_OF_TARGETS_TO_BUILD}"], "libraries": {}}