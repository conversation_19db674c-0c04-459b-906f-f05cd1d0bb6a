import 'dart:typed_data';
import 'package:flutter/foundation.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import 'package:fit_4_force/core/config/supabase_config.dart';
import 'package:logger/logger.dart';
import 'package:image/image.dart' as img;

/// User-only storage service for Fit4Force (no admin upload functionality)
/// All educational content is managed through Supabase dashboard
class UserStorageService {
  static final UserStorageService _instance = UserStorageService._internal();
  factory UserStorageService() => _instance;
  UserStorageService._internal();

  final Logger _logger = Logger();

  /// Get Supabase client
  // For production, use the real Supabase client. For tests, inject a mock if needed.
  SupabaseClient get _client => SupabaseConfig.client;

  /// Storage bucket names (read-only for most)
  static const String profileImagesBucket = 'profile-images';
  static const String workoutImagesBucket = 'workout-images';
  static const String postImagesBucket = 'post-images';
  static const String studyMaterialsBucket = 'study-materials';
  static const String exerciseVideosBucket = 'exercise-videos';

  /// Upload profile image (user-only functionality)
  Future<String?> uploadProfileImage(
    String userId,
    Uint8List imageData,
    String fileName, {
    Function(double)? onProgress,
  }) async {
    try {
      // Validate authentication
      final currentUser = _client.auth.currentUser;
      if (currentUser == null) {
        throw Exception('User not authenticated');
      }

      // Validate user can upload their own profile image
      if (currentUser.id != userId) {
        throw Exception('Unauthorized: Can only upload your own profile image');
      }

      // Validate file size (5MB limit for profile images)
      if (imageData.length > 5 * 1024 * 1024) {
        throw Exception('File size exceeds 5MB limit');
      }

      // Validate file type
      if (!_isValidImageFile(fileName)) {
        throw Exception(
          'Invalid file type. Only JPG, PNG, and WebP are allowed',
        );
      }

      onProgress?.call(0.1); // Start progress

      // Compress image for profile
      final compressedData = await _compressImage(
        imageData,
        maxWidth: 400,
        maxHeight: 400,
        quality: 85,
      );

      onProgress?.call(0.5); // Compression complete

      final timestamp = DateTime.now().millisecondsSinceEpoch;
      final fileExtension = fileName.split('.').last.toLowerCase();
      final filePath =
          'users/$userId/profile/${timestamp}_profile.$fileExtension';

      // Upload to Supabase Storage
      await _client.storage
          .from(profileImagesBucket)
          .uploadBinary(
            filePath,
            compressedData,
            fileOptions: FileOptions(
              cacheControl: '3600',
              upsert: true,
              contentType: _getContentType(fileExtension),
            ),
          );

      onProgress?.call(0.9); // Upload complete

      // Get public URL
      final publicUrl = _client.storage
          .from(profileImagesBucket)
          .getPublicUrl(filePath);

      // Update user profile with new image URL
      await _updateUserProfileImage(userId, publicUrl);

      _logger.i('✅ Profile image uploaded: $filePath');
      onProgress?.call(1.0); // Complete
      return publicUrl;
    } catch (e) {
      _logger.e('❌ Error uploading profile image: $e');
      return null;
    }
  }

  /// Upload community post images (user functionality)
  Future<String?> uploadPostImage(
    String postId,
    Uint8List imageData,
    String fileName, {
    Function(double)? onProgress,
  }) async {
    try {
      // Validate authentication
      final currentUser = _client.auth.currentUser;
      if (currentUser == null) {
        throw Exception('User not authenticated');
      }

      // Validate file size (10MB limit for post images)
      if (imageData.length > 10 * 1024 * 1024) {
        throw Exception('File size exceeds 10MB limit');
      }

      // Validate file type
      if (!_isValidImageFile(fileName)) {
        throw Exception(
          'Invalid file type. Only JPG, PNG, and WebP are allowed',
        );
      }

      onProgress?.call(0.1); // Start progress

      // Compress image for posts
      final compressedData = await _compressImage(
        imageData,
        maxWidth: 1200,
        maxHeight: 800,
        quality: 75,
      );

      onProgress?.call(0.5); // Compression complete

      final timestamp = DateTime.now().millisecondsSinceEpoch;
      final fileExtension = fileName.split('.').last.toLowerCase();
      final filePath =
          'posts/$postId/${timestamp}_${_sanitizeFileName(fileName)}';

      // Upload to Supabase Storage
      await _client.storage
          .from(postImagesBucket)
          .uploadBinary(
            filePath,
            compressedData,
            fileOptions: FileOptions(
              cacheControl: '3600',
              upsert: true,
              contentType: _getContentType(fileExtension),
            ),
          );

      onProgress?.call(0.9); // Upload complete

      final publicUrl = _client.storage
          .from(postImagesBucket)
          .getPublicUrl(filePath);

      _logger.i('✅ Post image uploaded: $filePath');
      onProgress?.call(1.0); // Complete
      return publicUrl;
    } catch (e) {
      _logger.e('❌ Error uploading post image: $e');
      return null;
    }
  }

  /// Get study materials for user's target agency (read-only access)
  /// All study materials are uploaded via Supabase dashboard by admins
  /// Users only see content for their selected agency due to RLS policies
  Future<List<Map<String, dynamic>>> getStudyMaterials({
    String? sectionName,
    String? contentType,
    bool? isPremiumOnly,
    String? difficulty,
    int limit = 50,
  }) async {
    try {
      // Start with base query and build all filters first
      var filterQuery = _client
          .from('study_materials')
          .select()
          .eq('is_active', true)
          .not('published_at', 'is', null)
          .lte('published_at', DateTime.now().toIso8601String());

      // Add optional filters
      if (sectionName != null) {
        filterQuery = filterQuery.eq('content_sections.name', sectionName);
      }
      if (contentType != null) {
        filterQuery = filterQuery.eq('content_type', contentType);
      }
      if (isPremiumOnly != null) {
        filterQuery = filterQuery.eq('is_premium', isPremiumOnly);
      }
      if (difficulty != null) {
        filterQuery = filterQuery.eq('difficulty', difficulty);
      }

      // Apply ordering and pagination
      final results = await filterQuery
          .order('created_at', ascending: false)
          .limit(limit);

      return List<Map<String, dynamic>>.from(results as List);
    } catch (e) {
      _logger.e('❌ Error fetching study materials: $e');
      return [];
    }
  }

  /// Get study materials by section for user's agency
  Future<List<Map<String, dynamic>>> getStudyMaterialsBySection(
    String sectionName,
  ) async {
    return await getStudyMaterials(sectionName: sectionName);
  }

  /// Get available content sections for user's target agency
  Future<List<Map<String, dynamic>>> getContentSections() async {
    try {
      final response = await _client
          .from('content_sections')
          .select('''
            *,
            agencies!inner(name, code)
          ''')
          .eq('is_active', true)
          .order('display_order', ascending: true);

      return List<Map<String, dynamic>>.from(response);
    } catch (e) {
      _logger.e('❌ Error getting content sections: $e');
      return [];
    }
  }

  /// Get user's target agency information
  Future<Map<String, dynamic>?> getUserTargetAgency() async {
    try {
      final currentUser = _client.auth.currentUser;
      if (currentUser == null) return null;

      final response =
          await _client
              .from('user_preferences')
              .select('''
            *,
            agencies!target_agency_id(*)
          ''')
              .eq('user_id', currentUser.id)
              .single();

      return response;
    } catch (e) {
      _logger.e('❌ Error getting user target agency: $e');
      return null;
    }
  }

  /// Update user's target agency
  Future<bool> updateUserTargetAgency(String agencyCode) async {
    try {
      final currentUser = _client.auth.currentUser;
      if (currentUser == null) return false;

      // Get agency ID from code
      final agencyResponse =
          await _client.from('agencies').select('id')
              .eq('code', agencyCode)
              .single();

      final agencyId = agencyResponse['id'];

      // Update or insert user preferences
      await _client.from('user_preferences').upsert({
        'user_id': currentUser.id,
        'target_agency_id': agencyId,
        'updated_at': DateTime.now().toIso8601String(),
      });

      _logger.i('✅ Updated user target agency to: $agencyCode');
      return true;
    } catch (e) {
      _logger.e('❌ Error updating user target agency: $e');
      return false;
    }
  }

  /// Get workout images (read-only access)
  /// All workout images are uploaded via Supabase dashboard by admins
  Future<List<Map<String, dynamic>>> getWorkoutImages({
    String? workoutId,
    int limit = 20,
  }) async {
    try {
      String path = 'workouts/';
      if (workoutId != null) {
        path += '$workoutId/';
      }

      final files = await _client.storage
          .from(workoutImagesBucket)
          .list(path: path);

      return files
          .map(
            (file) => {
              'name': file.name,
              'url': _client.storage
                  .from(workoutImagesBucket)
                  .getPublicUrl('$path${file.name}'),
              'size': file.metadata?['size'],
              'created_at': file.createdAt,
            },
          )
          .toList();
    } catch (e) {
      _logger.e('❌ Error getting workout images: $e');
      return [];
    }
  }

  /// Get exercise videos (read-only access)
  /// All exercise videos are uploaded via Supabase dashboard by admins
  Future<List<Map<String, dynamic>>> getExerciseVideos({
    String? exerciseId,
    int limit = 20,
  }) async {
    try {
      String path = 'exercises/';
      if (exerciseId != null) {
        path += '$exerciseId/videos/';
      }

      final files = await _client.storage
          .from(exerciseVideosBucket)
          .list(path: path);

      return files
          .map(
            (file) => {
              'name': file.name,
              'url': _client.storage
                  .from(exerciseVideosBucket)
                  .getPublicUrl('$path${file.name}'),
              'size': file.metadata?['size'],
              'created_at': file.createdAt,
            },
          )
          .toList();
    } catch (e) {
      _logger.e('❌ Error getting exercise videos: $e');
      return [];
    }
  }

  /// Delete user's own files only
  Future<bool> deleteUserFile(
    String bucketName,
    String filePath,
    String userId,
  ) async {
    try {
      final currentUser = _client.auth.currentUser;
      if (currentUser == null || currentUser.id != userId) {
        throw Exception('Unauthorized');
      }

      // Only allow deletion of user's own files
      if (!filePath.contains('users/$userId/')) {
        throw Exception('Can only delete your own files');
      }

      await _client.storage.from(bucketName).remove([filePath]);
      _logger.i('✅ User file deleted: $bucketName/$filePath');
      return true;
    } catch (e) {
      _logger.e('❌ Error deleting user file: $e');
      return false;
    }
  }

  /// Track user progress on study material
  Future<bool> updateStudyProgress({
    required String materialId,
    required String
    status, // 'not_started', 'in_progress', 'completed', 'bookmarked'
    int? progressPercentage,
    int? timeSpent,
    int? quizScore,
    int? quizTotal,
  }) async {
    try {
      final currentUser = _client.auth.currentUser;
      if (currentUser == null) return false;

      final progressData = <String, dynamic>{
        'user_id': currentUser.id,
        'material_id': materialId,
        'status': status,
        'last_accessed': DateTime.now().toIso8601String(),
        'updated_at': DateTime.now().toIso8601String(),
      };

      if (progressPercentage != null) {
        progressData['progress_percentage'] = progressPercentage;
      }

      if (timeSpent != null) {
        progressData['time_spent'] = timeSpent;
      }

      if (quizScore != null && quizTotal != null) {
        progressData['quiz_score'] = quizScore;
        progressData['quiz_total'] = quizTotal;

        // Update attempts count
        final existingProgress =
            await _client
                .from('user_study_progress')
                .select('quiz_attempts, best_score')
                .match({'user_id': currentUser.id, 'material_id': materialId})
                .maybeSingle();

        if (existingProgress != null) {
          final currentAttempts = existingProgress['quiz_attempts'] ?? 0;
          final currentBestScore = existingProgress['best_score'] ?? 0;

          progressData['quiz_attempts'] = currentAttempts + 1;
          progressData['best_score'] =
              quizScore > currentBestScore ? quizScore : currentBestScore;
        } else {
          progressData['quiz_attempts'] = 1;
          progressData['best_score'] = quizScore;
        }
      }

      if (status == 'not_started') {
        progressData['started_at'] = DateTime.now().toIso8601String();
      } else if (status == 'completed') {
        progressData['completed_at'] = DateTime.now().toIso8601String();
      }

      await _client.from('user_study_progress').upsert(progressData);

      _logger.i('✅ Study progress updated for material: $materialId');
      return true;
    } catch (e) {
      _logger.e('❌ Error updating study progress: $e');
      return false;
    }
  }

  /// Get user's progress for a specific material
  Future<Map<String, dynamic>?> getStudyProgress(String materialId) async {
    try {
      final currentUser = _client.auth.currentUser;
      if (currentUser == null) return null;

      final response =
          await _client.from('user_study_progress').select('*').match({
            'user_id': currentUser.id,
            'material_id': materialId,
          }).maybeSingle();

      return response;
    } catch (e) {
      _logger.e('❌ Error getting study progress: $e');
      return null;
    }
  }

  /// Rate a study material
  Future<bool> rateStudyMaterial({
    required String materialId,
    required int rating, // 1-5
    String? review,
  }) async {
    try {
      final currentUser = _client.auth.currentUser;
      if (currentUser == null) return false;

      if (rating < 1 || rating > 5) {
        throw Exception('Rating must be between 1 and 5');
      }

      await _client.from('content_ratings').upsert({
        'user_id': currentUser.id,
        'material_id': materialId,
        'rating': rating,
        'review': review,
        'updated_at': DateTime.now().toIso8601String(),
      });

      _logger.i('✅ Material rated: $materialId with $rating stars');
      return true;
    } catch (e) {
      _logger.e('❌ Error rating material: $e');
      return false;
    }
  }

  /// Get user's rating for a material
  Future<Map<String, dynamic>?> getUserRating(String materialId) async {
    try {
      final currentUser = _client.auth.currentUser;
      if (currentUser == null) return null;

      final response =
          await _client
              .from('content_ratings')
              .select('*')
              .filter('user_id', 'eq', currentUser.id)
              .filter('material_id', 'eq', materialId)
              .maybeSingle();

      return response;
    } catch (e) {
      _logger.e('❌ Error getting user rating: $e');
      return null;
    }
  }

  // Helper Methods

  /// Validate image file types
  bool _isValidImageFile(String fileName) {
    final extension = fileName.split('.').last.toLowerCase();
    return ['jpg', 'jpeg', 'png', 'webp'].contains(extension);
  }

  /// Get content type for file extension
  String _getContentType(String extension) {
    switch (extension.toLowerCase()) {
      case 'jpg':
      case 'jpeg':
        return 'image/jpeg';
      case 'png':
        return 'image/png';
      case 'webp':
        return 'image/webp';
      default:
        return 'application/octet-stream';
    }
  }

  /// Sanitize file name for storage
  String _sanitizeFileName(String fileName) {
    return fileName
        .replaceAll(RegExp(r'[^a-zA-Z0-9._-]'), '_')
        .replaceAll(RegExp(r'_{2,}'), '_');
  }

  /// Compress image for optimal storage
  Future<Uint8List> _compressImage(
    Uint8List imageData, {
    int maxWidth = 1200,
    int maxHeight = 800,
    int quality = 80,
  }) async {
    try {
      final image = img.decodeImage(imageData);
      if (image == null) return imageData;

      // Resize if needed
      img.Image resized = image;
      if (image.width > maxWidth || image.height > maxHeight) {
        resized = img.copyResize(
          image,
          width: image.width > maxWidth ? maxWidth : null,
          height: image.height > maxHeight ? maxHeight : null,
          maintainAspect: true,
        );
      }

      // Compress as JPEG
      final compressedData = img.encodeJpg(resized, quality: quality);

      _logger.d(
        '🗜️ Image compressed: ${imageData.length} -> ${compressedData.length} bytes',
      );
      return Uint8List.fromList(compressedData);
    } catch (e) {
      _logger.w('⚠️ Image compression failed, using original: $e');
      return imageData;
    }
  }

  /// Update user profile image URL in database
  Future<void> _updateUserProfileImage(String userId, String imageUrl) async {
    try {
      await _client.from('users').update({'profile_image_url': imageUrl}).match(
        {'id': userId},
      );
    } catch (e) {
      _logger.e('❌ Error updating user profile image: $e');
    }
  }
}
