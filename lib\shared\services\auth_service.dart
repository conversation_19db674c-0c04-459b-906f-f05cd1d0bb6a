import 'package:fit_4_force/shared/models/user_model.dart';
import 'base_service.dart';

class AuthService extends BaseService {
  @override
  String get collectionName => 'users';

  // Sign up with email and password
  Future<UserModel> signUp({
    required String email,
    required String password,
    required String fullName,
    required int age,
    required String gender,
    required double height,
    required double weight,
    required String targetAgency,
    required String fitnessGoal,
    required Map<String, bool> notificationPreferences,
  }) async {
    try {
      // Create a mock user ID
      final userId = DateTime.now().millisecondsSinceEpoch.toString();

      // Create user profile
      final user = UserModel(
        id: userId,
        createdAt: DateTime.now(),
        fullName: fullName,
        email: email,
        age: age,
        gender: gender,
        height: height,
        weight: weight,
        targetAgency: targetAgency,
        fitnessGoal: fitnessGoal,
        isPremium: false,
        notificationPreferences: notificationPreferences,
        completedQuizzes: [],
        savedWorkouts: [],
      );

      // Save user data (mock implementation)
      await create(user.toJson());

      return user;
    } catch (e) {
      rethrow;
    }
  }

  // Sign in with email and password
  Future<UserModel> signIn({
    required String email,
    required String password,
  }) async {
    try {
      // This is a mock implementation
      // In a real app, this would authenticate with Supabase or another backend

      // Create a mock user for testing
      final user = UserModel(
        id: '123456789',
        createdAt: DateTime.now(),
        fullName: 'Test User',
        email: email,
        age: 25,
        gender: 'Male',
        height: 175.0,
        weight: 70.0,
        targetAgency: 'Nigerian Army',
        fitnessGoal: 'Pass fitness test',
        isPremium: false,
        notificationPreferences: {'push': true, 'email': true},
        completedQuizzes: [],
        savedWorkouts: [],
      );

      return user;
    } catch (e) {
      rethrow;
    }
  }

  // Sign out
  Future<void> signOut() async {
    try {
      // This is a mock implementation
      // In a real app, this would sign out from Supabase or another backend
      print('User signed out');
    } catch (e) {
      rethrow;
    }
  }

  // Reset password
  Future<void> resetPassword(String email) async {
    try {
      // This is a mock implementation
      // In a real app, this would send a password reset email
      print('Password reset email sent to $email');
    } catch (e) {
      rethrow;
    }
  }

  // Update user profile
  Future<void> updateProfile(UserModel user) async {
    try {
      await update(user.id, user.toJson());
    } catch (e) {
      rethrow;
    }
  }

  // Get current user profile
  Future<UserModel?> getCurrentUser() async {
    try {
      if (!isAuthenticated) return null;

      final userData = await getById(currentUserId!);
      if (userData == null) return null;

      return UserModel.fromJson(userData);
    } catch (e) {
      rethrow;
    }
  }

  // Update premium status
  Future<void> updatePremiumStatus(
    String userId,
    bool isPremium,
    DateTime expiryDate,
  ) async {
    try {
      await update(userId, {
        'isPremium': isPremium,
        'premiumExpiryDate': expiryDate.toIso8601String(),
      });
    } catch (e) {
      rethrow;
    }
  }

  // Stream user profile changes
  Stream<UserModel?> streamUserProfile(String userId) {
    // This is a mock implementation
    // In a real app, this would stream user profile changes from Supabase or another backend
    return Stream.value(
      UserModel(
        id: userId,
        createdAt: DateTime.now(),
        fullName: 'Test User',
        email: '<EMAIL>',
        age: 25,
        gender: 'Male',
        height: 175.0,
        weight: 70.0,
        targetAgency: 'Nigerian Army',
        fitnessGoal: 'Pass fitness test',
        isPremium: false,
        notificationPreferences: {'push': true, 'email': true},
        completedQuizzes: [],
        savedWorkouts: [],
      ),
    );
  }

  // Update user email
  Future<void> updateEmail(String newEmail, String password) async {
    try {
      // This is a mock implementation
      // In a real app, this would update the user's email in Supabase or another backend

      // Mock implementation - just log the action
      print('Email updated to $newEmail');
    } catch (e) {
      rethrow;
    }
  }
}
