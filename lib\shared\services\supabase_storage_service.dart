import 'dart:typed_data';
import 'package:fit_4_force/core/config/supabase_config.dart';
import 'package:flutter/foundation.dart';
import 'package:logger/logger.dart';
import 'package:path/path.dart' as path;
import 'package:supabase_flutter/supabase_flutter.dart';
import 'package:uuid/uuid.dart';

/// Service for handling file storage operations with Supabase
class SupabaseStorageService {
  final Logger _logger = Logger();
  final SupabaseClient _client = SupabaseConfig.client;
  final Uuid _uuid = const Uuid();

  /// Upload a file to storage
  Future<String> uploadFile({
    required String bucketName,
    required String filePath,
    required List<int> bytes,
    String? contentType,
  }) async {
    try {
      final fileName = path.basename(filePath);
      final fileExtension = path.extension(filePath);
      final uniqueFileName = '${_uuid.v4()}$fileExtension';
      
      await _client.storage.from(bucketName).uploadBinary(
        uniqueFileName,
        Uint8List.fromList(bytes),
        fileOptions: FileOptions(
          contentType: contentType,
        ),
      );
      
      // Get public URL
      final publicUrl = _client.storage.from(bucketName).getPublicUrl(uniqueFileName);
      
      return publicUrl;
    } catch (e) {
      _logger.e('Error uploading file: $e');
      rethrow;
    }
  }

  /// Upload a profile image
  Future<String> uploadProfileImage({
    required String userId,
    required List<int> bytes,
  }) async {
    try {
      final fileExtension = '.jpg'; // Assuming JPEG format for profile images
      final uniqueFileName = '$userId/profile$fileExtension';
      
      await _client.storage.from('profiles').uploadBinary(
        uniqueFileName,
        Uint8List.fromList(bytes),
        fileOptions: const FileOptions(
          contentType: 'image/jpeg',
        ),
      );
      
      // Get public URL
      final publicUrl = _client.storage.from('profiles').getPublicUrl(uniqueFileName);
      
      return publicUrl;
    } catch (e) {
      _logger.e('Error uploading profile image: $e');
      rethrow;
    }
  }

  /// Delete a file from storage
  Future<void> deleteFile({
    required String bucketName,
    required String filePath,
  }) async {
    try {
      await _client.storage.from(bucketName).remove([filePath]);
    } catch (e) {
      _logger.e('Error deleting file: $e');
      rethrow;
    }
  }

  /// Get a file from storage
  Future<Uint8List> getFile({
    required String bucketName,
    required String filePath,
  }) async {
    try {
      final bytes = await _client.storage.from(bucketName).download(filePath);
      return bytes;
    } catch (e) {
      _logger.e('Error getting file: $e');
      rethrow;
    }
  }

  /// Get a public URL for a file
  String getPublicUrl({
    required String bucketName,
    required String filePath,
  }) {
    try {
      return _client.storage.from(bucketName).getPublicUrl(filePath);
    } catch (e) {
      _logger.e('Error getting public URL: $e');
      rethrow;
    }
  }

  /// Create a signed URL for a file (with expiration)
  Future<String> createSignedUrl({
    required String bucketName,
    required String filePath,
    required int expiresIn,
  }) async {
    try {
      final signedUrl = await _client.storage.from(bucketName).createSignedUrl(
        filePath,
        expiresIn,
      );
      return signedUrl;
    } catch (e) {
      _logger.e('Error creating signed URL: $e');
      rethrow;
    }
  }
}
