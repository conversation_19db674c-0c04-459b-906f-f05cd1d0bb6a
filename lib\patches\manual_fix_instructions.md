# Manual Fix Instructions for Flutter Paystack Package

The Flutter Paystack package is using deprecated APIs that have been removed in newer Flutter versions. You need to manually fix the following files:

## 1. Fix checkout_widget.dart

File path: `%LOCALAPPDATA%\Pub\Cache\git\flutter_paystack-a4a33c3dd0a12f46d655a2e63d11e9f20ba82d01\lib\src\widgets\checkout\checkout_widget.dart`

Make the following changes:

1. Replace `bodyText1` with `bodyLarge`
   - Find: `color: Theme.of(context).textTheme.bodyText1!.color,`
   - Replace with: `color: Theme.of(context).textTheme.bodyLarge!.color,`

2. Remove `vsync: this` parameter from AnimatedSize
   - Find: `AnimatedSize(vsync: this,`
   - Replace with: `AnimatedSize(`

## 2. Fix buttons.dart

File path: `%LOCALAPPDATA%\Pub\Cache\git\flutter_paystack-a4a33c3dd0a12f46d655a2e63d11e9f20ba82d01\lib\src\widgets\buttons.dart`

Make the following changes:

1. Replace `accentColor` with `colorScheme.secondary`
   - Find: `color: Theme.of(context).accentColor,`
   - Replace with: `color: Theme.of(context).colorScheme.secondary,`

## How to Apply the Fixes

1. Open the files in a text editor
2. Make the changes as described above
3. Save the files
4. Run `flutter pub get` to update the package

## Alternative: Use a Different Paystack Package

If you're having trouble fixing the Flutter Paystack package, you can consider using a different package:

1. [paystack_manager](https://pub.dev/packages/paystack_manager)
2. [flutter_paystack_plus](https://pub.dev/packages/flutter_paystack_plus)

These packages are maintained and compatible with newer Flutter versions.
