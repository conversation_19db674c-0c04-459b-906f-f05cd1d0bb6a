import 'package:flutter/material.dart';
import 'package:fit_4_force/core/theme/app_theme.dart';

/// Utility class for ensuring text accessibility and readability
class TextAccessibility {
  TextAccessibility._();

  /// WCAG AA standard contrast ratio (4.5:1)
  static const double _minContrastRatio = 4.5;

  /// WCAG AAA standard contrast ratio (7:1)
  static const double _preferredContrastRatio = 7.0;

  /// Calculate luminance of a color
  static double _getLuminance(Color color) {
    final r = color.r / 255.0;
    final g = color.g / 255.0;
    final b = color.b / 255.0;

    final rLum = r <= 0.03928 ? r / 12.92 : pow((r + 0.055) / 1.055, 2.4);
    final gLum = g <= 0.03928 ? g / 12.92 : pow((g + 0.055) / 1.055, 2.4);
    final bLum = b <= 0.03928 ? b / 12.92 : pow((b + 0.055) / 1.055, 2.4);

    return 0.2126 * rLum + 0.7152 * gLum + 0.0722 * bLum;
  }

  /// Calculate contrast ratio between two colors
  static double getContrastRatio(Color color1, Color color2) {
    final lum1 = _getLuminance(color1);
    final lum2 = _getLuminance(color2);
    final lighter = lum1 > lum2 ? lum1 : lum2;
    final darker = lum1 > lum2 ? lum2 : lum1;
    return (lighter + 0.05) / (darker + 0.05);
  }

  /// Check if text color has sufficient contrast against background
  static bool hasGoodContrast(Color textColor, Color backgroundColor) {
    return getContrastRatio(textColor, backgroundColor) >= _minContrastRatio;
  }

  /// Get the best text color (black or white) for a given background
  static Color getBestTextColor(Color backgroundColor) {
    final whiteContrast = getContrastRatio(Colors.white, backgroundColor);
    final blackContrast = getContrastRatio(Colors.black, backgroundColor);
    return whiteContrast > blackContrast ? Colors.white : Colors.black;
  }

  /// Get accessible text color with fallback
  static Color getAccessibleTextColor({
    required Color backgroundColor,
    Color? preferredColor,
    bool forceHighContrast = false,
  }) {
    final contrastThreshold =
        forceHighContrast ? _preferredContrastRatio : _minContrastRatio;

    if (preferredColor != null) {
      final contrast = getContrastRatio(preferredColor, backgroundColor);
      if (contrast >= contrastThreshold) {
        return preferredColor;
      }
    }

    return getBestTextColor(backgroundColor);
  }

  /// Enhanced text style with accessibility considerations
  static TextStyle getAccessibleTextStyle({
    required BuildContext context,
    required Color backgroundColor,
    Color? preferredColor,
    double? fontSize,
    FontWeight? fontWeight,
    bool forceHighContrast = false,
    bool addShadow = false,
  }) {
    final textColor = getAccessibleTextColor(
      backgroundColor: backgroundColor,
      preferredColor: preferredColor,
      forceHighContrast: forceHighContrast,
    );

    return TextStyle(
      color: textColor,
      fontSize: fontSize,
      fontWeight: fontWeight,
      shadows:
          addShadow
              ? [
                Shadow(
                  color:
                      textColor == Colors.white
                          ? Colors.black54
                          : Colors.white54,
                  blurRadius: 2.0,
                  offset: const Offset(0, 1),
                ),
              ]
              : null,
    );
  }

  /// Get text color for gradient backgrounds
  static Color getTextColorForGradient(List<Color> gradientColors) {
    // Calculate average color of gradient
    double totalRed = 0, totalGreen = 0, totalBlue = 0;
    for (final color in gradientColors) {
      totalRed += color.r;
      totalGreen += color.g;
      totalBlue += color.b;
    }

    final avgColor = Color.fromRGBO(
      (totalRed / gradientColors.length).round(),
      (totalGreen / gradientColors.length).round(),
      (totalBlue / gradientColors.length).round(),
      1.0,
    );

    return getBestTextColor(avgColor);
  }

  /// Predefined accessible color combinations
  static const Map<String, Map<String, Color>> accessibleCombinations = {
    'primary': {
      'background': AppTheme.primaryColor,
      'text': Colors.white,
      'secondary': Color(0xFFE3F2FD),
    },
    'secondary': {
      'background': AppTheme.secondaryColor,
      'text': Colors.white,
      'secondary': Color(0xFFF3E5F5),
    },
    'success': {
      'background': AppTheme.successColor,
      'text': Colors.white,
      'secondary': Color(0xFFE8F5E8),
    },
    'warning': {
      'background': AppTheme.warningColor,
      'text': Colors.black,
      'secondary': Color(0xFFFFF8E1),
    },
    'error': {
      'background': AppTheme.errorColor,
      'text': Colors.white,
      'secondary': Color(0xFFFFEBEE),
    },
    'premium': {
      'background': AppTheme.premiumColor,
      'text': Colors.black,
      'secondary': Color(0xFFFFF8E1),
    },
  };

  /// Get accessible colors for a theme
  static Map<String, Color> getAccessibleColors(String theme) {
    return accessibleCombinations[theme] ?? accessibleCombinations['primary']!;
  }

  /// Create text with guaranteed readability
  static Widget createReadableText(
    String text, {
    required Color backgroundColor,
    Color? preferredColor,
    double? fontSize,
    FontWeight? fontWeight,
    TextAlign? textAlign,
    int? maxLines,
    TextOverflow? overflow,
    bool forceHighContrast = false,
    bool addShadow = false,
  }) {
    final textColor = getAccessibleTextColor(
      backgroundColor: backgroundColor,
      preferredColor: preferredColor,
      forceHighContrast: forceHighContrast,
    );

    return Text(
      text,
      style: TextStyle(
        color: textColor,
        fontSize: fontSize,
        fontWeight: fontWeight,
        shadows:
            addShadow
                ? [
                  Shadow(
                    color:
                        textColor == Colors.white
                            ? Colors.black54
                            : Colors.white54,
                    blurRadius: 2.0,
                    offset: const Offset(0, 1),
                  ),
                ]
                : null,
      ),
      textAlign: textAlign,
      maxLines: maxLines,
      overflow: overflow,
    );
  }
}

/// Helper function for power calculation
double pow(double base, double exponent) {
  if (exponent == 0) return 1.0;
  if (exponent == 1) return base;
  if (exponent == 2) return base * base;
  if (exponent == 2.4) {
    // Approximation for gamma correction
    final squared = base * base;
    return squared * pow(base, 0.4);
  }
  // Simple approximation for other cases
  double result = 1.0;
  for (int i = 0; i < exponent.floor(); i++) {
    result *= base;
  }
  return result;
}
