import 'package:fit_4_force/features/stats/models/user_stats.dart';
import 'package:fit_4_force/features/training/models/fitness_assessment.dart';
import 'package:logger/logger.dart';

/// A mock implementation of the progress service that doesn't use Firebase
class ProgressService {
  final Logger _logger = Logger();

  // Get user stats
  Future<UserStats> getUserStats(String userId) async {
    try {
      // Return mock data
      return UserStats(
        userId: userId,
        fitnessScore: 75.0,
        totalWorkoutsCompleted: 12,
        streakDays: 5,
        lastWorkoutDate: DateTime.now().subtract(const Duration(days: 1)),
        progressMetrics: {
          'pushUps': 30.0,
          'sitUps': 40.0,
          'runTime': 10.5,
        },
        recentWorkouts: [
          WorkoutLog(
            id: '1',
            workoutId: 'w1',
            workoutTitle: 'Full Body Workout',
            completedDate: DateTime.now().subtract(const Duration(days: 1)),
            durationMinutes: 45,
            focusArea: 'Full Body',
            intensity: 8.0,
          ),
          WorkoutLog(
            id: '2',
            workoutId: 'w2',
            workoutTitle: 'Cardio Training',
            completedDate: DateTime.now().subtract(const Duration(days: 3)),
            durationMinutes: 30,
            focusArea: 'Cardio',
            intensity: 7.0,
          ),
        ],
      );
    } catch (e) {
      _logger.e('Error getting user stats: $e');
      throw Exception('Failed to load user stats');
    }
  }

  // Get recent workouts
  Future<List<WorkoutLog>> getRecentWorkouts(
    String userId, {
    int limit = 10,
  }) async {
    try {
      // Return mock data
      return [
        WorkoutLog(
          id: '1',
          workoutId: 'w1',
          workoutTitle: 'Full Body Workout',
          completedDate: DateTime.now().subtract(const Duration(days: 1)),
          durationMinutes: 45,
          focusArea: 'Full Body',
          intensity: 8.0,
        ),
        WorkoutLog(
          id: '2',
          workoutId: 'w2',
          workoutTitle: 'Cardio Training',
          completedDate: DateTime.now().subtract(const Duration(days: 3)),
          durationMinutes: 30,
          focusArea: 'Cardio',
          intensity: 7.0,
        ),
        WorkoutLog(
          id: '3',
          workoutId: 'w3',
          workoutTitle: 'Upper Body Strength',
          completedDate: DateTime.now().subtract(const Duration(days: 5)),
          durationMinutes: 40,
          focusArea: 'Upper Body',
          intensity: 9.0,
        ),
      ];
    } catch (e) {
      _logger.e('Error getting recent workouts: $e');
      return [];
    }
  }

  // Get user achievements
  Future<List<Achievement>> getUserAchievements(String userId) async {
    try {
      // Return mock data
      return [
        Achievement(
          id: '1',
          title: 'First Workout',
          description: 'Completed your first workout',
          iconUrl: 'assets/icons/achievement_first_workout.png',
          earnedDate: DateTime.now().subtract(const Duration(days: 30)),
          points: 10,
          category: 'Milestone',
        ),
        Achievement(
          id: '2',
          title: '10 Workouts',
          description: 'Completed 10 workouts',
          iconUrl: 'assets/icons/achievement_10_workouts.png',
          earnedDate: DateTime.now().subtract(const Duration(days: 5)),
          points: 50,
          category: 'Milestone',
        ),
      ];
    } catch (e) {
      _logger.e('Error getting user achievements: $e');
      return [];
    }
  }

  // Get fitness progress over time
  Future<Map<String, List<Map<String, dynamic>>>> getFitnessProgress(
    String userId,
  ) async {
    try {
      // Return mock data
      final now = DateTime.now();
      return {
        'pushUps': [
          {
            'date': now.subtract(const Duration(days: 30)).millisecondsSinceEpoch,
            'value': 20,
          },
          {
            'date': now.subtract(const Duration(days: 20)).millisecondsSinceEpoch,
            'value': 25,
          },
          {
            'date': now.subtract(const Duration(days: 10)).millisecondsSinceEpoch,
            'value': 30,
          },
        ],
        'sitUps': [
          {
            'date': now.subtract(const Duration(days: 30)).millisecondsSinceEpoch,
            'value': 30,
          },
          {
            'date': now.subtract(const Duration(days: 20)).millisecondsSinceEpoch,
            'value': 35,
          },
          {
            'date': now.subtract(const Duration(days: 10)).millisecondsSinceEpoch,
            'value': 40,
          },
        ],
        'runTime': [
          {
            'date': now.subtract(const Duration(days: 30)).millisecondsSinceEpoch,
            'value': 12.0,
          },
          {
            'date': now.subtract(const Duration(days: 20)).millisecondsSinceEpoch,
            'value': 11.5,
          },
          {
            'date': now.subtract(const Duration(days: 10)).millisecondsSinceEpoch,
            'value': 10.5,
          },
        ],
      };
    } catch (e) {
      _logger.e('Error getting fitness progress: $e');
      return {'pushUps': [], 'sitUps': [], 'runTime': []};
    }
  }

  // Add a new fitness assessment
  Future<void> addFitnessAssessment(FitnessAssessment assessment) async {
    try {
      // In a real implementation, this would save to Supabase
      _logger.i('Adding fitness assessment: ${assessment.toJson()}');
    } catch (e) {
      _logger.e('Error adding fitness assessment: $e');
      throw Exception('Failed to save fitness assessment');
    }
  }
}
