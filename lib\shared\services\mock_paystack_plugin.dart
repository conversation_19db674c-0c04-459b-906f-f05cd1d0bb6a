import 'package:flutter/material.dart';
import 'package:flutter_paystack/flutter_paystack.dart';

/// A mock implementation of the Paystack plugin for development purposes
class MockPaystackPlugin implements PaystackPlugin {
  bool _initialized = false;
  String _publicKey = '';

  @override
  Future<CheckoutResponse> checkout(
    BuildContext context, {
    required Charge charge,
    CheckoutMethod method = CheckoutMethod.selectable,
    bool fullscreen = false,
    Widget? logo,
    bool hideEmail = false,
    bool hideAmount = false,
  }) async {
    // Simulate a successful payment
    return CheckoutResponse(
      message: 'Payment successful',
      reference: charge.reference,
      status: true,
      method: method,
      verify: true,
      card: null,
    );
  }

  @override
  Future<CheckoutResponse> chargeCard(
    BuildContext context, {
    required Charge charge,
  }) async {
    // Simulate a successful card charge
    return CheckoutResponse(
      message: 'Payment successful',
      reference: charge.reference,
      status: true,
      method: CheckoutMethod.card,
      verify: true,
      card: null,
    );
  }

  @override
  Future<void> initialize({required String publicKey}) async {
    // Simulate initialization
    _initialized = true;
    _publicKey = publicKey;
  }

  @override
  void dispose() {
    // Clean up resources
    _initialized = false;
    _publicKey = '';
  }

  @override
  bool get sdkInitialized => _initialized;

  @override
  String get publicKey => _publicKey;
}
