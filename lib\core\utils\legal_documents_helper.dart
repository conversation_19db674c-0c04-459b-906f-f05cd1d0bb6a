import 'package:url_launcher/url_launcher.dart';
import 'package:logger/logger.dart';

/// Helper class for launching legal documents hosted on GitHub Pages
class LegalDocumentsHelper {
  static final Logger _logger = Logger();

  // Netlify deployment URL for Fit4Force legal documents
  static const String _baseUrl =
      'https://lambent-salamander-6b0b6c.netlify.app';

  // Legal document URLs
  static const String termsOfServiceUrl = '$_baseUrl/terms-of-service.html';
  static const String privacyPolicyUrl = '$_baseUrl/privacy-policy.html';
  static const String cookiePolicyUrl = '$_baseUrl/cookie-policy.html';
  static const String disclaimerUrl = '$_baseUrl/disclaimer.html';
  static const String contactUrl = '$_baseUrl/contact.html';
  static const String refundPolicyUrl = '$_baseUrl/refund-policy.html';
  static const String dataSecurityUrl =
      '$_baseUrl/data-retention-security.html';
  static const String communityPolicyUrl = '$_baseUrl/community-policy.html';
  static const String legalIndexUrl = '$_baseUrl/index.html';

  /// Launch Terms of Service
  static Future<bool> launchTermsOfService() async {
    return await _launchUrl(termsOfServiceUrl, 'Terms of Service');
  }

  /// Launch Privacy Policy
  static Future<bool> launchPrivacyPolicy() async {
    return await _launchUrl(privacyPolicyUrl, 'Privacy Policy');
  }

  /// Launch Cookie Policy
  static Future<bool> launchCookiePolicy() async {
    return await _launchUrl(cookiePolicyUrl, 'Cookie Policy');
  }

  /// Launch Disclaimer
  static Future<bool> launchDisclaimer() async {
    return await _launchUrl(disclaimerUrl, 'Disclaimer');
  }

  /// Launch Contact Page
  static Future<bool> launchContactPage() async {
    return await _launchUrl(contactUrl, 'Contact Us');
  }

  /// Launch Refund Policy
  static Future<bool> launchRefundPolicy() async {
    return await _launchUrl(refundPolicyUrl, 'Refund Policy');
  }

  /// Launch Data Security Policy
  static Future<bool> launchDataSecurity() async {
    return await _launchUrl(dataSecurityUrl, 'Data Security Policy');
  }

  /// Launch Community Policy
  static Future<bool> launchCommunityPolicy() async {
    return await _launchUrl(communityPolicyUrl, 'Community Policy');
  }

  /// Launch Legal Documents Index
  static Future<bool> launchLegalIndex() async {
    return await _launchUrl(legalIndexUrl, 'Legal Documents');
  }

  /// Generic method to launch any legal document URL
  static Future<bool> _launchUrl(String url, String documentName) async {
    try {
      _logger.i('🔗 Launching $documentName: $url');

      final uri = Uri.parse(url);

      if (await canLaunchUrl(uri)) {
        await launchUrl(
          uri,
          mode: LaunchMode.externalApplication,
          webViewConfiguration: const WebViewConfiguration(
            enableJavaScript: true,
            enableDomStorage: true,
          ),
        );

        _logger.i('✅ Successfully launched $documentName');
        return true;
      } else {
        _logger.e('❌ Cannot launch $documentName URL: $url');
        return false;
      }
    } catch (e) {
      _logger.e('❌ Error launching $documentName: $e');
      return false;
    }
  }

  /// Check if legal documents URLs are properly configured
  static bool areUrlsConfigured() {
    return !_baseUrl.contains('[YOUR-GITHUB-USERNAME]') &&
        !_baseUrl.contains('[REPOSITORY-NAME]');
  }

  /// Get all legal document URLs as a map
  static Map<String, String> getAllUrls() {
    return {
      'Terms of Service': termsOfServiceUrl,
      'Privacy Policy': privacyPolicyUrl,
      'Cookie Policy': cookiePolicyUrl,
      'Disclaimer': disclaimerUrl,
      'Contact Us': contactUrl,
      'Refund Policy': refundPolicyUrl,
      'Data Security': dataSecurityUrl,
      'Community Policy': communityPolicyUrl,
      'Legal Index': legalIndexUrl,
    };
  }

  /// Validate that all URLs are accessible (for testing)
  static Future<Map<String, bool>> validateAllUrls() async {
    final results = <String, bool>{};
    final urls = getAllUrls();

    for (final entry in urls.entries) {
      try {
        final uri = Uri.parse(entry.value);
        final canLaunch = await canLaunchUrl(uri);
        results[entry.key] = canLaunch;

        _logger.d('${canLaunch ? "✅" : "❌"} ${entry.key}: ${entry.value}');
      } catch (e) {
        results[entry.key] = false;
        _logger.e('❌ Error validating ${entry.key}: $e');
      }
    }

    return results;
  }
}

/// Extension for easy access to legal documents from any widget
extension LegalDocumentsExtension on Object {
  /// Quick access to legal documents helper
  LegalDocumentsHelper get legal => LegalDocumentsHelper();
}

/// Legal document types enum for better type safety
enum LegalDocumentType {
  termsOfService,
  privacyPolicy,
  cookiePolicy,
  disclaimer,
  contact,
  refundPolicy,
  dataSecurity,
  communityPolicy,
  legalIndex,
}

/// Extension to get URLs from enum
extension LegalDocumentTypeExtension on LegalDocumentType {
  String get url {
    switch (this) {
      case LegalDocumentType.termsOfService:
        return LegalDocumentsHelper.termsOfServiceUrl;
      case LegalDocumentType.privacyPolicy:
        return LegalDocumentsHelper.privacyPolicyUrl;
      case LegalDocumentType.cookiePolicy:
        return LegalDocumentsHelper.cookiePolicyUrl;
      case LegalDocumentType.disclaimer:
        return LegalDocumentsHelper.disclaimerUrl;
      case LegalDocumentType.contact:
        return LegalDocumentsHelper.contactUrl;
      case LegalDocumentType.refundPolicy:
        return LegalDocumentsHelper.refundPolicyUrl;
      case LegalDocumentType.dataSecurity:
        return LegalDocumentsHelper.dataSecurityUrl;
      case LegalDocumentType.communityPolicy:
        return LegalDocumentsHelper.communityPolicyUrl;
      case LegalDocumentType.legalIndex:
        return LegalDocumentsHelper.legalIndexUrl;
    }
  }

  String get displayName {
    switch (this) {
      case LegalDocumentType.termsOfService:
        return 'Terms of Service';
      case LegalDocumentType.privacyPolicy:
        return 'Privacy Policy';
      case LegalDocumentType.cookiePolicy:
        return 'Cookie Policy';
      case LegalDocumentType.disclaimer:
        return 'Disclaimer';
      case LegalDocumentType.contact:
        return 'Contact Us';
      case LegalDocumentType.refundPolicy:
        return 'Refund Policy';
      case LegalDocumentType.dataSecurity:
        return 'Data Security Policy';
      case LegalDocumentType.communityPolicy:
        return 'Community Policy';
      case LegalDocumentType.legalIndex:
        return 'Legal Documents';
    }
  }

  /// Launch this legal document
  Future<bool> launch() async {
    return await LegalDocumentsHelper._launchUrl(url, displayName);
  }
}
