# Adding Exercise Images to Fit4Force

This guide explains how to add exercise images to the Fit4Force app.

## Image Requirements

- **Format**: PNG or JPG
- **Size**: 300x300 pixels recommended (square aspect ratio)
- **Background**: Transparent or solid color
- **Style**: Consistent style across all images (illustrations or photos)

## Option 1: Use Free Exercise Images

Several websites offer free exercise illustrations:

1. **Workout Labs**: https://workoutlabs.com/exercise-guide/
   - Offers free SVG exercise illustrations
   - Clean, consistent style

2. **Open Source Fitness Illustrations**:
   - GitHub repositories with free fitness illustrations
   - Search for "fitness illustrations" on GitHub

3. **Wikimedia Commons**:
   - Has some free exercise images
   - Quality and style may vary

## Option 2: Use Paid Stock Images

For higher quality and consistency:

1. **Shutterstock**: https://www.shutterstock.com/
2. **Adobe Stock**: https://stock.adobe.com/
3. **iStock**: https://www.istockphoto.com/

Search for "exercise illustrations" or "fitness illustrations".

## Option 3: Generate Custom Illustrations

Create custom illustrations using:

1. **AI Image Generation**:
   - DALL-E
   - Midjourney
   - Stable Diffusion

2. **Hire a Designer**:
   - Fiverr or Upwork
   - Commission a set of consistent exercise illustrations

## Adding Images to the App

1. **Create the directory structure**:
   ```
   mkdir -p assets/images/exercises
   ```

2. **Place your images in the directory**:
   - Name them consistently (e.g., `jumping_jacks.png`, `pushups.png`)
   - Use lowercase and underscores for filenames

3. **Update pubspec.yaml**:
   ```yaml
   flutter:
     assets:
       - assets/images/exercises/
   ```

4. **Run flutter pub get**:
   ```
   flutter pub get
   ```

5. **Use images in your app**:
   ```dart
   Image.asset(
     'assets/images/exercises/jumping_jacks.png',
     width: 80,
     height: 80,
     fit: BoxFit.cover,
   )
   ```

## Example Exercise List

Here's a list of common exercises to include:

### Cardio/HIIT
- Jumping Jacks
- Mountain Climbers
- Burpees
- High Knees
- Skater Jumps

### Strength
- Push-Ups (Standard, Wide, Diamond)
- Pull-Ups / Chin-Ups
- Squats
- Lunges
- Planks

### Military Fitness
- Bear Crawls
- Sandbag Carries
- Wall Climbs
- Rope Pulls
- Shuttle Runs

### Core & Flexibility
- Plank (Front, Side)
- Russian Twists
- Bicycle Crunches
- Leg Raises
- Hip Flexor Stretch

## Troubleshooting

If images don't appear:

1. Check that the path is correct
2. Verify that pubspec.yaml includes the assets directory
3. Run `flutter clean` and then `flutter pub get`
4. Restart the app

## Resources

- [Flutter Asset Management](https://flutter.dev/docs/development/ui/assets-and-images)
- [Flutter Image Widget](https://api.flutter.dev/flutter/widgets/Image-class.html)
