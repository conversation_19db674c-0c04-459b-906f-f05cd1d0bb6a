import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:fit_4_force/core/config/app_config.dart';
import 'package:fit_4_force/core/theme/app_theme.dart';
import 'package:fit_4_force/core/utils/responsive_utils.dart';
import 'package:fit_4_force/core/widgets/responsive_widgets.dart';
import 'package:fit_4_force/features/auth/bloc/auth_bloc.dart';
import 'package:fit_4_force/features/subscription/bloc/subscription_bloc.dart';
import 'package:fit_4_force/shared/widgets/base_widget.dart';
import 'package:intl/intl.dart';

class PremiumScreen extends StatefulWidget {
  const PremiumScreen({super.key});

  @override
  State<PremiumScreen> createState() => _PremiumScreenState();
}

class _PremiumScreenState extends State<PremiumScreen> {
  @override
  void initState() {
    super.initState();
    context.read<SubscriptionBloc>().add(CheckSubscriptionEvent());
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(title: const Text('Premium Membership')),
      body: BlocConsumer<SubscriptionBloc, SubscriptionState>(
        listener: (context, state) {
          if (state is SubscriptionError) {
            ScaffoldMessenger.of(
              context,
            ).showSnackBar(SnackBar(content: Text(state.message)));
          } else if (state is SubscriptionSuccess) {
            ScaffoldMessenger.of(context).showSnackBar(
              const SnackBar(
                content: Text('Subscription successful!'),
                backgroundColor: Colors.green,
              ),
            );

            // Refresh auth state to update premium status
            final authBloc = context.read<AuthBloc>();
            if (authBloc.state is Authenticated) {
              final user = (authBloc.state as Authenticated).user;
              authBloc.add(
                AuthenticatedEvent(
                  user.copyWith(
                    isPremium: true,
                    premiumExpiryDate: state.subscription.expiryDate,
                  ),
                ),
              );
            }
          }
        },
        builder: (context, state) {
          if (state is SubscriptionLoading || state is SubscriptionProcessing) {
            return const Center(child: CircularProgressIndicator());
          }

          if (state is SubscriptionActive) {
            return _buildActiveSubscription(context, state);
          }

          return _buildSubscriptionOffer(context);
        },
      ),
    );
  }

  Widget _buildActiveSubscription(
    BuildContext context,
    SubscriptionActive state,
  ) {
    final dateFormat = DateFormat('MMMM d, yyyy');
    final expiryDate = dateFormat.format(state.subscription.expiryDate);

    return SingleChildScrollView(
      padding: const EdgeInsets.all(24),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          // Premium badge
          Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: AppTheme.premiumColor.withValues(alpha: 0.2 * 255),
              shape: BoxShape.circle,
            ),
            child: Icon(
              Icons.workspace_premium,
              size: 80,
              color: AppTheme.premiumColor,
            ),
          ),
          const SizedBox(height: 24),

          // Premium status
          Text(
            'Premium Member',
            style: Theme.of(context).textTheme.headlineMedium?.copyWith(
              fontWeight: FontWeight.bold,
              color: AppTheme.premiumDarkColor,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            'Your subscription is active until:',
            style: Theme.of(context).textTheme.titleMedium,
          ),
          const SizedBox(height: 4),
          Text(
            expiryDate,
            style: Theme.of(
              context,
            ).textTheme.titleLarge?.copyWith(fontWeight: FontWeight.bold),
          ),
          const SizedBox(height: 32),

          // Divider
          const Divider(),
          const SizedBox(height: 24),

          // Premium features
          Text(
            'Your Premium Benefits',
            style: Theme.of(
              context,
            ).textTheme.titleLarge?.copyWith(fontWeight: FontWeight.bold),
          ),
          const SizedBox(height: 16),
          ...AppConfig.premiumFeatures.map(
            (feature) => _buildFeatureItem(feature),
          ),
          const SizedBox(height: 32),

          // Renew button
          BaseButton(
            text: 'Renew Subscription',
            icon: Icons.refresh,
            onPressed: () {
              final authState = context.read<AuthBloc>().state;
              if (authState is Authenticated) {
                context.read<SubscriptionBloc>().add(
                  RenewSubscriptionEvent(
                    email: authState.user.email,
                    fullName: authState.user.fullName,
                    context: context,
                  ),
                );
              }
            },
          ),
          const SizedBox(height: 16),

          // Cancel button
          TextButton(
            onPressed: () {
              _showCancelDialog(context, state.subscription.id);
            },
            child: const Text('Cancel Subscription'),
          ),
          const SizedBox(height: 16),

          // View history button
          OutlinedButton(
            onPressed: () {
              context.read<SubscriptionBloc>().add(
                LoadSubscriptionHistoryEvent(),
              );
              _showSubscriptionHistory(context);
            },
            child: const Text('View Subscription History'),
          ),
        ],
      ),
    );
  }

  Widget _buildSubscriptionOffer(BuildContext context) {
    final currencyFormat = NumberFormat.currency(symbol: '₦', decimalDigits: 0);
    final isDesktop = ResponsiveUtils.isDesktop(context);
    final padding = ResponsiveUtils.getResponsivePadding(context);
    final spacing = ResponsiveUtils.getResponsiveSpacing(context);

    return SingleChildScrollView(
      padding: padding,
      child: Center(
        child: ConstrainedBox(
          constraints: BoxConstraints(
            maxWidth: isDesktop ? 600 : double.infinity,
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.center,
            children: [
              // Premium icon
              Icon(
                Icons.workspace_premium,
                size: ResponsiveUtils.getResponsiveFontSize(
                  context,
                  mobile: 60,
                  tablet: 70,
                  desktop: 80,
                ),
                color: AppTheme.premiumColor,
              ),
              SizedBox(height: spacing),

              // Title
              ResponsiveText(
                'Upgrade to Premium',
                mobileFontSize: 24.0,
                tabletFontSize: 28.0,
                desktopFontSize: 32.0,
                fontWeight: FontWeight.bold,
                textAlign: TextAlign.center,
              ),
              SizedBox(height: spacing / 3),
              ResponsiveText(
                'Unlock all features and maximize your training',
                mobileFontSize: 14.0,
                tabletFontSize: 16.0,
                desktopFontSize: 18.0,
                color: Colors.grey[600],
                textAlign: TextAlign.center,
              ),
              SizedBox(height: spacing * 1.5),

              // Price card
              BaseCard(
                backgroundColor: AppTheme.premiumColor.withValues(
                  alpha: 0.1 * 255,
                ),
                child: Column(
                  children: [
                    Text(
                      'Monthly Subscription',
                      style: Theme.of(context).textTheme.titleLarge?.copyWith(
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 16),
                    Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          currencyFormat.format(
                            AppConfig.premiumSubscriptionPrice,
                          ),
                          style: Theme.of(
                            context,
                          ).textTheme.headlineLarge?.copyWith(
                            fontWeight: FontWeight.bold,
                            color: AppTheme.premiumDarkColor,
                          ),
                        ),
                        Text(
                          '/month',
                          style: Theme.of(context).textTheme.titleMedium
                              ?.copyWith(color: Colors.grey[600]),
                        ),
                      ],
                    ),
                    const SizedBox(height: 16),
                    const Text(
                      'Cancel anytime',
                      style: TextStyle(color: Colors.grey),
                    ),
                  ],
                ),
              ),
              const SizedBox(height: 32),

              // Features
              Text(
                'Premium Features',
                style: Theme.of(
                  context,
                ).textTheme.titleLarge?.copyWith(fontWeight: FontWeight.bold),
              ),
              const SizedBox(height: 16),
              ...AppConfig.premiumFeatures.map(
                (feature) => _buildFeatureItem(feature),
              ),
              const SizedBox(height: 32),

              // Subscribe button
              ResponsiveButton(
                text: 'Subscribe Now',
                backgroundColor: AppTheme.premiumColor,
                textColor: AppTheme.textOnPremium,
                onPressed: () {
                  final authState = context.read<AuthBloc>().state;
                  if (authState is Authenticated) {
                    context.read<SubscriptionBloc>().add(
                      SubscribeEvent(
                        email: authState.user.email,
                        fullName: authState.user.fullName,
                        context: context,
                      ),
                    );
                  }
                },
                mobileHeight: 48.0,
                tabletHeight: 52.0,
                desktopHeight: 56.0,
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildFeatureItem(String feature) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 8),
      child: Row(
        children: [
          Icon(Icons.check_circle, color: Colors.green, size: 20),
          const SizedBox(width: 12),
          Expanded(
            child: Text(feature, style: Theme.of(context).textTheme.bodyLarge),
          ),
        ],
      ),
    );
  }

  void _showCancelDialog(BuildContext context, String subscriptionId) {
    showDialog(
      context: context,
      builder:
          (context) => AlertDialog(
            title: const Text('Cancel Subscription'),
            content: const Text(
              'Are you sure you want to cancel your premium subscription? '
              'You will lose access to premium features at the end of your current billing period.',
            ),
            actions: [
              TextButton(
                onPressed: () {
                  Navigator.of(context).pop();
                },
                child: const Text('No, Keep It'),
              ),
              TextButton(
                onPressed: () {
                  Navigator.of(context).pop();
                  context.read<SubscriptionBloc>().add(
                    CancelSubscriptionEvent(subscriptionId: subscriptionId),
                  );
                },
                style: TextButton.styleFrom(foregroundColor: Colors.red),
                child: const Text('Yes, Cancel'),
              ),
            ],
          ),
    );
  }

  void _showSubscriptionHistory(BuildContext context) {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(16)),
      ),
      builder: (context) {
        return DraggableScrollableSheet(
          initialChildSize: 0.6,
          minChildSize: 0.3,
          maxChildSize: 0.9,
          expand: false,
          builder: (context, scrollController) {
            return BlocBuilder<SubscriptionBloc, SubscriptionState>(
              builder: (context, state) {
                if (state is SubscriptionLoading) {
                  return const Center(child: CircularProgressIndicator());
                }

                if (state is SubscriptionHistoryLoaded) {
                  final subscriptions = state.subscriptions;

                  if (subscriptions.isEmpty) {
                    return const Center(
                      child: Text('No subscription history found.'),
                    );
                  }

                  return Column(
                    children: [
                      Padding(
                        padding: const EdgeInsets.all(16),
                        child: Text(
                          'Subscription History',
                          style: Theme.of(context).textTheme.titleLarge
                              ?.copyWith(fontWeight: FontWeight.bold),
                        ),
                      ),
                      const Divider(),
                      Expanded(
                        child: ListView.separated(
                          controller: scrollController,
                          itemCount: subscriptions.length,
                          separatorBuilder: (context, index) => const Divider(),
                          itemBuilder: (context, index) {
                            final subscription = subscriptions[index];
                            final dateFormat = DateFormat('MMM d, yyyy');
                            final startDate = dateFormat.format(
                              subscription.startDate,
                            );
                            final expiryDate = dateFormat.format(
                              subscription.expiryDate,
                            );
                            final currencyFormat = NumberFormat.currency(
                              symbol: '₦',
                              decimalDigits: 0,
                            );

                            return ListTile(
                              title: Text(
                                'Premium Subscription',
                                style: Theme.of(context).textTheme.titleMedium
                                    ?.copyWith(fontWeight: FontWeight.bold),
                              ),
                              subtitle: Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  const SizedBox(height: 4),
                                  Text(
                                    'Amount: ${currencyFormat.format(subscription.amount)}',
                                  ),
                                  Text('Period: $startDate to $expiryDate'),
                                  Text(
                                    'Status: ${subscription.isActive ? 'Active' : 'Inactive'}',
                                    style: TextStyle(
                                      color:
                                          subscription.isActive
                                              ? Colors.green
                                              : Colors.red,
                                      fontWeight: FontWeight.bold,
                                    ),
                                  ),
                                ],
                              ),
                              trailing:
                                  subscription.isActive
                                      ? const Icon(
                                        Icons.check_circle,
                                        color: Colors.green,
                                      )
                                      : null,
                            );
                          },
                        ),
                      ),
                    ],
                  );
                }

                return const Center(
                  child: Text('Failed to load subscription history.'),
                );
              },
            );
          },
        );
      },
    );
  }
}
