import 'package:flutter/material.dart';
import 'package:fit_4_force/core/theme/app_theme.dart';
import 'package:fit_4_force/features/fitness/models/challenge_model.dart';
import 'package:fit_4_force/shared/models/user_model.dart';
import 'package:fit_4_force/shared/widgets/base_button.dart';

class ChallengesScreen extends StatefulWidget {
  final UserModel user;

  const ChallengesScreen({super.key, required this.user});

  @override
  State<ChallengesScreen> createState() => _ChallengesScreenState();
}

class _ChallengesScreenState extends State<ChallengesScreen>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;
  final TextEditingController _searchController = TextEditingController();
  String _selectedDifficulty = 'All';
  final List<String> _difficulties = [
    'All',
    'Beginner',
    'Intermediate',
    'Advanced',
  ];

  // Mock data for challenges
  final List<ChallengeModel> _challenges = [
    ChallengeModel(
      id: '1',
      name: '30-Day Push-Up Challenge',
      description:
          'Increase your upper body strength with this progressive push-up challenge. Perfect for military aspirants.',
      imageUrl: 'assets/images/content/challenge1.jpg',
      startDate: DateTime.now().subtract(const Duration(days: 5)),
      endDate: DateTime.now().add(const Duration(days: 25)),
      difficulty: 'Intermediate',
      workouts: [],
      icon: Icons.emoji_events,
      color: Colors.amber,
      isPremium: false,
      participantsCount: 245,
      rewards: ['Badge', 'Certificate'],
      targetAgency: 'Nigerian Army',
      tags: ['Strength', 'Upper Body', 'Endurance'],
      leaderboard: {},
    ),
    ChallengeModel(
      id: '2',
      name: 'Navy Swim Test Prep',
      description:
          'Prepare for the Navy swim test with this 4-week swimming challenge. Improve your technique and endurance.',
      imageUrl: 'assets/images/content/challenge2.jpg',
      startDate: DateTime.now().add(const Duration(days: 7)),
      endDate: DateTime.now().add(const Duration(days: 35)),
      difficulty: 'Advanced',
      workouts: [],
      icon: Icons.pool,
      color: Colors.blue,
      isPremium: true,
      participantsCount: 128,
      rewards: ['Badge', 'Certificate', 'Points'],
      targetAgency: 'Navy',
      tags: ['Swimming', 'Endurance', 'Technique'],
      leaderboard: {},
    ),
    ChallengeModel(
      id: '3',
      name: 'Beginner Running Program',
      description:
          'Start your running journey with this beginner-friendly program. Build endurance gradually and safely.',
      imageUrl: 'assets/images/content/challenge3.jpg',
      startDate: DateTime.now().subtract(const Duration(days: 10)),
      endDate: DateTime.now().add(const Duration(days: 20)),
      difficulty: 'Beginner',
      workouts: [],
      icon: Icons.directions_run,
      color: Colors.green,
      isPremium: false,
      participantsCount: 312,
      rewards: ['Badge'],
      targetAgency: 'All',
      tags: ['Running', 'Cardio', 'Endurance'],
      leaderboard: {},
    ),
  ];

  // Mock data for user's challenge progress
  final List<ChallengeProgressModel> _challengeProgress = [
    ChallengeProgressModel(
      id: '1',
      challengeId: '1',
      userId: 'user123',
      joinedDate: DateTime.now().subtract(const Duration(days: 5)),
      completedWorkouts: 5,
      totalWorkouts: 30,
      score: 150,
      rank: 42,
      isCompleted: false,
      workoutCompletionStatus: {
        'day1': true,
        'day2': true,
        'day3': true,
        'day4': true,
        'day5': true,
        'day6': false,
        'day7': false,
      },
    ),
  ];

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 3, vsync: this);
  }

  @override
  void dispose() {
    _tabController.dispose();
    _searchController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text(
          'Fitness Challenges',
          style: TextStyle(fontWeight: FontWeight.bold),
        ),
        actions: [
          IconButton(
            icon: const Icon(Icons.search),
            onPressed: () {
              _showSearchDialog();
            },
          ),
          IconButton(
            icon: const Icon(Icons.filter_list),
            onPressed: () {
              _showFilterDialog();
            },
          ),
        ],
        bottom: TabBar(
          controller: _tabController,
          labelColor: AppTheme.primaryColor,
          unselectedLabelColor: AppTheme.textSecondaryLight,
          indicatorColor: AppTheme.primaryColor,
          indicatorWeight: 3,
          tabs: const [
            Tab(text: 'ALL CHALLENGES'),
            Tab(text: 'MY CHALLENGES'),
            Tab(text: 'LEADERBOARDS'),
          ],
        ),
      ),
      body: TabBarView(
        controller: _tabController,
        children: [
          _buildAllChallengesTab(),
          _buildMyChallengesTab(),
          _buildLeaderboardsTab(),
        ],
      ),
    );
  }

  Widget _buildAllChallengesTab() {
    final filteredChallenges =
        _selectedDifficulty == 'All'
            ? _challenges
            : _challenges
                .where(
                  (challenge) => challenge.difficulty == _selectedDifficulty,
                )
                .toList();

    return filteredChallenges.isEmpty
        ? _buildEmptyState(
          'No challenges available',
          'Check back later for new challenges!',
        )
        : ListView.builder(
          padding: const EdgeInsets.all(16),
          itemCount: filteredChallenges.length,
          itemBuilder: (context, index) {
            return _buildChallengeCard(filteredChallenges[index]);
          },
        );
  }

  Widget _buildMyChallengesTab() {
    final myChallenges =
        _challenges
            .where(
              (challenge) => _challengeProgress.any(
                (progress) => progress.challengeId == challenge.id,
              ),
            )
            .toList();

    return myChallenges.isEmpty
        ? _buildEmptyState(
          'No challenges joined yet',
          'Join a challenge to track your progress!',
        )
        : ListView.builder(
          padding: const EdgeInsets.all(16),
          itemCount: myChallenges.length,
          itemBuilder: (context, index) {
            final challenge = myChallenges[index];
            final progress = _challengeProgress.firstWhere(
              (p) => p.challengeId == challenge.id,
            );
            return _buildChallengeProgressCard(challenge, progress);
          },
        );
  }

  Widget _buildLeaderboardsTab() {
    return _challengeProgress.isEmpty
        ? _buildEmptyState(
          'No leaderboards available',
          'Join a challenge to see leaderboards!',
        )
        : ListView.builder(
          padding: const EdgeInsets.all(16),
          itemCount: _challenges.length,
          itemBuilder: (context, index) {
            return _buildLeaderboardCard(_challenges[index]);
          },
        );
  }

  Widget _buildEmptyState(String title, String message) {
    return Center(
      child: Padding(
        padding: const EdgeInsets.all(24.0),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.emoji_events,
              size: 80,
              color: Colors.grey.withValues(alpha: 0.3 * 255),
            ),
            const SizedBox(height: 16),
            Text(
              title,
              style: const TextStyle(
                fontSize: 20,
                fontWeight: FontWeight.bold,
                color: Colors.black87,
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 8),
            Text(
              message,
              style: TextStyle(fontSize: 16, color: Colors.grey.shade600),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 24),
            BaseButton(
              text: 'Browse Challenges',
              icon: Icons.search,
              backgroundColor: AppTheme.primaryColor,
              onPressed: () {
                _tabController.animateTo(0);
              },
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildChallengeCard(ChallengeModel challenge) {
    return Card(
      margin: const EdgeInsets.only(bottom: 16),
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
      child: InkWell(
        onTap: () => _navigateToChallengeDetail(challenge),
        borderRadius: BorderRadius.circular(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Challenge image
            Container(
              height: 150,
              width: double.infinity,
              decoration: BoxDecoration(
                borderRadius: const BorderRadius.only(
                  topLeft: Radius.circular(16),
                  topRight: Radius.circular(16),
                ),
                image: DecorationImage(
                  image: AssetImage(challenge.imageUrl),
                  fit: BoxFit.cover,
                ),
              ),
              child: Stack(
                children: [
                  // Gradient overlay
                  Container(
                    decoration: BoxDecoration(
                      borderRadius: const BorderRadius.only(
                        topLeft: Radius.circular(16),
                        topRight: Radius.circular(16),
                      ),
                      gradient: LinearGradient(
                        begin: Alignment.topCenter,
                        end: Alignment.bottomCenter,
                        colors: [
                          Colors.transparent,
                          Colors.black.withValues(alpha: 0.7 * 255),
                        ],
                      ),
                    ),
                  ),
                  // Challenge name and status
                  Positioned(
                    bottom: 16,
                    left: 16,
                    right: 16,
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          challenge.name,
                          style: const TextStyle(
                            fontSize: 20,
                            fontWeight: FontWeight.bold,
                            color: Colors.white,
                            shadows: [
                              Shadow(
                                offset: Offset(0, 1),
                                blurRadius: 3.0,
                                color: Colors.black,
                              ),
                            ],
                          ),
                        ),
                        const SizedBox(height: 4),
                        Row(
                          children: [
                            Container(
                              padding: const EdgeInsets.symmetric(
                                horizontal: 8,
                                vertical: 2,
                              ),
                              decoration: BoxDecoration(
                                color: _getDifficultyColor(
                                  challenge.difficulty,
                                ).withValues(alpha: 0.8 * 255),
                                borderRadius: BorderRadius.circular(12),
                              ),
                              child: Text(
                                challenge.difficulty,
                                style: const TextStyle(
                                  fontSize: 12,
                                  color: Colors.white,
                                  fontWeight: FontWeight.w500,
                                ),
                              ),
                            ),
                            const SizedBox(width: 8),
                            Container(
                              padding: const EdgeInsets.symmetric(
                                horizontal: 8,
                                vertical: 2,
                              ),
                              decoration: BoxDecoration(
                                color: _getStatusColor(
                                  challenge,
                                ).withValues(alpha: 0.8 * 255),
                                borderRadius: BorderRadius.circular(12),
                              ),
                              child: Text(
                                _getChallengeStatus(challenge),
                                style: const TextStyle(
                                  fontSize: 12,
                                  color: Colors.white,
                                  fontWeight: FontWeight.w500,
                                ),
                              ),
                            ),
                            if (challenge.isPremium) ...[
                              const SizedBox(width: 8),
                              Container(
                                padding: const EdgeInsets.symmetric(
                                  horizontal: 8,
                                  vertical: 2,
                                ),
                                decoration: BoxDecoration(
                                  color: Colors.amber.withValues(
                                    alpha: 0.8 * 255,
                                  ),
                                  borderRadius: BorderRadius.circular(12),
                                ),
                                child: const Text(
                                  'Premium',
                                  style: TextStyle(
                                    fontSize: 12,
                                    color: Colors.white,
                                    fontWeight: FontWeight.w500,
                                  ),
                                ),
                              ),
                            ],
                          ],
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ),
            // Challenge details
            Padding(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    challenge.description,
                    style: const TextStyle(fontSize: 14, color: Colors.black87),
                    maxLines: 2,
                    overflow: TextOverflow.ellipsis,
                  ),
                  const SizedBox(height: 16),
                  // Challenge info
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      _buildInfoItem(
                        Icons.calendar_today,
                        '${challenge.durationDays} days',
                        Colors.blue,
                      ),
                      _buildInfoItem(
                        Icons.people,
                        '${challenge.participantsCount} participants',
                        Colors.green,
                      ),
                      _buildInfoItem(
                        Icons.military_tech,
                        challenge.targetAgency,
                        _getAgencyColor(challenge.targetAgency),
                      ),
                    ],
                  ),
                  const SizedBox(height: 16),
                  // Join button
                  SizedBox(
                    width: double.infinity,
                    child: BaseButton(
                      text: _getChallengeButtonText(challenge),
                      icon: _getChallengeButtonIcon(challenge),
                      backgroundColor: _getChallengeButtonColor(challenge),
                      onPressed: () => _joinChallenge(challenge),
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildChallengeProgressCard(
    ChallengeModel challenge,
    ChallengeProgressModel progress,
  ) {
    return Card(
      margin: const EdgeInsets.only(bottom: 16),
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
      child: InkWell(
        onTap: () => _navigateToChallengeDetail(challenge),
        borderRadius: BorderRadius.circular(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Challenge image with progress overlay
            Container(
              height: 150,
              width: double.infinity,
              decoration: BoxDecoration(
                borderRadius: const BorderRadius.only(
                  topLeft: Radius.circular(16),
                  topRight: Radius.circular(16),
                ),
                image: DecorationImage(
                  image: AssetImage(challenge.imageUrl),
                  fit: BoxFit.cover,
                ),
              ),
              child: Stack(
                children: [
                  // Gradient overlay
                  Container(
                    decoration: BoxDecoration(
                      borderRadius: const BorderRadius.only(
                        topLeft: Radius.circular(16),
                        topRight: Radius.circular(16),
                      ),
                      gradient: LinearGradient(
                        begin: Alignment.topCenter,
                        end: Alignment.bottomCenter,
                        colors: [
                          Colors.transparent,
                          Colors.black.withValues(alpha: 0.7 * 255),
                        ],
                      ),
                    ),
                  ),
                  // Challenge name and progress
                  Positioned(
                    bottom: 16,
                    left: 16,
                    right: 16,
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          challenge.name,
                          style: const TextStyle(
                            fontSize: 20,
                            fontWeight: FontWeight.bold,
                            color: Colors.white,
                            shadows: [
                              Shadow(
                                offset: Offset(0, 1),
                                blurRadius: 3.0,
                                color: Colors.black,
                              ),
                            ],
                          ),
                        ),
                        const SizedBox(height: 8),
                        // Progress bar
                        ClipRRect(
                          borderRadius: BorderRadius.circular(8),
                          child: LinearProgressIndicator(
                            value: progress.completionPercentage / 100,
                            backgroundColor: Colors.grey.withValues(
                              alpha: 0.3 * 255,
                            ),
                            valueColor: AlwaysStoppedAnimation<Color>(
                              _getProgressColor(progress.completionPercentage),
                            ),
                            minHeight: 8,
                          ),
                        ),
                        const SizedBox(height: 4),
                        Text(
                          '${progress.completionPercentage.toStringAsFixed(1)}% Complete',
                          style: const TextStyle(
                            fontSize: 12,
                            color: Colors.white,
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ),
            // Challenge progress details
            Padding(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Progress stats
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      _buildProgressItem(
                        'Workouts',
                        '${progress.completedWorkouts}/${progress.totalWorkouts}',
                        Colors.blue,
                      ),
                      _buildProgressItem(
                        'Score',
                        '${progress.score} pts',
                        Colors.amber,
                      ),
                      _buildProgressItem(
                        'Rank',
                        '#${progress.rank}',
                        Colors.purple,
                      ),
                      _buildProgressItem(
                        'Days Left',
                        '${challenge.daysRemaining}',
                        Colors.green,
                      ),
                    ],
                  ),
                  const SizedBox(height: 16),
                  // Continue button
                  SizedBox(
                    width: double.infinity,
                    child: BaseButton(
                      text: 'Continue Challenge',
                      icon: Icons.play_arrow,
                      backgroundColor: AppTheme.primaryColor,
                      onPressed: () => _navigateToChallengeDetail(challenge),
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildLeaderboardCard(ChallengeModel challenge) {
    return Card(
      margin: const EdgeInsets.only(bottom: 16),
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
      child: InkWell(
        onTap: () => _navigateToLeaderboard(challenge),
        borderRadius: BorderRadius.circular(16),
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Row(
            children: [
              Container(
                width: 60,
                height: 60,
                decoration: BoxDecoration(
                  color: challenge.color.withValues(alpha: 0.1 * 255),
                  shape: BoxShape.circle,
                ),
                child: Icon(challenge.icon, color: challenge.color, size: 30),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      challenge.name,
                      style: const TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                        color: Colors.black87,
                      ),
                    ),
                    const SizedBox(height: 4),
                    Text(
                      '${challenge.participantsCount} participants',
                      style: TextStyle(
                        fontSize: 14,
                        color: Colors.grey.shade600,
                      ),
                    ),
                  ],
                ),
              ),
              const Icon(Icons.chevron_right, color: Colors.grey),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildInfoItem(IconData icon, String text, Color color) {
    return Column(
      children: [
        Container(
          padding: const EdgeInsets.all(8),
          decoration: BoxDecoration(
            color: color.withValues(alpha: 0.1 * 255),
            shape: BoxShape.circle,
          ),
          child: Icon(icon, color: color, size: 16),
        ),
        const SizedBox(height: 4),
        Text(
          text,
          style: TextStyle(fontSize: 12, color: Colors.grey.shade700),
          maxLines: 1,
          overflow: TextOverflow.ellipsis,
        ),
      ],
    );
  }

  Widget _buildProgressItem(String label, String value, Color color) {
    return Column(
      children: [
        Text(
          value,
          style: TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.bold,
            color: color,
          ),
        ),
        const SizedBox(height: 4),
        Text(
          label,
          style: TextStyle(fontSize: 12, color: Colors.grey.shade600),
        ),
      ],
    );
  }

  void _showSearchDialog() {
    showDialog(
      context: context,
      builder: (context) {
        return AlertDialog(
          title: const Text('Search Challenges'),
          content: TextField(
            controller: _searchController,
            decoration: const InputDecoration(
              hintText: 'Enter keywords...',
              prefixIcon: Icon(Icons.search),
            ),
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.pop(context),
              child: const Text('CANCEL'),
            ),
            TextButton(
              onPressed: () {
                // Perform search
                Navigator.pop(context);
                // Update UI with search results
              },
              child: const Text('SEARCH'),
            ),
          ],
        );
      },
    );
  }

  void _showFilterDialog() {
    showDialog(
      context: context,
      builder: (context) {
        return StatefulBuilder(
          builder: (context, setState) {
            return AlertDialog(
              title: const Text('Filter Challenges'),
              content: Column(
                mainAxisSize: MainAxisSize.min,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  const Text(
                    'Difficulty',
                    style: TextStyle(fontWeight: FontWeight.bold, fontSize: 16),
                  ),
                  const SizedBox(height: 8),
                  Wrap(
                    spacing: 8,
                    runSpacing: 8,
                    children:
                        _difficulties.map((difficulty) {
                          return ChoiceChip(
                            label: Text(difficulty),
                            selected: _selectedDifficulty == difficulty,
                            onSelected: (selected) {
                              setState(() {
                                _selectedDifficulty = difficulty;
                              });
                            },
                          );
                        }).toList(),
                  ),
                ],
              ),
              actions: [
                TextButton(
                  onPressed: () => Navigator.pop(context),
                  child: const Text('CANCEL'),
                ),
                TextButton(
                  onPressed: () {
                    // Apply filters
                    Navigator.pop(context);
                    // Update UI with filtered challenges
                    this.setState(() {});
                  },
                  child: const Text('APPLY'),
                ),
              ],
            );
          },
        );
      },
    );
  }

  void _navigateToChallengeDetail(ChallengeModel challenge) {
    // Navigate to challenge detail screen
    if (challenge.isPremium && !widget.user.isPremium) {
      _showPremiumDialog();
    } else {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('Viewing challenge: ${challenge.name}')),
      );
    }
  }

  void _navigateToLeaderboard(ChallengeModel challenge) {
    // Navigate to leaderboard screen
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(content: Text('Viewing leaderboard: ${challenge.name}')),
    );
  }

  void _joinChallenge(ChallengeModel challenge) {
    if (challenge.isPremium && !widget.user.isPremium) {
      _showPremiumDialog();
      return;
    }

    if (challenge.isUpcoming) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(
            'You\'ve been added to the waitlist for ${challenge.name}',
          ),
        ),
      );
      return;
    }

    if (challenge.isCompleted) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('This challenge has already ended')),
      );
      return;
    }

    // Check if already joined
    if (_challengeProgress.any((p) => p.challengeId == challenge.id)) {
      _navigateToChallengeDetail(challenge);
      return;
    }

    // Join challenge
    setState(() {
      _challengeProgress.add(
        ChallengeProgressModel(
          id: DateTime.now().millisecondsSinceEpoch.toString(),
          challengeId: challenge.id,
          userId: widget.user.id,
          joinedDate: DateTime.now(),
          completedWorkouts: 0,
          totalWorkouts: 30, // Mock value
          score: 0,
          rank: challenge.participantsCount + 1,
          isCompleted: false,
          workoutCompletionStatus: {},
        ),
      );
    });

    ScaffoldMessenger.of(
      context,
    ).showSnackBar(SnackBar(content: Text('You\'ve joined ${challenge.name}')));

    // Switch to My Challenges tab
    _tabController.animateTo(1);
  }

  void _showPremiumDialog() {
    showDialog(
      context: context,
      builder: (context) {
        return AlertDialog(
          title: const Text('Premium Content'),
          content: const Text(
            'This challenge is only available to premium users. Upgrade to access all premium content!',
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.pop(context),
              child: const Text('CANCEL'),
            ),
            TextButton(
              onPressed: () {
                Navigator.pop(context);
                // Navigate to subscription screen
              },
              child: const Text('UPGRADE'),
            ),
          ],
        );
      },
    );
  }

  String _getChallengeStatus(ChallengeModel challenge) {
    if (challenge.isActive) return 'Active';
    if (challenge.isUpcoming) return 'Upcoming';
    return 'Completed';
  }

  Color _getStatusColor(ChallengeModel challenge) {
    if (challenge.isActive) return Colors.green;
    if (challenge.isUpcoming) return Colors.blue;
    return Colors.grey;
  }

  Color _getDifficultyColor(String difficulty) {
    switch (difficulty) {
      case 'Beginner':
        return Colors.green;
      case 'Intermediate':
        return Colors.orange;
      case 'Advanced':
        return Colors.red;
      default:
        return Colors.grey;
    }
  }

  Color _getAgencyColor(String agency) {
    switch (agency) {
      case 'Nigerian Army':
        return Colors.green;
      case 'Navy':
        return Colors.blue;
      case 'Air Force':
        return Colors.indigo;
      case 'DSSC':
        return Colors.purple;
      case 'NDA':
        return Colors.red;
      case 'NSCDC':
        return Colors.orange;
      case 'EFCC':
        return Colors.teal;
      default:
        return Colors.grey;
    }
  }

  Color _getProgressColor(double percentage) {
    if (percentage >= 75) return Colors.green;
    if (percentage >= 50) return Colors.orange;
    if (percentage >= 25) return Colors.amber;
    return Colors.red;
  }

  String _getChallengeButtonText(ChallengeModel challenge) {
    if (_challengeProgress.any((p) => p.challengeId == challenge.id)) {
      return 'Continue';
    }

    if (challenge.isUpcoming) return 'Join Waitlist';
    if (challenge.isCompleted) return 'View Results';
    return 'Join Challenge';
  }

  IconData _getChallengeButtonIcon(ChallengeModel challenge) {
    if (_challengeProgress.any((p) => p.challengeId == challenge.id)) {
      return Icons.play_arrow;
    }

    if (challenge.isUpcoming) return Icons.access_time;
    if (challenge.isCompleted) return Icons.assessment;
    return Icons.add;
  }

  Color _getChallengeButtonColor(ChallengeModel challenge) {
    if (_challengeProgress.any((p) => p.challengeId == challenge.id)) {
      return AppTheme.primaryColor;
    }

    if (challenge.isUpcoming) return Colors.blue;
    if (challenge.isCompleted) return Colors.grey;
    return AppTheme.primaryColor;
  }
}
