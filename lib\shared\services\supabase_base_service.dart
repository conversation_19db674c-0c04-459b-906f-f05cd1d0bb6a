import 'package:logger/logger.dart';

/// Mock query builder for Supabase-like queries
class _MockQueryBuilder {
  final String _table;
  final Map<String, List<Map<String, dynamic>>> _database;
  List<Map<String, dynamic>> _results = [];
  final List<Map<String, String>> _filters = [];

  _MockQueryBuilder(this._table, this._database) {
    if (!_database.containsKey(_table)) {
      _database[_table] = [];
    }
    _results = List.from(_database[_table]!);
  }

  _MockQueryBuilder select() {
    return this;
  }

  _MockQueryBuilder eq(String field, dynamic value) {
    _filters.add({'field': field, 'value': value.toString()});
    _results =
        _results
            .where((doc) => doc[field].toString() == value.toString())
            .toList();
    return this;
  }

  _MockQueryBuilder order(String field, {bool ascending = true}) {
    _results.sort((a, b) {
      final aValue = a[field];
      final bValue = b[field];
      if (aValue == null || bValue == null) return 0;

      int comparison;
      if (aValue is String && bValue is String) {
        comparison = aValue.compareTo(bValue);
      } else if (aValue is num && bValue is num) {
        comparison = aValue.compareTo(bValue);
      } else {
        comparison = aValue.toString().compareTo(bValue.toString());
      }

      return ascending ? comparison : -comparison;
    });
    return this;
  }

  _MockQueryBuilder limit(int count) {
    if (_results.length > count) {
      _results = _results.sublist(0, count);
    }
    return this;
  }

  Future<Map<String, dynamic>?> maybeSingle() async {
    if (_results.isEmpty) return null;
    return _results.first;
  }

  Future<Map<String, dynamic>> single() async {
    if (_results.isEmpty) throw Exception('No results found');
    if (_results.length > 1) throw Exception('More than one result found');
    return _results.first;
  }

  Future<List<Map<String, dynamic>>> get() async {
    return _results;
  }

  Future<List<Map<String, dynamic>>> insert(Map<String, dynamic> data) async {
    final id = data['id'] ?? DateTime.now().millisecondsSinceEpoch.toString();
    final document = {...data, 'id': id};
    _database[_table]!.add(document);
    return [document];
  }

  Future<void> update(Map<String, dynamic> data) async {
    for (final filter in _filters) {
      final field = filter['field']!;
      final value = filter['value']!;

      final indices = [];
      for (int i = 0; i < _database[_table]!.length; i++) {
        if (_database[_table]![i][field].toString() == value) {
          indices.add(i);
        }
      }

      for (final index in indices) {
        _database[_table]![index] = {..._database[_table]![index], ...data};
      }
    }
  }

  Future<void> delete() async {
    for (final filter in _filters) {
      final field = filter['field']!;
      final value = filter['value']!;

      _database[_table]!.removeWhere((doc) => doc[field].toString() == value);
    }
  }
}

/// Mock base service for database operations
abstract class SupabaseBaseService {
  final Logger _logger = Logger();

  // Mock database storage
  static final Map<String, List<Map<String, dynamic>>> _mockDatabase = {};

  /// Table name getter
  String get tableName;

  // Initialize the table if it doesn't exist
  void _initTable() {
    if (!_mockDatabase.containsKey(tableName)) {
      _mockDatabase[tableName] = [];
    }
  }

  /// Create document
  Future<String> create(Map<String, dynamic> data) async {
    try {
      _initTable();

      // Generate ID if not provided
      final id = data['id'] ?? DateTime.now().millisecondsSinceEpoch.toString();

      // Create the document with ID and timestamps
      final document = {
        ...data,
        'id': id,
        'created_at': DateTime.now().toIso8601String(),
      };

      // Add to mock database
      _mockDatabase[tableName]!.add(document);

      _logger.i('Mock document created in $tableName with ID: $id');
      return id;
    } catch (e) {
      _logger.e('Error creating document: $e');
      rethrow;
    }
  }

  /// Get document by ID
  Future<Map<String, dynamic>?> getById(String id) async {
    try {
      _initTable();

      // Find document by ID
      final document = _mockDatabase[tableName]!.firstWhere(
        (doc) => doc['id'] == id,
        orElse: () => {},
      );

      if (document.isEmpty) {
        return null;
      }

      _logger.i('Mock document retrieved from $tableName with ID: $id');
      return document;
    } catch (e) {
      _logger.e('Error getting document: $e');
      rethrow;
    }
  }

  /// Update document
  Future<void> update(String id, Map<String, dynamic> data) async {
    try {
      _initTable();

      // Find document index
      final index = _mockDatabase[tableName]!.indexWhere(
        (doc) => doc['id'] == id,
      );

      if (index == -1) {
        throw Exception('Document not found');
      }

      // Update document
      _mockDatabase[tableName]![index] = {
        ..._mockDatabase[tableName]![index],
        ...data,
        'updated_at': DateTime.now().toIso8601String(),
      };

      _logger.i('Mock document updated in $tableName with ID: $id');
    } catch (e) {
      _logger.e('Error updating document: $e');
      rethrow;
    }
  }

  /// Delete document
  Future<void> delete(String id) async {
    try {
      _initTable();

      // Remove document
      _mockDatabase[tableName]!.removeWhere((doc) => doc['id'] == id);

      _logger.i('Mock document deleted from $tableName with ID: $id');
    } catch (e) {
      _logger.e('Error deleting document: $e');
      rethrow;
    }
  }

  /// Get all documents
  Future<List<Map<String, dynamic>>> getAll() async {
    try {
      _initTable();

      _logger.i('Mock retrieved all documents from $tableName');
      return _mockDatabase[tableName]!;
    } catch (e) {
      _logger.e('Error getting all documents: $e');
      rethrow;
    }
  }

  /// Query documents
  Future<List<Map<String, dynamic>>> query({
    required String field,
    required dynamic value,
  }) async {
    try {
      _initTable();

      // Filter documents
      final results =
          _mockDatabase[tableName]!
              .where((doc) => doc[field] == value)
              .toList();

      _logger.i('Mock queried documents from $tableName where $field = $value');
      return results;
    } catch (e) {
      _logger.e('Error querying documents: $e');
      rethrow;
    }
  }

  /// Mock method to simulate Supabase client.from().select().eq().maybeSingle()
  dynamic from(String table) {
    return _MockQueryBuilder(table, _mockDatabase);
  }
}
