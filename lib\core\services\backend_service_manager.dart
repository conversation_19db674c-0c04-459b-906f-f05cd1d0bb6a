import 'package:flutter/foundation.dart';
import 'package:fit_4_force/core/services/realtime_service.dart';
import 'package:fit_4_force/core/services/storage_service.dart';
import 'package:fit_4_force/core/services/notification_service.dart';
import 'package:fit_4_force/core/config/supabase_config.dart';
import 'package:logger/logger.dart';

/// Centralized manager for all Supabase backend services
class BackendServiceManager {
  static final BackendServiceManager _instance = BackendServiceManager._internal();
  factory BackendServiceManager() => _instance;
  BackendServiceManager._internal();

  final Logger _logger = Logger();
  
  // Service instances
  late final RealtimeService _realtimeService;
  late final StorageService _storageService;
  late final NotificationService _notificationService;
  
  bool _isInitialized = false;
  
  // Getters for services
  RealtimeService get realtime => _realtimeService;
  StorageService get storage => _storageService;
  NotificationService get notifications => _notificationService;
  
  /// Initialize all backend services
  Future<void> initialize() async {
    if (_isInitialized) {
      _logger.w('⚠️ Backend services already initialized');
      return;
    }

    try {
      _logger.i('🚀 Initializing Fit4Force backend services...');

      // Ensure Supabase is initialized first
      if (!SupabaseConfig.isInitialized) {
        await SupabaseConfig.initialize();
      }

      // Initialize service instances
      _realtimeService = RealtimeService();
      _storageService = StorageService();
      _notificationService = NotificationService();

      // Initialize storage buckets
      await _storageService.initializeBuckets();
      _logger.i('✅ Storage service initialized');

      // Initialize notification service
      await _notificationService.initialize();
      _logger.i('✅ Notification service initialized');

      // Realtime service is ready to use (no initialization needed)
      _logger.i('✅ Realtime service ready');

      _isInitialized = true;
      _logger.i('🎉 All backend services initialized successfully!');

      // Log service status in debug mode
      if (kDebugMode) {
        await _logServiceStatus();
      }
    } catch (e) {
      _logger.e('❌ Error initializing backend services: $e');
      rethrow;
    }
  }

  /// Log status of all services
  Future<void> _logServiceStatus() async {
    try {
      _logger.d('📊 Backend Service Status:');
      _logger.d('   🔄 Realtime: ${_realtimeService.activeSubscriptionCount} active subscriptions');
      
      final storageStats = await _storageService.getStorageStats();
      _logger.d('   📁 Storage: ${storageStats.length} buckets configured');
      
      _logger.d('   🔔 Notifications: Service ready');
      _logger.d('   🔗 Supabase: ${SupabaseConfig.isInitialized ? "Connected" : "Disconnected"}');
    } catch (e) {
      _logger.w('⚠️ Error getting service status: $e');
    }
  }

  /// Cleanup all services (call when app is closing)
  Future<void> cleanup() async {
    try {
      _logger.i('🧹 Cleaning up backend services...');
      
      // Cleanup realtime subscriptions
      await _realtimeService.unsubscribeAll();
      
      _logger.i('✅ Backend services cleaned up');
    } catch (e) {
      _logger.e('❌ Error cleaning up backend services: $e');
    }
  }

  /// Check if all services are healthy
  Future<Map<String, bool>> healthCheck() async {
    final health = <String, bool>{};
    
    try {
      // Check Supabase connection
      health['supabase'] = SupabaseConfig.isInitialized;
      
      // Check realtime service
      health['realtime'] = true; // Always available if Supabase is connected
      
      // Check storage service
      try {
        await _storageService.getStorageStats();
        health['storage'] = true;
      } catch (e) {
        health['storage'] = false;
      }
      
      // Check notification service
      health['notifications'] = true; // Service is stateless, always available
      
    } catch (e) {
      _logger.e('❌ Error during health check: $e');
    }
    
    return health;
  }

  /// Get comprehensive service statistics
  Future<Map<String, dynamic>> getServiceStatistics() async {
    try {
      final stats = <String, dynamic>{};
      
      // Realtime statistics
      stats['realtime'] = {
        'active_subscriptions': _realtimeService.activeSubscriptionCount,
        'status': 'healthy',
      };
      
      // Storage statistics
      stats['storage'] = await _storageService.getStorageStats();
      
      // Notification statistics (would need to be implemented)
      stats['notifications'] = {
        'status': 'healthy',
        'service_ready': true,
      };
      
      // Overall status
      stats['overall'] = {
        'initialized': _isInitialized,
        'supabase_connected': SupabaseConfig.isInitialized,
        'timestamp': DateTime.now().toIso8601String(),
      };
      
      return stats;
    } catch (e) {
      _logger.e('❌ Error getting service statistics: $e');
      return {'error': e.toString()};
    }
  }

  /// Subscribe to community updates for a user
  Future<void> subscribeToUserCommunityUpdates(String userId) async {
    try {
      // Subscribe to posts
      _realtimeService.subscribeToPosts().listen((update) {
        _logger.d('📝 Community post update for user $userId: ${update['event']}');
        // Handle post updates (could trigger notifications, UI updates, etc.)
      });
      
      _logger.i('✅ Subscribed to community updates for user $userId');
    } catch (e) {
      _logger.e('❌ Error subscribing to community updates: $e');
    }
  }

  /// Subscribe to workout progress for a user
  Future<void> subscribeToUserWorkoutProgress(String userId) async {
    try {
      _realtimeService.subscribeToWorkoutProgress(userId).listen((update) {
        _logger.d('🏋️ Workout progress update for user $userId: ${update['event']}');
        // Handle workout progress updates
      });
      
      _logger.i('✅ Subscribed to workout progress for user $userId');
    } catch (e) {
      _logger.e('❌ Error subscribing to workout progress: $e');
    }
  }

  /// Setup user-specific subscriptions
  Future<void> setupUserSubscriptions(String userId) async {
    try {
      await subscribeToUserCommunityUpdates(userId);
      await subscribeToUserWorkoutProgress(userId);
      
      _logger.i('✅ All user subscriptions setup for $userId');
    } catch (e) {
      _logger.e('❌ Error setting up user subscriptions: $e');
    }
  }

  /// Cleanup user-specific subscriptions
  Future<void> cleanupUserSubscriptions(String userId) async {
    try {
      await _realtimeService.unsubscribe('posts_channel');
      await _realtimeService.unsubscribe('workout_progress_$userId');
      
      _logger.i('✅ User subscriptions cleaned up for $userId');
    } catch (e) {
      _logger.e('❌ Error cleaning up user subscriptions: $e');
    }
  }

  /// Send a test notification (for debugging)
  Future<void> sendTestNotification(String userId) async {
    if (kDebugMode) {
      await _notificationService.sendNotification(
        userId: userId,
        type: NotificationType.general,
        title: 'Test Notification',
        message: 'This is a test notification from Fit4Force!',
        data: {'test': true},
        immediate: true,
      );
    }
  }

  /// Get service initialization status
  bool get isInitialized => _isInitialized;
  
  /// Get Supabase connection status
  bool get isConnected => SupabaseConfig.isInitialized;
}
