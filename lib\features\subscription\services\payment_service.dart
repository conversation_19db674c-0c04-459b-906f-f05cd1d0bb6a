import 'package:flutter/material.dart';
import 'package:flutter_paystack/flutter_paystack.dart';
import 'package:fit_4_force/core/config/app_config.dart';

class PaymentService {
  // Initialize the plugin
  static final plugin = PaystackPlugin();
  static bool _initialized = false;

  static void initialize() {
    if (!_initialized) {
      plugin.initialize(publicKey: AppConfig.paystackPublicKey);
      _initialized = true;
    }
  }

  static Future<bool> initializePayment({
    required String email,
    required String fullName,
    required BuildContext context,
  }) async {
    try {
      // Make sure plugin is initialized
      initialize();

      // Prepare the payment
      final amount =
          AppConfig.premiumSubscriptionPrice * 100; // Convert to kobo
      final reference = DateTime.now().millisecondsSinceEpoch.toString();

      // Create charge
      final charge =
          Charge()
            ..amount = amount.toInt()
            ..email = email
            ..reference = reference
            ..putCustomField('Full Name', fullName)
            ..putCustomField('Subscription Type', 'premium')
            ..putCustomField('Duration', 'monthly');

      // Present the checkout
      final response = await plugin.checkout(
        context,
        method: CheckoutMethod.selectable,
        charge: charge,
      );

      // Handle the response
      if (response.status) {
        // Payment successful
        return true;
      } else {
        // Payment failed
        if (context.mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('Payment failed: ${response.message}'),
              backgroundColor: Colors.red,
            ),
          );
        }
        return false;
      }
    } catch (e) {
      // Handle any exceptions
      if (context.mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Payment error: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
      return false;
    }
  }
}
