import 'package:flutter/material.dart';
import 'package:fit_4_force/core/theme/app_theme.dart';
import 'package:fit_4_force/features/community/models/forum_model.dart';
import 'package:fit_4_force/features/community/models/post_model.dart';
import 'package:fit_4_force/shared/models/user_model.dart';
import 'package:fit_4_force/shared/widgets/base_button.dart';

class AgencyForumScreen extends StatefulWidget {
  final UserModel user;
  final String agency;
  final Color color;
  final IconData icon;

  const AgencyForumScreen({
    super.key,
    required this.user,
    required this.agency,
    required this.color,
    required this.icon,
  });

  @override
  State<AgencyForumScreen> createState() => _AgencyForumScreenState();
}

class _AgencyForumScreenState extends State<AgencyForumScreen>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;
  final TextEditingController _searchController = TextEditingController();

  // Mock data for forums
  late List<ForumModel> _forums;

  // Mock data for posts
  late List<PostModel> _posts;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 3, vsync: this);
    _initMockData();
  }

  void _initMockData() {
    // Initialize forums
    _forums = [
      ForumModel(
        id: '1',
        createdAt: DateTime.now().subtract(const Duration(days: 30)),
        name: 'General Discussion',
        description: 'General discussion about ${widget.agency} recruitment and career',
        agency: widget.agency,
        postsCount: 156,
        membersCount: 324,
        topics: ['Recruitment', 'Career', 'General'],
        icon: Icons.forum,
        color: widget.color,
        isPinned: true,
        isOfficial: true,
      ),
      ForumModel(
        id: '2',
        createdAt: DateTime.now().subtract(const Duration(days: 25)),
        name: 'Aptitude Test Preparation',
        description: 'Discuss and share resources for the ${widget.agency} aptitude test',
        agency: widget.agency,
        postsCount: 89,
        membersCount: 210,
        topics: ['Aptitude Test', 'Study Materials', 'Practice Questions'],
        icon: Icons.quiz,
        color: widget.color,
        isPinned: false,
        isOfficial: false,
      ),
      ForumModel(
        id: '3',
        createdAt: DateTime.now().subtract(const Duration(days: 20)),
        name: 'Physical Fitness',
        description: 'Training tips and requirements for the ${widget.agency} physical fitness test',
        agency: widget.agency,
        postsCount: 72,
        membersCount: 185,
        topics: ['Physical Training', 'Fitness Test', 'Workout Plans'],
        icon: Icons.fitness_center,
        color: widget.color,
        isPinned: false,
        isOfficial: false,
      ),
      ForumModel(
        id: '4',
        createdAt: DateTime.now().subtract(const Duration(days: 15)),
        name: 'Interview Preparation',
        description: 'Tips and experiences for the ${widget.agency} interview process',
        agency: widget.agency,
        postsCount: 64,
        membersCount: 142,
        topics: ['Interview', 'Questions', 'Tips'],
        icon: Icons.question_answer,
        color: widget.color,
        isPinned: false,
        isOfficial: true,
      ),
    ];

    // Initialize posts
    _posts = [
      PostModel(
        id: '1',
        createdAt: DateTime.now().subtract(const Duration(hours: 5)),
        userId: 'user1',
        userName: 'John Doe',
        userProfileImageUrl: null,
        title: 'My experience with the ${widget.agency} recruitment process',
        content:
            'I recently went through the ${widget.agency} recruitment process and wanted to share my experience...',
        agency: widget.agency,
        tags: ['Experience', 'Recruitment', 'Tips'],
        likesCount: 24,
        commentsCount: 5,
        isLikedByCurrentUser: true,
        icon: widget.icon,
        color: widget.color,
      ),
      PostModel(
        id: '2',
        createdAt: DateTime.now().subtract(const Duration(days: 1)),
        userId: 'user2',
        userName: 'Jane Smith',
        userProfileImageUrl: null,
        title: 'Study materials for ${widget.agency} aptitude test',
        content:
            'I compiled a list of study materials that helped me prepare for the ${widget.agency} aptitude test...',
        agency: widget.agency,
        tags: ['Study Materials', 'Aptitude Test', 'Resources'],
        likesCount: 42,
        commentsCount: 12,
        isLikedByCurrentUser: false,
        icon: Icons.book,
        color: Colors.blue,
      ),
      PostModel(
        id: '3',
        createdAt: DateTime.now().subtract(const Duration(days: 3)),
        userId: 'user3',
        userName: 'Michael Johnson',
        userProfileImageUrl: null,
        title: 'Physical fitness requirements for ${widget.agency}',
        content:
            'Here are the physical fitness requirements for ${widget.agency} and how I prepared for them...',
        agency: widget.agency,
        tags: ['Physical Fitness', 'Requirements', 'Training'],
        likesCount: 18,
        commentsCount: 7,
        isLikedByCurrentUser: false,
        icon: Icons.fitness_center,
        color: Colors.green,
      ),
    ];
  }

  @override
  void dispose() {
    _tabController.dispose();
    _searchController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(
          '${widget.agency} Forum',
          style: const TextStyle(fontWeight: FontWeight.bold),
        ),
        actions: [
          IconButton(
            icon: const Icon(Icons.search),
            onPressed: () {
              _showSearchDialog();
            },
          ),
          IconButton(
            icon: const Icon(Icons.notifications_outlined),
            onPressed: () {
              // Show notifications
            },
          ),
        ],
        bottom: TabBar(
          controller: _tabController,
          labelColor: widget.color,
          unselectedLabelColor: AppTheme.textSecondaryLight,
          indicatorColor: widget.color,
          indicatorWeight: 3,
          tabs: const [
            Tab(text: 'FORUMS'),
            Tab(text: 'POSTS'),
            Tab(text: 'MEMBERS'),
          ],
        ),
      ),
      body: TabBarView(
        controller: _tabController,
        children: [
          _buildForumsTab(),
          _buildPostsTab(),
          _buildMembersTab(),
        ],
      ),
      floatingActionButton: FloatingActionButton(
        backgroundColor: widget.color,
        child: const Icon(Icons.add),
        onPressed: () {
          _showCreatePostDialog();
        },
      ),
    );
  }

  Widget _buildForumsTab() {
    return ListView.builder(
      padding: const EdgeInsets.all(16),
      itemCount: _forums.length,
      itemBuilder: (context, index) {
        return _buildForumCard(_forums[index]);
      },
    );
  }

  Widget _buildPostsTab() {
    return ListView.builder(
      padding: const EdgeInsets.all(16),
      itemCount: _posts.length,
      itemBuilder: (context, index) {
        return _buildPostCard(_posts[index]);
      },
    );
  }

  Widget _buildMembersTab() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.people,
            size: 80,
            color: Colors.grey.withValues(alpha: 0.3 * 255),
          ),
          const SizedBox(height: 16),
          const Text(
            'Members Directory',
            style: TextStyle(
              fontSize: 24,
              fontWeight: FontWeight.bold,
              color: Colors.black87,
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 8),
          Text(
            'Connect with other ${widget.agency} aspirants',
            style: TextStyle(
              fontSize: 16,
              color: Colors.grey.shade600,
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 24),
          BaseButton(
            text: 'Coming Soon',
            icon: Icons.people,
            backgroundColor: widget.color,
            onPressed: () {
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(
                  content: Text('Members directory coming soon!'),
                ),
              );
            },
          ),
        ],
      ),
    );
  }

  Widget _buildForumCard(ForumModel forum) {
    return Card(
      margin: const EdgeInsets.only(bottom: 16),
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(16),
      ),
      child: InkWell(
        onTap: () => _navigateToForumDetail(forum),
        borderRadius: BorderRadius.circular(16),
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  // Forum icon
                  Container(
                    padding: const EdgeInsets.all(12),
                    decoration: BoxDecoration(
                      color: forum.color.withValues(alpha: 0.1 * 255),
                      shape: BoxShape.circle,
                    ),
                    child: Icon(
                      forum.icon,
                      color: forum.color,
                      size: 24,
                    ),
                  ),
                  const SizedBox(width: 12),
                  // Forum name and stats
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Row(
                          children: [
                            Expanded(
                              child: Text(
                                forum.name,
                                style: const TextStyle(
                                  fontSize: 18,
                                  fontWeight: FontWeight.bold,
                                  color: Colors.black87,
                                ),
                              ),
                            ),
                            if (forum.isPinned)
                              const Icon(
                                Icons.push_pin,
                                color: Colors.grey,
                                size: 16,
                              ),
                            if (forum.isOfficial)
                              Container(
                                margin: const EdgeInsets.only(left: 8),
                                padding: const EdgeInsets.symmetric(
                                  horizontal: 6,
                                  vertical: 2,
                                ),
                                decoration: BoxDecoration(
                                  color: forum.color.withValues(alpha: 0.1 * 255),
                                  borderRadius: BorderRadius.circular(4),
                                ),
                                child: Text(
                                  'Official',
                                  style: TextStyle(
                                    fontSize: 10,
                                    fontWeight: FontWeight.w500,
                                    color: forum.color,
                                  ),
                                ),
                              ),
                          ],
                        ),
                        const SizedBox(height: 4),
                        Text(
                          '${forum.postsCount} posts • ${forum.membersCount} members',
                          style: TextStyle(
                            fontSize: 14,
                            color: Colors.grey.shade600,
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 12),
              // Description
              Text(
                forum.description,
                style: const TextStyle(
                  fontSize: 14,
                  color: Colors.black87,
                ),
                maxLines: 2,
                overflow: TextOverflow.ellipsis,
              ),
              const SizedBox(height: 12),
              // Topics
              Wrap(
                spacing: 8,
                runSpacing: 8,
                children: forum.topics.map((topic) {
                  return Chip(
                    label: Text(
                      topic,
                      style: const TextStyle(fontSize: 12),
                    ),
                    backgroundColor: Colors.grey.withValues(alpha: 0.1 * 255),
                    padding: EdgeInsets.zero,
                    materialTapTargetSize: MaterialTapTargetSize.shrinkWrap,
                  );
                }).toList(),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildPostCard(PostModel post) {
    return Card(
      margin: const EdgeInsets.only(bottom: 16),
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(16),
      ),
      child: InkWell(
        onTap: () => _navigateToPostDetail(post),
        borderRadius: BorderRadius.circular(16),
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Post header
              Row(
                children: [
                  // User avatar
                  CircleAvatar(
                    radius: 20,
                    backgroundColor: post.color.withValues(alpha: 0.2 * 255),
                    child: Text(
                      post.userName.substring(0, 1).toUpperCase(),
                      style: TextStyle(
                        fontWeight: FontWeight.bold,
                        color: post.color,
                      ),
                    ),
                  ),
                  const SizedBox(width: 12),
                  // User name and post info
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          post.userName,
                          style: const TextStyle(
                            fontWeight: FontWeight.bold,
                            fontSize: 15,
                            color: Colors.black87,
                          ),
                        ),
                        const SizedBox(height: 2),
                        Text(
                          _formatTimeAgo(post.createdAt),
                          style: TextStyle(
                            fontSize: 12,
                            color: Colors.grey.shade600,
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 12),
              // Post title
              Text(
                post.title,
                style: const TextStyle(
                  fontWeight: FontWeight.bold,
                  fontSize: 18,
                  color: Colors.black87,
                ),
              ),
              const SizedBox(height: 8),
              // Post content
              Text(
                post.content,
                style: const TextStyle(
                  fontSize: 15,
                  color: Colors.black87,
                ),
                maxLines: 3,
                overflow: TextOverflow.ellipsis,
              ),
              const SizedBox(height: 12),
              // Post stats
              Row(
                children: [
                  Icon(
                    Icons.favorite,
                    size: 16,
                    color: post.isLikedByCurrentUser ? Colors.red : Colors.grey,
                  ),
                  const SizedBox(width: 4),
                  Text(
                    post.likesCount.toString(),
                    style: TextStyle(
                      fontSize: 12,
                      color: Colors.grey.shade600,
                    ),
                  ),
                  const SizedBox(width: 16),
                  const Icon(
                    Icons.comment,
                    size: 16,
                    color: Colors.grey,
                  ),
                  const SizedBox(width: 4),
                  Text(
                    post.commentsCount.toString(),
                    style: TextStyle(
                      fontSize: 12,
                      color: Colors.grey.shade600,
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  void _showSearchDialog() {
    showDialog(
      context: context,
      builder: (context) {
        return AlertDialog(
          title: Text('Search ${widget.agency} Forum'),
          content: TextField(
            controller: _searchController,
            decoration: const InputDecoration(
              hintText: 'Enter keywords...',
              prefixIcon: Icon(Icons.search),
            ),
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.pop(context),
              child: const Text('CANCEL'),
            ),
            TextButton(
              onPressed: () {
                // Perform search
                Navigator.pop(context);
                // Update UI with search results
              },
              child: const Text('SEARCH'),
            ),
          ],
        );
      },
    );
  }

  void _showCreatePostDialog() {
    final titleController = TextEditingController();
    final contentController = TextEditingController();

    showDialog(
      context: context,
      builder: (context) {
        return AlertDialog(
          title: Text('Create Post in ${widget.agency} Forum'),
          content: SingleChildScrollView(
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                TextField(
                  controller: titleController,
                  decoration: const InputDecoration(
                    labelText: 'Title',
                    hintText: 'Enter a title for your post',
                  ),
                ),
                const SizedBox(height: 16),
                TextField(
                  controller: contentController,
                  decoration: const InputDecoration(
                    labelText: 'Content',
                    hintText: 'Write your post content here',
                  ),
                  maxLines: 5,
                ),
              ],
            ),
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.pop(context),
              child: const Text('CANCEL'),
            ),
            TextButton(
              onPressed: () {
                // Create post
                if (titleController.text.isNotEmpty &&
                    contentController.text.isNotEmpty) {
                  // Add post to the list
                  setState(() {
                    _posts.insert(
                      0,
                      PostModel(
                        id: DateTime.now().millisecondsSinceEpoch.toString(),
                        createdAt: DateTime.now(),
                        userId: widget.user.id,
                        userName: widget.user.fullName,
                        userProfileImageUrl: widget.user.profileImageUrl,
                        title: titleController.text,
                        content: contentController.text,
                        agency: widget.agency,
                        tags: [],
                        likesCount: 0,
                        commentsCount: 0,
                        isLikedByCurrentUser: false,
                        icon: widget.icon,
                        color: widget.color,
                      ),
                    );
                  });
                  Navigator.pop(context);
                  ScaffoldMessenger.of(context).showSnackBar(
                    const SnackBar(content: Text('Post created successfully!')),
                  );
                }
              },
              child: const Text('POST'),
            ),
          ],
        );
      },
    );
  }

  void _navigateToForumDetail(ForumModel forum) {
    // Navigate to forum detail screen
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(content: Text('Viewing forum: ${forum.name}')),
    );
  }

  void _navigateToPostDetail(PostModel post) {
    // Navigate to post detail screen
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(content: Text('Viewing post: ${post.title}')),
    );
  }

  String _formatTimeAgo(DateTime dateTime) {
    final difference = DateTime.now().difference(dateTime);
    if (difference.inDays > 0) {
      return '${difference.inDays}d ago';
    } else if (difference.inHours > 0) {
      return '${difference.inHours}h ago';
    } else if (difference.inMinutes > 0) {
      return '${difference.inMinutes}m ago';
    } else {
      return 'Just now';
    }
  }
}
