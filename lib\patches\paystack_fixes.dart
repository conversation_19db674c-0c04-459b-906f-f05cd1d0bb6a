import 'dart:io';
import 'package:logger/logger.dart';

/// This class contains methods to fix the Flutter Paystack package
class PaystackFixes {
  /// The path to the Flutter Paystack package
  final String paystackPath;

  /// Logger instance
  final Logger logger = Logger();

  /// Constructor
  PaystackFixes({required this.paystackPath});

  /// Fix the checkout_widget.dart file
  Future<bool> fixCheckoutWidget() async {
    final filePath =
        '$paystackPath/lib/src/widgets/checkout/checkout_widget.dart';
    final file = File(filePath);

    if (!file.existsSync()) {
      logger.w('File not found: $filePath');
      return false;
    }

    try {
      // Create backup
      final backupPath = '$filePath.bak';
      await file.copy(backupPath);
      logger.i('Created backup at $backupPath');

      // Read file content
      var content = await file.readAsString();

      // Apply fixes
      content = content.replaceAll('bodyText1', 'bodyLarge');
      content = content.replaceAll('vsync: this,', '');

      // Write fixed content
      await file.writeAsString(content);
      logger.i('Fixed checkout_widget.dart');
      return true;
    } catch (e) {
      logger.e('Error fixing checkout_widget.dart: $e');
      return false;
    }
  }

  /// Fix the buttons.dart file
  Future<bool> fixButtonsDart() async {
    final filePath = '$paystackPath/lib/src/widgets/buttons.dart';
    final file = File(filePath);

    if (!file.existsSync()) {
      logger.w('File not found: $filePath');
      return false;
    }

    try {
      // Create backup
      final backupPath = '$filePath.bak';
      await file.copy(backupPath);
      logger.i('Created backup at $backupPath');

      // Read file content
      var content = await file.readAsString();

      // Apply fixes
      content = content.replaceAll('accentColor', 'colorScheme.secondary');

      // Write fixed content
      await file.writeAsString(content);
      logger.i('Fixed buttons.dart');
      return true;
    } catch (e) {
      logger.e('Error fixing buttons.dart: $e');
      return false;
    }
  }

  /// Fix all files
  Future<bool> fixAll() async {
    final checkoutFixed = await fixCheckoutWidget();
    final buttonsFixed = await fixButtonsDart();
    return checkoutFixed && buttonsFixed;
  }
}

/// Main function to run the fixes
void main() async {
  final logger = Logger();

  final pubCachePath =
      Platform.isWindows
          ? '${Platform.environment['LOCALAPPDATA']}\\Pub\\Cache'
          : '${Platform.environment['HOME']}/.pub-cache';

  final paystackPath =
      '$pubCachePath\\git\\flutter_paystack-a4a33c3dd0a12f46d655a2e63d11e9f20ba82d01';
  final fixes = PaystackFixes(paystackPath: paystackPath);
  final success = await fixes.fixAll();

  if (success) {
    logger.i('All fixes applied successfully!');
  } else {
    logger.w('Some fixes failed. Please check the logs above.');
  }
}
