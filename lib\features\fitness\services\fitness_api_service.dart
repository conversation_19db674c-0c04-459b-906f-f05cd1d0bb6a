import 'package:fit_4_force/core/services/api_service.dart';

/// API service for fitness-related endpoints
class FitnessApiService {
  /// Base API service
  final ApiService _apiService;

  /// Constructor
  FitnessApiService({ApiService? apiService})
    : _apiService = apiService ?? ApiService();

  /// Get fitness plans by agency
  Future<List<dynamic>> getFitnessPlans(
    String agency,
    String token, {
    int page = 1,
    int limit = 10,
  }) async {
    final response = await _apiService.get(
      '/fitness/plans',
      queryParams: {'agency': agency, 'page': page, 'limit': limit},
      token: token,
    );

    return response['data'];
  }

  /// Get fitness plan by ID
  Future<Map<String, dynamic>> getFitnessPlanById(
    String planId,
    String token,
  ) async {
    final response = await _apiService.get(
      '/fitness/plans/$planId',
      token: token,
    );

    return response;
  }

  /// Get user fitness profile
  Future<Map<String, dynamic>> getUserFitnessProfile(
    String userId,
    String token,
  ) async {
    final response = await _apiService.get(
      '/fitness/profile/$userId',
      token: token,
    );

    return response;
  }

  /// Update user fitness profile
  Future<Map<String, dynamic>> updateUserFitnessProfile(
    String userId,
    Map<String, dynamic> data,
    String token,
  ) async {
    final response = await _apiService.put(
      '/fitness/profile/$userId',
      body: data,
      token: token,
    );

    return response;
  }

  /// Log fitness activity
  Future<Map<String, dynamic>> logFitnessActivity(
    Map<String, dynamic> data,
    String token,
  ) async {
    final response = await _apiService.post(
      '/fitness/activities',
      body: data,
      token: token,
    );

    return response;
  }

  /// Get user fitness activities
  Future<List<dynamic>> getUserFitnessActivities(
    String userId,
    String token, {
    int page = 1,
    int limit = 10,
    String? startDate,
    String? endDate,
  }) async {
    final queryParams = <String, dynamic>{'page': page, 'limit': limit};

    if (startDate != null) {
      queryParams['startDate'] = startDate;
    }

    if (endDate != null) {
      queryParams['endDate'] = endDate;
    }

    final response = await _apiService.get(
      '/fitness/activities/$userId',
      queryParams: queryParams,
      token: token,
    );

    return response['data'];
  }

  /// Generate personalized fitness plan
  Future<Map<String, dynamic>> generatePersonalizedPlan(
    Map<String, dynamic> userProfile,
    String agency,
    String token,
  ) async {
    final response = await _apiService.post(
      '/fitness/generate-plan',
      body: {'userProfile': userProfile, 'agency': agency},
      token: token,
    );

    return response;
  }
}
