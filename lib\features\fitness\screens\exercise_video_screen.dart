import 'dart:io';
import 'package:flutter/material.dart';
import 'package:fit_4_force/features/fitness/models/workout_model.dart';
import 'package:fit_4_force/features/fitness/screens/exercise_detail_screen.dart';
import 'package:video_player/video_player.dart';
import 'package:chewie/chewie.dart';
import 'package:path_provider/path_provider.dart';
import 'package:http/http.dart' as http;

class ExerciseVideoScreen extends StatefulWidget {
  final ExerciseModel exercise;

  const ExerciseVideoScreen({super.key, required this.exercise});

  @override
  State<ExerciseVideoScreen> createState() => _ExerciseVideoScreenState();
}

class _ExerciseVideoScreenState extends State<ExerciseVideoScreen> {
  late VideoPlayerController _videoPlayerController;
  ChewieController? _chewieController;
  bool _isLoading = true;
  bool _hasError = false;
  String _errorMessage = '';
  bool _isDownloading = false;
  double _downloadProgress = 0.0;
  bool _isVideoDownloaded = false;
  String? _localVideoPath;
  List<ExerciseModel> _relatedExercises = [];

  @override
  void initState() {
    super.initState();
    _checkIfVideoIsDownloaded();
    _loadRelatedExercises();
  }

  Future<void> _checkIfVideoIsDownloaded() async {
    if (widget.exercise.videoUrl.startsWith('http')) {
      final videoFileName = _getVideoFileName(widget.exercise.videoUrl);
      final directory = await getApplicationDocumentsDirectory();
      final filePath = '${directory.path}/videos/$videoFileName';
      final file = File(filePath);

      if (await file.exists()) {
        setState(() {
          _isVideoDownloaded = true;
          _localVideoPath = filePath;
        });
      }
    }

    _initializePlayer();
  }

  String _getVideoFileName(String url) {
    // Extract filename from URL and sanitize it
    final uri = Uri.parse(url);
    final pathSegments = uri.pathSegments;
    if (pathSegments.isNotEmpty) {
      return pathSegments.last.replaceAll(RegExp(r'[^\w\s\-\.]'), '_');
    }
    // Fallback: use a hash of the URL
    return 'video_${url.hashCode.abs()}.mp4';
  }

  Future<void> _initializePlayer() async {
    try {
      // Check if the video URL is a network URL or an asset
      final isNetworkUrl = widget.exercise.videoUrl.startsWith('http');

      if (isNetworkUrl) {
        if (_isVideoDownloaded && _localVideoPath != null) {
          // Use the cached video file
          _videoPlayerController = VideoPlayerController.file(
            File(_localVideoPath!),
          );
        } else {
          // Stream from network
          _videoPlayerController = VideoPlayerController.network(
            widget.exercise.videoUrl,
          );
        }
      } else {
        // Use asset
        _videoPlayerController = VideoPlayerController.asset(
          widget.exercise.videoUrl,
        );
      }

      await _videoPlayerController.initialize();

      _chewieController = ChewieController(
        videoPlayerController: _videoPlayerController,
        autoPlay: true,
        looping: false,
        aspectRatio: _videoPlayerController.value.aspectRatio,
        errorBuilder: (context, errorMessage) {
          return Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                const Icon(Icons.error_outline, color: Colors.red, size: 48),
                const SizedBox(height: 16),
                Text(
                  'Error: $errorMessage',
                  style: const TextStyle(color: Colors.white),
                  textAlign: TextAlign.center,
                ),
              ],
            ),
          );
        },
      );

      setState(() {
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _isLoading = false;
        _hasError = true;
        _errorMessage = e.toString();
      });
    }
  }

  Future<void> _downloadVideo() async {
    if (!widget.exercise.videoUrl.startsWith('http') || _isVideoDownloaded) {
      return;
    }

    setState(() {
      _isDownloading = true;
      _downloadProgress = 0.0;
    });

    try {
      final videoFileName = _getVideoFileName(widget.exercise.videoUrl);
      final directory = await getApplicationDocumentsDirectory();

      // Create videos directory if it doesn't exist
      final videosDir = Directory('${directory.path}/videos');
      if (!await videosDir.exists()) {
        await videosDir.create(recursive: true);
      }

      final filePath = '${videosDir.path}/$videoFileName';
      final file = File(filePath);

      // Download the file
      final response = await http.get(
        Uri.parse(widget.exercise.videoUrl),
        headers: {'Accept-Encoding': 'gzip'},
      );

      if (response.statusCode == 200) {
        await file.writeAsBytes(response.bodyBytes);

        setState(() {
          _isVideoDownloaded = true;
          _localVideoPath = filePath;
          _isDownloading = false;

          // Reinitialize player with the downloaded file
          _disposeCurrentPlayer();
          _initializePlayer();
        });

        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Video downloaded successfully'),
            duration: Duration(seconds: 2),
          ),
        );
      } else {
        throw Exception('Failed to download video: ${response.statusCode}');
      }
    } catch (e) {
      setState(() {
        _isDownloading = false;
        _hasError = true;
        _errorMessage = 'Download failed: ${e.toString()}';
      });

      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Failed to download video: ${e.toString()}'),
          backgroundColor: Colors.red,
          duration: const Duration(seconds: 3),
        ),
      );
    }
  }

  void _loadRelatedExercises() {
    // In a real app, you would fetch related exercises from your backend
    // For now, we'll create mock related exercises

    final muscleGroup = _determineMuscleGroup(widget.exercise.name);

    _relatedExercises = [
      ExerciseModel(
        id: 'related1',
        name: 'Related Exercise 1',
        description: 'Another exercise targeting the same muscle group',
        imageUrl: 'assets/images/exercises/related1.png',
        videoUrl: 'https://example.com/videos/related1.mp4',
        duration: 0,
        sets: 3,
        reps: 12,
        restTime: 30,
      ),
      ExerciseModel(
        id: 'related2',
        name: 'Related Exercise 2',
        description: 'Alternative exercise for $muscleGroup',
        imageUrl: 'assets/images/exercises/related2.png',
        videoUrl: 'https://example.com/videos/related2.mp4',
        duration: 30,
        sets: 1,
        reps: 0,
        restTime: 15,
      ),
      ExerciseModel(
        id: 'related3',
        name: 'Related Exercise 3',
        description: 'Complementary exercise for $muscleGroup',
        imageUrl: 'assets/images/exercises/related3.png',
        videoUrl: 'https://example.com/videos/related3.mp4',
        duration: 0,
        sets: 3,
        reps: 10,
        restTime: 45,
      ),
    ];
  }

  String _determineMuscleGroup(String exerciseName) {
    final name = exerciseName.toLowerCase();

    if (name.contains('push-up') || name.contains('bench press')) {
      return 'chest';
    } else if (name.contains('squat') || name.contains('lunge')) {
      return 'legs';
    } else if (name.contains('pull-up') || name.contains('row')) {
      return 'back';
    } else if (name.contains('curl') || name.contains('chin-up')) {
      return 'biceps';
    } else if (name.contains('tricep') || name.contains('dip')) {
      return 'triceps';
    } else if (name.contains('shoulder') || name.contains('press')) {
      return 'shoulders';
    } else if (name.contains('crunch') ||
        name.contains('sit-up') ||
        name.contains('plank')) {
      return 'core';
    } else if (name.contains('jumping') ||
        name.contains('cardio') ||
        name.contains('run')) {
      return 'cardio';
    }

    return 'full body';
  }

  void _disposeCurrentPlayer() {
    _chewieController?.dispose();
    _videoPlayerController.dispose();
  }

  @override
  void dispose() {
    _videoPlayerController.dispose();
    _chewieController?.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.black,
      appBar: AppBar(
        backgroundColor: Colors.transparent,
        elevation: 0,
        title: Text(
          widget.exercise.name,
          style: const TextStyle(color: Colors.white),
        ),
        iconTheme: const IconThemeData(color: Colors.white),
      ),
      body: SafeArea(
        child:
            _isLoading
                ? const Center(
                  child: CircularProgressIndicator(color: Colors.white),
                )
                : _hasError
                ? _buildErrorWidget()
                : _buildVideoPlayer(),
      ),
    );
  }

  Widget _buildVideoPlayer() {
    return Column(
      children: [
        Expanded(
          child: Center(
            child:
                _chewieController != null
                    ? Chewie(controller: _chewieController!)
                    : const CircularProgressIndicator(color: Colors.white),
          ),
        ),
        _buildExerciseInfo(),
      ],
    );
  }

  Widget _buildErrorWidget() {
    return Center(
      child: Padding(
        padding: const EdgeInsets.all(24.0),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Icon(Icons.error_outline, color: Colors.red, size: 64),
            const SizedBox(height: 24),
            Text(
              'Failed to load video',
              style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                color: Colors.white,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),
            Text(
              _errorMessage,
              style: const TextStyle(color: Colors.white70),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 32),
            ElevatedButton.icon(
              onPressed: () {
                setState(() {
                  _isLoading = true;
                  _hasError = false;
                });
                _initializePlayer();
              },
              icon: const Icon(Icons.refresh),
              label: const Text('Retry'),
              style: ElevatedButton.styleFrom(
                padding: const EdgeInsets.symmetric(
                  horizontal: 24,
                  vertical: 12,
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildExerciseInfo() {
    return Container(
      padding: const EdgeInsets.all(16),
      color: Colors.black87,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Expanded(
                child: Text(
                  widget.exercise.name,
                  style: const TextStyle(
                    color: Colors.white,
                    fontSize: 20,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
              // Download button
              if (widget.exercise.videoUrl.startsWith('http'))
                _isVideoDownloaded
                    ? const Icon(
                      Icons.check_circle,
                      color: Colors.green,
                      size: 24,
                    )
                    : _isDownloading
                    ? const SizedBox(
                      width: 24,
                      height: 24,
                      child: CircularProgressIndicator(
                        color: Colors.white,
                        strokeWidth: 2,
                      ),
                    )
                    : IconButton(
                      icon: const Icon(Icons.download, color: Colors.white),
                      onPressed: _downloadVideo,
                      tooltip: 'Download for offline viewing',
                    ),
            ],
          ),
          const SizedBox(height: 8),
          Text(
            widget.exercise.description,
            style: const TextStyle(color: Colors.white70, fontSize: 14),
          ),
          const SizedBox(height: 16),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceAround,
            children: [
              _buildInfoItem(
                Icons.fitness_center,
                widget.exercise.duration > 0
                    ? '${widget.exercise.duration}s'
                    : '${widget.exercise.reps} reps',
              ),
              _buildInfoItem(Icons.repeat, '${widget.exercise.sets} sets'),
              _buildInfoItem(
                Icons.timer_outlined,
                '${widget.exercise.restTime}s rest',
              ),
            ],
          ),

          const SizedBox(height: 24),

          // Related exercises section
          const Text(
            'Related Exercises',
            style: TextStyle(
              color: Colors.white,
              fontSize: 18,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 16),
          SizedBox(
            height: 120,
            child: ListView.builder(
              scrollDirection: Axis.horizontal,
              itemCount: _relatedExercises.length,
              itemBuilder: (context, index) {
                final exercise = _relatedExercises[index];
                return _buildRelatedExerciseCard(exercise);
              },
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildRelatedExerciseCard(ExerciseModel exercise) {
    return GestureDetector(
      onTap: () {
        Navigator.push(
          context,
          MaterialPageRoute(
            builder: (context) => ExerciseDetailScreen(exercise: exercise),
          ),
        );
      },
      child: Container(
        width: 160,
        margin: const EdgeInsets.only(right: 16),
        decoration: BoxDecoration(
          color: Colors.black54,
          borderRadius: BorderRadius.circular(12),
          border: Border.all(color: Colors.grey.shade800),
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Exercise image
            ClipRRect(
              borderRadius: const BorderRadius.vertical(
                top: Radius.circular(12),
              ),
              child: Image.asset(
                exercise.imageUrl,
                height: 70,
                width: double.infinity,
                fit: BoxFit.cover,
                errorBuilder: (context, error, stackTrace) {
                  return Container(
                    height: 70,
                    color: Colors.grey.shade800,
                    child: const Icon(
                      Icons.fitness_center,
                      color: Colors.white54,
                      size: 30,
                    ),
                  );
                },
              ),
            ),

            // Exercise info
            Padding(
              padding: const EdgeInsets.all(8.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    exercise.name,
                    style: const TextStyle(
                      color: Colors.white,
                      fontWeight: FontWeight.bold,
                      fontSize: 12,
                    ),
                    maxLines: 1,
                    overflow: TextOverflow.ellipsis,
                  ),
                  const SizedBox(height: 2),
                  Text(
                    exercise.duration > 0
                        ? '${exercise.duration}s'
                        : '${exercise.reps} reps × ${exercise.sets} sets',
                    style: const TextStyle(color: Colors.white70, fontSize: 10),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildInfoItem(IconData icon, String text) {
    return Column(
      children: [
        Icon(icon, color: Colors.white70, size: 24),
        const SizedBox(height: 4),
        Text(text, style: const TextStyle(color: Colors.white70, fontSize: 12)),
      ],
    );
  }
}
