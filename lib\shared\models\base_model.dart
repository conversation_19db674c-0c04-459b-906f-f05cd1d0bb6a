import 'package:equatable/equatable.dart';

abstract class BaseModel extends Equatable {
  final String id;
  final DateTime createdAt;
  final DateTime? updatedAt;

  const BaseModel({
    required this.id,
    required this.createdAt,
    this.updatedAt,
  });

  @override
  List<Object?> get props => [id, createdAt, updatedAt];

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'createdAt': createdAt.toIso8601String(),
      'updatedAt': updatedAt?.toIso8601String(),
    };
  }

  BaseModel copyWith({
    String? id,
    DateTime? createdAt,
    DateTime? updatedAt,
  });
} 