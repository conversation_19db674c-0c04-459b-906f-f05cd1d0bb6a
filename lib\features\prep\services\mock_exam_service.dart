import 'package:flutter/material.dart';
import 'package:fit_4_force/features/prep/models/mock_exam_model.dart';

class MockExamService {
  // Singleton pattern
  static final MockExamService _instance = MockExamService._internal();
  
  factory MockExamService() {
    return _instance;
  }
  
  MockExamService._internal();
  
  // Sample mock exams
  final List<MockExamModel> _mockExams = [
    MockExamModel(
      id: '1',
      title: 'General Knowledge Test',
      description: 'Test your general knowledge about Nigeria and current affairs.',
      category: 'General Knowledge',
      timeLimit: 30,
      questions: [
        ExamQuestionModel(
          id: '1',
          text: 'What is the capital of Nigeria?',
          type: QuestionType.multipleChoice,
          options: ['Lagos', 'Abuja', 'Kano', 'Port Harcourt'],
          correctAnswers: [1], // Abuja
          explanation: 'Abuja has been the capital of Nigeria since December 12, 1991.',
        ),
        ExamQuestionModel(
          id: '2',
          text: 'Nigeria gained independence in what year?',
          type: QuestionType.multipleChoice,
          options: ['1957', '1960', '1963', '1970'],
          correctAnswers: [1], // 1960
          explanation: 'Nigeria gained independence from British colonial rule on October 1, 1960.',
        ),
        ExamQuestionModel(
          id: '3',
          text: 'The Nigerian flag consists of green and white stripes.',
          type: QuestionType.trueFalse,
          options: ['True', 'False'],
          correctAnswers: [0], // True
          explanation: 'The Nigerian flag consists of three vertical stripes - green, white, green.',
        ),
        ExamQuestionModel(
          id: '4',
          text: 'Who is the current President of Nigeria?',
          type: QuestionType.shortAnswer,
          correctAnswer: 'Bola Ahmed Tinubu',
          explanation: 'Bola Ahmed Tinubu was sworn in as President of Nigeria on May 29, 2023.',
        ),
        ExamQuestionModel(
          id: '5',
          text: 'Which of the following are official languages in Nigeria?',
          type: QuestionType.multipleChoice,
          options: ['English', 'French', 'Hausa', 'Yoruba'],
          correctAnswers: [0, 2, 3], // English, Hausa, Yoruba
          explanation: 'English is the official language, while Hausa, Yoruba, and Igbo (not listed) are recognized regional languages.',
          points: 3,
        ),
      ],
      color: Colors.blue,
      createdAt: DateTime.now().subtract(const Duration(days: 30)),
      totalAttempts: 245,
      averageScore: 72.5,
    ),
    
    MockExamModel(
      id: '2',
      title: 'Mathematics Aptitude Test',
      description: 'Test your mathematical skills with this comprehensive exam.',
      category: 'Mathematics',
      timeLimit: 45,
      questions: [
        ExamQuestionModel(
          id: '1',
          text: 'Solve for x: 2x + 5 = 15',
          type: QuestionType.multipleChoice,
          options: ['x = 5', 'x = 10', 'x = 7.5', 'x = 5.5'],
          correctAnswers: [0], // x = 5
          explanation: '2x + 5 = 15\n2x = 10\nx = 5',
        ),
        ExamQuestionModel(
          id: '2',
          text: 'What is the area of a circle with radius 4 cm?',
          type: QuestionType.multipleChoice,
          options: ['16π cm²', '8π cm²', '4π cm²', '12π cm²'],
          correctAnswers: [0], // 16π cm²
          explanation: 'Area of a circle = πr². With r = 4, Area = π(4)² = 16π cm²',
        ),
        ExamQuestionModel(
          id: '3',
          text: 'If a car travels at 60 km/h, how far will it travel in 2.5 hours?',
          type: QuestionType.shortAnswer,
          correctAnswer: '150 km',
          explanation: 'Distance = Speed × Time = 60 km/h × 2.5 h = 150 km',
        ),
        ExamQuestionModel(
          id: '4',
          text: 'The sum of interior angles of a triangle is 180 degrees.',
          type: QuestionType.trueFalse,
          options: ['True', 'False'],
          correctAnswers: [0], // True
          explanation: 'The sum of interior angles in any triangle is always 180 degrees.',
        ),
        ExamQuestionModel(
          id: '5',
          text: 'Solve the quadratic equation: x² - 5x + 6 = 0',
          type: QuestionType.multipleChoice,
          options: ['x = 2, x = 3', 'x = -2, x = -3', 'x = 2, x = -3', 'x = -2, x = 3'],
          correctAnswers: [0], // x = 2, x = 3
          explanation: 'Using the quadratic formula or factoring: x² - 5x + 6 = 0\n(x - 2)(x - 3) = 0\nx = 2 or x = 3',
          points: 2,
        ),
      ],
      color: Colors.green,
      createdAt: DateTime.now().subtract(const Duration(days: 20)),
      totalAttempts: 189,
      averageScore: 68.2,
    ),
    
    MockExamModel(
      id: '3',
      title: 'Physical Fitness Assessment',
      description: 'Test your knowledge about physical fitness and training.',
      category: 'Physical Training',
      timeLimit: 20,
      questions: [
        ExamQuestionModel(
          id: '1',
          text: 'What is the minimum number of push-ups required for male applicants in most Nigerian military fitness tests?',
          type: QuestionType.multipleChoice,
          options: ['10', '20', '30', '40'],
          correctAnswers: [2], // 30
          explanation: 'Most Nigerian military agencies require a minimum of 30 push-ups for male applicants.',
        ),
        ExamQuestionModel(
          id: '2',
          text: 'How many sit-ups should be performed in 2 minutes to meet the standard requirements?',
          type: QuestionType.multipleChoice,
          options: ['25', '35', '45', '55'],
          correctAnswers: [2], // 45
          explanation: 'The standard requirement is typically 45 sit-ups in 2 minutes.',
        ),
        ExamQuestionModel(
          id: '3',
          text: 'The Cooper test measures cardiovascular fitness by running for 12 minutes.',
          type: QuestionType.trueFalse,
          options: ['True', 'False'],
          correctAnswers: [0], // True
          explanation: 'The Cooper test requires running as far as possible in 12 minutes to assess cardiovascular fitness.',
        ),
        ExamQuestionModel(
          id: '4',
          text: 'What is the minimum time required to complete a 2.4 km run for male applicants?',
          type: QuestionType.multipleChoice,
          options: ['8 minutes', '10 minutes', '12 minutes', '15 minutes'],
          correctAnswers: [1], // 10 minutes
          explanation: 'Most agencies require male applicants to complete a 2.4 km run in 10 minutes or less.',
        ),
        ExamQuestionModel(
          id: '5',
          text: 'List three benefits of interval training.',
          type: QuestionType.essay,
          explanation: 'Benefits of interval training include: improved cardiovascular fitness, increased calorie burn, improved speed and endurance, reduced training time, and prevention of plateaus.',
          points: 3,
        ),
      ],
      isPremium: true,
      color: Colors.red,
      createdAt: DateTime.now().subtract(const Duration(days: 15)),
      totalAttempts: 132,
      averageScore: 75.8,
    ),
  ];
  
  // Sample exam attempts
  final List<ExamAttemptModel> _examAttempts = [];
  
  // Get all mock exams
  List<MockExamModel> getAllExams() {
    return _mockExams;
  }
  
  // Get mock exams by category
  List<MockExamModel> getExamsByCategory(String category) {
    return _mockExams.where((exam) => exam.category == category).toList();
  }
  
  // Get a specific exam by ID
  MockExamModel? getExamById(String id) {
    try {
      return _mockExams.firstWhere((exam) => exam.id == id);
    } catch (e) {
      return null;
    }
  }
  
  // Get all attempts for a user
  List<ExamAttemptModel> getUserAttempts(String userId) {
    return _examAttempts.where((attempt) => attempt.userId == userId).toList();
  }
  
  // Get attempts for a specific exam by a user
  List<ExamAttemptModel> getUserExamAttempts(String userId, String examId) {
    return _examAttempts.where(
      (attempt) => attempt.userId == userId && attempt.examId == examId
    ).toList();
  }
  
  // Start a new exam attempt
  ExamAttemptModel startExam(String examId, String userId) {
    final attempt = ExamAttemptModel(
      id: DateTime.now().millisecondsSinceEpoch.toString(),
      examId: examId,
      userId: userId,
      startTime: DateTime.now(),
      answers: {},
    );
    
    _examAttempts.add(attempt);
    return attempt;
  }
  
  // Save an answer during an exam
  void saveAnswer(String attemptId, String questionId, dynamic answer) {
    final index = _examAttempts.indexWhere((attempt) => attempt.id == attemptId);
    if (index != -1) {
      final attempt = _examAttempts[index];
      final answers = Map<String, dynamic>.from(attempt.answers);
      answers[questionId] = answer;
      
      _examAttempts[index] = ExamAttemptModel(
        id: attempt.id,
        examId: attempt.examId,
        userId: attempt.userId,
        startTime: attempt.startTime,
        endTime: attempt.endTime,
        answers: answers,
        score: attempt.score,
        totalPossible: attempt.totalPossible,
        isCompleted: attempt.isCompleted,
      );
    }
  }
  
  // Complete an exam and calculate score
  ExamResultModel completeExam(String attemptId) {
    final index = _examAttempts.indexWhere((attempt) => attempt.id == attemptId);
    if (index == -1) {
      throw Exception('Attempt not found');
    }
    
    final attempt = _examAttempts[index];
    final exam = getExamById(attempt.examId);
    
    if (exam == null) {
      throw Exception('Exam not found');
    }
    
    // Calculate score
    int score = 0;
    int totalPossible = exam.totalPoints;
    Map<String, bool> questionResults = {};
    Map<String, String> feedback = {};
    
    for (var question in exam.questions) {
      final answer = attempt.answers[question.id];
      bool isCorrect = false;
      
      if (answer != null) {
        switch (question.type) {
          case QuestionType.multipleChoice:
            isCorrect = question.isMultipleChoiceAnswerCorrect(answer as List<int>);
            break;
          case QuestionType.trueFalse:
            isCorrect = question.isTrueFalseAnswerCorrect(answer as bool);
            break;
          case QuestionType.shortAnswer:
            isCorrect = question.isShortAnswerCorrect(answer as String);
            break;
          case QuestionType.essay:
            // Essay questions need manual grading
            isCorrect = false;
            break;
        }
        
        if (isCorrect) {
          score += question.points;
        }
      }
      
      questionResults[question.id] = isCorrect;
      feedback[question.id] = question.explanation ?? '';
    }
    
    // Update attempt
    final now = DateTime.now();
    _examAttempts[index] = ExamAttemptModel(
      id: attempt.id,
      examId: attempt.examId,
      userId: attempt.userId,
      startTime: attempt.startTime,
      endTime: now,
      answers: attempt.answers,
      score: score,
      totalPossible: totalPossible,
      isCompleted: true,
    );
    
    // Create and return result
    return ExamResultModel(
      attemptId: attempt.id,
      examId: exam.id,
      examTitle: exam.title,
      score: score,
      totalPossible: totalPossible,
      percentage: totalPossible > 0 ? (score / totalPossible) * 100 : 0,
      timeTakenMinutes: now.difference(attempt.startTime).inMinutes,
      completedAt: now,
      questionResults: questionResults,
      feedback: feedback,
    );
  }
}
