import 'package:flutter/foundation.dart';
import 'package:fit_4_force/core/services/backend_service_manager.dart';
import 'package:fit_4_force/core/config/supabase_config.dart';
import 'package:logger/logger.dart';

/// Development-only backend testing suite
/// This should NEVER be included in production builds
class BackendTestSuite {
  static final BackendTestSuite _instance = BackendTestSuite._internal();
  factory BackendTestSuite() => _instance;
  BackendTestSuite._internal();

  final Logger _logger = Logger();
  final BackendServiceManager _serviceManager = BackendServiceManager();

  /// Run comprehensive backend tests (DEBUG MODE ONLY)
  Future<Map<String, dynamic>> runTests() async {
    if (!kDebugMode) {
      _logger.w('⚠️ Backend tests can only run in debug mode');
      return {'error': 'Tests only available in debug mode'};
    }

    _logger.i('🧪 Starting Fit4Force Backend Test Suite...');
    
    final results = <String, dynamic>{
      'timestamp': DateTime.now().toIso8601String(),
      'tests': <String, dynamic>{},
    };

    // Test 1: Supabase Connection
    results['tests']['supabase_connection'] = await _testSupabaseConnection();
    
    // Test 2: Service Manager
    results['tests']['service_manager'] = await _testServiceManager();
    
    // Test 3: Database Operations
    results['tests']['database_operations'] = await _testDatabaseOperations();
    
    // Test 4: Real-time Subscriptions
    results['tests']['realtime_subscriptions'] = await _testRealtimeSubscriptions();
    
    // Test 5: Storage Operations
    results['tests']['storage_operations'] = await _testStorageOperations();
    
    // Test 6: Notification Service
    results['tests']['notification_service'] = await _testNotificationService();

    _logger.i('✅ Backend test suite completed');
    return results;
  }

  /// Test Supabase connection
  Future<Map<String, dynamic>> _testSupabaseConnection() async {
    try {
      final isInitialized = SupabaseConfig.isInitialized;
      final client = SupabaseConfig.client;
      
      // Try a simple query
      final response = await client
          .from('users')
          .select('count')
          .limit(1);

      return {
        'status': 'passed',
        'initialized': isInitialized,
        'query_test': response != null,
        'url': SupabaseConfig.supabaseUrl,
      };
    } catch (e) {
      return {
        'status': 'failed',
        'error': e.toString(),
        'initialized': SupabaseConfig.isInitialized,
      };
    }
  }

  /// Test service manager
  Future<Map<String, dynamic>> _testServiceManager() async {
    try {
      final isInitialized = _serviceManager.isInitialized;
      final isConnected = _serviceManager.isConnected;
      final healthCheck = await _serviceManager.healthCheck();
      final stats = await _serviceManager.getServiceStatistics();

      return {
        'status': 'passed',
        'initialized': isInitialized,
        'connected': isConnected,
        'health_check': healthCheck,
        'statistics': stats,
      };
    } catch (e) {
      return {
        'status': 'failed',
        'error': e.toString(),
      };
    }
  }

  /// Test database operations
  Future<Map<String, dynamic>> _testDatabaseOperations() async {
    try {
      final client = SupabaseConfig.client;
      
      // Test reading from different tables
      final tests = <String, dynamic>{};
      
      final tables = ['users', 'workouts', 'exercises', 'posts'];
      for (final table in tables) {
        try {
          final response = await client
              .from(table)
              .select('*')
              .limit(1);
          
          tests[table] = {
            'status': 'passed',
            'has_data': response.isNotEmpty,
            'count': response.length,
          };
        } catch (e) {
          tests[table] = {
            'status': 'failed',
            'error': e.toString(),
          };
        }
      }

      return {
        'status': 'passed',
        'table_tests': tests,
      };
    } catch (e) {
      return {
        'status': 'failed',
        'error': e.toString(),
      };
    }
  }

  /// Test real-time subscriptions
  Future<Map<String, dynamic>> _testRealtimeSubscriptions() async {
    try {
      final realtimeService = _serviceManager.realtime;
      
      // Test subscription creation (don't actually listen)
      final activeCount = realtimeService.activeSubscriptionCount;
      
      return {
        'status': 'passed',
        'active_subscriptions': activeCount,
        'service_available': true,
      };
    } catch (e) {
      return {
        'status': 'failed',
        'error': e.toString(),
      };
    }
  }

  /// Test storage operations
  Future<Map<String, dynamic>> _testStorageOperations() async {
    try {
      final storageService = _serviceManager.storage;
      final stats = await storageService.getStorageStats();
      
      return {
        'status': 'passed',
        'bucket_stats': stats,
        'service_available': true,
      };
    } catch (e) {
      return {
        'status': 'failed',
        'error': e.toString(),
      };
    }
  }

  /// Test notification service
  Future<Map<String, dynamic>> _testNotificationService() async {
    try {
      final notificationService = _serviceManager.notifications;
      
      // Test getting preferences for a mock user
      final mockUserId = 'test-user-id';
      final preferences = await notificationService.getUserNotificationPreferences(mockUserId);
      
      return {
        'status': 'passed',
        'preferences_loaded': preferences != null,
        'service_available': true,
      };
    } catch (e) {
      return {
        'status': 'failed',
        'error': e.toString(),
      };
    }
  }

  /// Quick health check (for development debugging)
  Future<bool> quickHealthCheck() async {
    if (!kDebugMode) return false;
    
    try {
      final isConnected = SupabaseConfig.isInitialized;
      final servicesReady = _serviceManager.isInitialized;
      
      _logger.d('🏥 Quick Health Check:');
      _logger.d('   Supabase: ${isConnected ? "✅" : "❌"}');
      _logger.d('   Services: ${servicesReady ? "✅" : "❌"}');
      
      return isConnected && servicesReady;
    } catch (e) {
      _logger.e('❌ Health check failed: $e');
      return false;
    }
  }

  /// Log test results in a readable format
  void logTestResults(Map<String, dynamic> results) {
    if (!kDebugMode) return;
    
    _logger.i('📊 Backend Test Results:');
    _logger.i('   Timestamp: ${results['timestamp']}');
    
    final tests = results['tests'] as Map<String, dynamic>;
    tests.forEach((testName, result) {
      final status = result['status'];
      final icon = status == 'passed' ? '✅' : '❌';
      _logger.i('   $icon $testName: $status');
      
      if (status == 'failed' && result['error'] != null) {
        _logger.e('      Error: ${result['error']}');
      }
    });
  }
}
