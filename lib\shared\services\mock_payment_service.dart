import 'package:flutter/material.dart';
import 'package:fit_4_force/core/config/app_config.dart';
import 'package:fit_4_force/shared/services/auth_service.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'base_service.dart';
import 'package:logger/logger.dart';

/// A mock implementation of the payment service
/// This is used temporarily to avoid the flutter_paystack compatibility issues
class MockPaymentService extends BaseService {
  final AuthService _authService;
  final Logger _logger = Logger();
  bool _initialized = false;

  @override
  CollectionReference<Map<String, dynamic>> get collection =>
      firestore.collection('payments');

  MockPaymentService(this._authService) {
    _initializePaymentService();
  }

  Future<void> _initializePaymentService() async {
    if (!_initialized) {
      // No actual initialization needed for the mock service
      _initialized = true;
      _logger.i('Mock payment service initialized');
    }
  }

  Future<bool> processPayment({
    required BuildContext context,
    required String email,
    required String fullName,
    required double amount,
    Function(String)? onSuccess,
    Function(String)? onError,
  }) async {
    try {
      // Generate a unique reference for this transaction
      final reference = _generateReference();

      // Check if context is still valid
      if (!context.mounted) {
        if (onError != null) {
          onError('Context is no longer valid');
        }
        return false;
      }

      // Show a mock payment dialog
      await showDialog(
        context: context,
        barrierDismissible: false,
        builder: (context) => AlertDialog(
          title: const Text('Payment Processing (Mock)'),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Text('Processing payment of ₦$amount'),
              const SizedBox(height: 20),
              const CircularProgressIndicator(),
            ],
          ),
          actions: [
            TextButton(
              onPressed: () {
                Navigator.of(context).pop();
                // Simulate a successful payment
                Future.delayed(const Duration(milliseconds: 500), () async {
                  await _handleSuccessfulPayment(reference);
                  if (onSuccess != null) {
                    onSuccess(reference);
                  }
                });
              },
              child: const Text('Simulate Success'),
            ),
            TextButton(
              onPressed: () {
                Navigator.of(context).pop();
                if (onError != null) {
                  onError('Payment cancelled');
                }
              },
              child: const Text('Cancel'),
            ),
          ],
        ),
      );

      // Always return true since the actual result is handled by the callbacks
      return true;
    } catch (e) {
      _logger.e('Payment error: $e');
      if (onError != null) {
        onError(e.toString());
      }
      return false;
    }
  }

  Future<void> _handleSuccessfulPayment(String reference) async {
    if (currentUserId == null) return;

    try {
      // Set premium status for 30 days
      final expiryDate = DateTime.now().add(const Duration(days: 30));
      await _authService.updatePremiumStatus(currentUserId!, true, expiryDate);

      // Create a payment record
      await collection.add({
        'userId': currentUserId,
        'reference': reference,
        'amount': AppConfig.premiumSubscriptionPrice,
        'status': 'success',
        'type': 'subscription',
        'createdAt': FieldValue.serverTimestamp(),
      });
    } catch (e) {
      _logger.e('Error processing successful payment: $e');
    }
  }

  String _generateReference() {
    return 'MOCK_FIT4FORCE_${DateTime.now().millisecondsSinceEpoch}';
  }

  Future<bool> verifyTransaction(String reference) async {
    try {
      // Check if the transaction exists in our database
      final querySnapshot =
          await collection
              .where('reference', isEqualTo: reference)
              .limit(1)
              .get();

      return querySnapshot.docs.isNotEmpty;
    } catch (e) {
      _logger.e('Error verifying transaction: $e');
      return false;
    }
  }
}
